<?php

namespace App\Http\Controllers;

use App\Models\Form;
use Illuminate\Http\Request;

class FormsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (\Auth::user()->hasRole('admin')) {
            $forms = Form::where('user_id', \Auth::user()->id)
                ->OrWhere('user_id', 0)
                ->orderBy('id', 'desc')->get();
        } else {
            $forms = Form::where('user_id', \Auth::user()->id)
                ->orderBy('id', 'desc')->get();
        }


        return view('admin.forms.index', compact('forms'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Form $forms)
    {
        return view('admin.forms.add', ['forms' => $forms]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'form_name' => 'required',
            'field_type' => 'required|array',
            'field_name' => 'required|array',
        ]);
        $formData = $request->all();
        $i = 0;
        $fieldOptionsCount = 0;
        $constructedFormData = [];
        foreach ($formData['field_type'] as $fieldType) {
            $constructedFormData[$i]['field_type'] = $fieldType;
            $constructedFormData[$i]['field_id'] = (isset($formData['field_id'][$i])) ? $formData['field_id'][$i] : uniqid();
            $constructedFormData[$i]['field_name'] = $formData['field_name'][$i];
            $constructedFormData[$i]['is_required'] = $formData['is_required'][$i];
            if ($formData['field_type'][$i] == 'select' || $formData['field_type'][$i] == 'radio' || $formData['field_type'][$i] == 'checkbox') {
                $fieldOptions = array_values($formData['field_options']);
                $constructedFormData[$i]['field_options'] = $fieldOptions[$fieldOptionsCount];
                $fieldOptionsCount++;
            }
            $i++;
        }
        $formData['form_data'] = json_encode($constructedFormData);
        $formData['user_id'] = (\Auth::user()->hasRole('admin')) ? 0 : \Auth::user()->id;
        \App\Models\Form::manageForm($formData);
        return redirect()->route('forms.index')
            ->with('success', 'Forms created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Form $form)
    {
        return view('admin.forms.add', ['forms' => $form]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
