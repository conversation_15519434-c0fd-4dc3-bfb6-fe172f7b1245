<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Leaders extends Model
{
    use HasFactory;
    protected $table = 'leaders';
    protected $fillable = [
        'name',
        'user_id',
        'category',
        'contact',
        'social_media_links',
        'email',
        'address',
        'bio',
        'status',
        'order',
    ];

    // **********Category-Relation**********
    public function category()
    {
        return $this->belongsTo(LeadersCategory::class, 'category', 'category');
    }
    // **********Category-Relation**********
}
