APP_NAME=TicketsFender
APP_ENV=production
APP_KEY=base64:9UQwXr31XdsFOh3JKLP1+4bLFIPG9WOQr4f4N1ow7GU=
APP_DEBUG=true
APP_URL=https://ticketfender.com

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST="***********"
DB_PORT=
DB_DATABASE="event"
DB_USERNAME="EvePreDeveloper"
DB_PASSWORD="^R9aPF)K9w6c"

SERVER_ADDR=

GCI_CLIENT_SECRET=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
GCI_CLIENT_ID=901431133463-lof76t7g49vc83r4sncdth5ucv8s6gh0.apps.googleusercontent.com
GCI_USER_REDIRECT_URL=https://ticketfender.com/user/social-login-process
GCI_ORGANIZER_REDIRECT_URL=https://ticketfender.com/organizer/social-login-process

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER="smtp"
MAIL_HOST="smtp.gmail.com"
MAIL_PORT="587"
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="sjyi itfc vjmu itmo"
MAIL_ENCRYPTION="TLS"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Ticket Fender"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
SERVER_ADDR=RAVE_PUBLIC_KEY=
RAVE_SECRET_KEY=
SERVER_ADDR=






