<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VolunteeringForm extends Model
{
    use HasFactory;
    protected $table = 'volunteering_forms';
    protected $fillable = [
        'user_id',
        'form_name',
        'field_data'
    ];

    public static function manageForm($formData)
    {
        if (isset($formData['id']) && !empty($formData['id'])) {
            $form = self::find($formData['id']);
        } else {
            $form = new VolunteeringForm();
        }
        $form->user_id = $formData['user_id'];
        $form->form_name = $formData['form_name'];
        $form->field_data = $formData['form_data'];
        $form->save();
    }

    public static function getFormFields($formId)
    {
        $form = self::find($formId);
        $constructedFormFields = [];
        if (isset($form->field_data) && !empty($form->field_data)) {
            $formFieldArray = json_decode($form->field_data, true);
            foreach ($formFieldArray as $formFieldAr) {
                $constructedFormFields[$formFieldAr['field_id']] = $formFieldAr;
            }
        }
        return $constructedFormFields;
    }

    public static function getAllForms()
    {
        return self::all();
    }

    public static function getForm($id)
    {
        return self::find($id);
    }
}
