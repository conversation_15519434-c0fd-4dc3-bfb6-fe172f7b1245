<?php

namespace App\Http\Controllers;

use App\Mail\ApproveOrganizerMail;
use App\Mail\TaxVerifyMail;
use App\Mail\WithdrawMail;
use App\Models\ReferalCommission;
use App\Models\ReferIncomeTransaction;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use App\Mail\TicketBook;
use App\Mail\TicketBookOrg;
use Illuminate\Validation\Rule;
use App\Models\User;
use App\Models\Event;
use Illuminate\Support\Facades\Log;
use App\Models\Review;
use App\Models\Ticket;
use App\Models\OrderTax;
use App\Models\AppUser;
use App\Models\Category;
use App\Models\Coupon;
use App\Models\CouponUsageHistory;
use App\Models\Order;
use App\Models\Setting;
use App\Models\Tax;
use App\Models\OrganizerPaymentKeys;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\File;
use App\Models\Language;
use App\Models\Module;
use App\Models\Notification;
use App\Models\NotificationTemplate;
use App\Models\OrderChild;
use App\Models\Settlement;
use Illuminate\Support\Facades\Rave;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Artesaos\SEOTools\Facades\JsonLdMulti;
use Artesaos\SEOTools\Facades\SEOTools;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\JsonLd;
use Exception;
use Facade\FlareClient\View;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

use function GuzzleHttp\Promise\all;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;
use Stripe\Stripe;
use Throwable;
use Illuminate\Support\Str;
use Carbon\CarbonPeriod;
use App\Models\Directory;

class UserController extends Controller
{
    public function __construct()
    {
        (new AppHelper)->eventStatusChange();
    }

    public function saveOrgId(Request $request)
    {
        $this->validate($request, [
            'org_id' => 'required'
        ]);
        $user = \Auth::user();
        $directory = Directory::find($request->org_id);
        if ($directory) {
            $contactInfo = json_decode($directory->contact_informations, true);
            $domain = "";
            if (isset($contactInfo['website']) && !empty($contactInfo['website'])) {
                $websiteArray = parse_url($contactInfo['website']);
                if (isset($websiteArray['host']) && !empty($websiteArray['host'])) {
                    $domain = str_replace('www.', '', $websiteArray['host']);
                }
            }
            $emailArray = explode('@', $request->personal_email);
            if (
                (isset($emailArray[1]) && !empty($emailArray[1]) &&
                    $emailArray[1] != $domain) || (!isset($emailArray[1]) || empty($emailArray[1]))
            ) {
                exit('Your email doesnt matched with organizer website');
            } else {
                $user->org_id = $request->org_id;
                $user->organization_status = 1;
                $user->status = 1;
                $user->save();
            }
            exit('Done');
        } else {
            exit('Invalid Organization');
        }
    }

    public function approveOrganizers(User $user)
    {
        if ($user) {
            $settings = json_decode($user->settings, true);
            $directory = "";
            if (isset($settings['org_id'])) {
                $user->org_id = $settings['org_id'];
                $directory = Directory::find($settings['org_id']);
            }
            $user->status = 1;
            $user->organization_status = 1;
            $user->settings = "";
            $user->save();
            $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                $q->whereIn("name", ["admin"]);
            })->pluck('email')->toArray();
            if (!empty($adminUsers)) {
                \Mail::to($adminUsers)->send(new ApproveOrganizerMail('admin', $user));
            }
            \Mail::to($user->email)->send(new ApproveOrganizerMail('organizer', $user));
            if (!empty($directory) && isset($directory->personal_email) && !empty($directory->personal_email)) {
                \Mail::to($directory->personal_email)->send(new ApproveOrganizerMail('organization', $user));
            }
        }

        return redirect()->back()->withStatus(__('Organizer has been successfully approved.'));
    }

    public function pendingOrganizers()
    {
        $users = User::with("roles")->whereHas("roles", function ($q) {
            $q->whereIn("name", ["Organizer"]);
        })->where('organization_status', 0)->where('org_id', '!=', 0)->get();
        // ->where('org_id', 0)->where('settings', 'like', '%org_name%')->get();
        return view('admin.user.pending-organizers', compact('users'));
    }

    public function taxformView(User $user)
    {
        $taxFormDetails = json_decode($user->finance_details, true);
        $user_id = $user->id;
        $status = $user->finance_status;
        $reason = $user->finance_decline_reason;
        return view('admin.view-tax-forms', compact('taxFormDetails', 'user_id', 'status', 'reason'));
    }

    public function taxForms()
    {
        if (Auth::user()->hasRole('admin')) {
            $taxFormUsers = User::where('finance_status', '!=', 0)->get();
            return view('admin.tax-forms', compact('taxFormUsers'));
        } else {
            return abort(403, 'Not Allowed');
        }
    }

    public function approveFinanceDetails(Request $request)
    {
        $user = Auth::user();
        if ($user->hasRole('admin')) {
            $user = User::find($request->user_id);
            $request->validate([
                'residence' => 'bail|required',
                'organization_type' => 'bail|required',
                'indi_first_name' => 'bail|required_if:organization_type,individual',
                'indi_last_name' => 'bail|required_if:organization_type,individual',
                'date_of_birth' => 'bail|required_if:organization_type,individual',
                'ssn' => 'bail|required_if:organization_type,individual',
                'company_name' => 'bail|required_if:organization_type,company',
                'company_tax_id' => 'bail|required_if:organization_type,company',
                // 'company_website' => 'bail|required_if:organization_type,company',
                'non_profit_name' => 'bail|required_if:organization_type,nonprofit',
                'non_profit_tax_id' => 'bail|required_if:organization_type,nonprofit',
                'website_or_description' => 'bail|required',
                'website' => 'bail|required_if:website_or_description,website|url',
                'description' => 'bail|required_if:website_or_description,description',
                'bank_country' => 'bail|required',
                'bank_currency' => 'bail|required',
                'method' => 'bail|required',
                'ac_hld_inf_type' => 'bail|required',
                'ac_hld_company_name' => 'bail|required_if:ac_hld_inf_type,company',
                'ac_hld_first_name' => 'bail|required_if:ac_hld_inf_type,individual',
                'ac_hld_last_name' => 'bail|required_if:ac_hld_inf_type,individual',
                'ac_hld_address_1' => 'bail|required',
                'ac_hld_city' => 'bail|required',
                'ac_hld_postal_code' => 'bail|required',
                'ac_hld_states' => 'bail|required',
                'account_type' => 'bail|required',
                'bank_name' => 'bail|required',
                'routing_number' => 'bail|required',
                'account_number' => 'bail|required',
                're_account_number' => 'bail|required|same:account_number',
                'account_nick_name' => 'bail|required',
                'user_id' => 'bail|required',
            ]);
            if ($request->organization_type == 'company') {
                $request->validate([
                    'company_website' => 'url',
                ]);
            }
            if (!empty($request->reject_reason)) {
                $user->finance_status = 3;
                $user->finance_decline_reason = $request->reject_reason;
            } else {
                $user->finance_status = 1;
                $user->finance_decline_reason = "";
            }
            $user->finance_details = json_encode($request->all());
            $user->save();
            Mail::to($user->email)->send(new TaxVerifyMail($user, 'organizer', 'admin-action'));
            $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                $q->whereIn("name", ["admin"]);
            })->pluck('email')->toArray();
            Mail::to($adminUsers)->send(new TaxVerifyMail($user, 'admin', 'admin-action'));
            return response()->json(['success' => 1]);
        } else {
            return abort(403, 'Not Allowed');
        }
    }

    public function saveFinanceDetails(Request $request)
    {
        $user = Auth::user();
        if ($user->hasRole('Organizer')) {
            if ($user->finance_status == '0' || $user->finance_status == '3') {
                $request->validate([
                    'residence' => 'bail|required',
                    'organization_type' => 'bail|required',
                    'indi_first_name' => 'bail|required_if:organization_type,individual',
                    'indi_last_name' => 'bail|required_if:organization_type,individual',
                    'date_of_birth' => 'bail|required_if:organization_type,individual',
                    'ssn' => 'bail|required_if:organization_type,individual',
                    'company_name' => 'bail|required_if:organization_type,company',
                    'company_tax_id' => 'bail|required_if:organization_type,company',
                    // 'company_website' => 'bail|required_if:organization_type,company',
                    'non_profit_name' => 'bail|required_if:organization_type,nonprofit',
                    'non_profit_tax_id' => 'bail|required_if:organization_type,nonprofit',
                    'website_or_description' => 'bail|required',
                    'website' => 'bail|required_if:website_or_description,website|url',
                    'description' => 'bail|required_if:website_or_description,description',
                    'bank_country' => 'bail|required',
                    'bank_currency' => 'bail|required',
                    'method' => 'bail|required',
                    'ac_hld_inf_type' => 'bail|required',
                    'ac_hld_company_name' => 'bail|required_if:ac_hld_inf_type,company',
                    'ac_hld_first_name' => 'bail|required_if:ac_hld_inf_type,individual',
                    'ac_hld_last_name' => 'bail|required_if:ac_hld_inf_type,individual',
                    'ac_hld_address_1' => 'bail|required',
                    'ac_hld_city' => 'bail|required',
                    'ac_hld_postal_code' => 'bail|required',
                    'ac_hld_states' => 'bail|required',
                    'account_type' => 'bail|required',
                    'bank_name' => 'bail|required',
                    'routing_number' => 'bail|required',
                    'account_number' => 'bail|required',
                    're_account_number' => 'bail|required|same:account_number',
                    'account_nick_name' => 'bail|required',
                ]);
                if ($request->organization_type == 'company') {
                    $request->validate([
                        'company_website' => 'url',
                    ]);
                }
                $user->finance_status = 2;
                $user->finance_details = json_encode($request->all());
                $user->finance_decline_reason = "";
                $user->save();
                Mail::to($user->email)->send(new TaxVerifyMail($user, 'organizer'));
                $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                    $q->whereIn("name", ["admin"]);
                })->pluck('email')->toArray();
                Mail::to($adminUsers)->send(new TaxVerifyMail($user));
                return response()->json(['success' => 1]);
            } else {
                return response()->json(['error' => 1, 'message' => 'you already submitted the form']);
            }
        } else {
            return abort(403, 'Not Allowed');
        }
    }

    public function marketing()
    {
        if (Auth::user()->hasRole('Organizer')) {
            $events = Event::with(['category:id,name'])
                ->where([
                    // ['status', 1],
                    ['user_id', Auth::user()->id],
                    ['is_deleted', 0],
                    ['event_status', 'Pending'],
                    // [
                    //     'end_time',
                    //     '>',
                    //     $date->format('Y-m-d H:i:s')
                    // ]
                ])->orderBy('start_time', 'ASC')->get();
            $debugMode = env('APP_DEBUG');
            return view('admin.marketing', compact('events', 'debugMode'));
        } else {
            return abort(403, 'Not Allowed');
        }
    }

    public function index()
    {
        abort_if(Gate::denies('user_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $users = User::with(['roles:id,name', 'organizationName:id,name'])->get();
        $debugMode = env('APP_DEBUG');
        return view('admin.user.index', compact('users', 'debugMode'));
    }

    public function create()
    {
        abort_if(Gate::denies('user_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $roles = Role::all();
        // $orgs = User::role('Organizer')->orderBy('id', 'DESC')->get();
        $directories = Directory::where('status', 1)->get()->toArray();
        return view('admin.user.create', compact('roles', 'directories'));
    }

    public function store(Request $request)
    {

        $request->validate([
            'first_name' => 'bail|required',
            'last_name' => 'bail|required',
            'email' => 'bail|required|email|unique:users',
            'phone' => 'bail|required',
            'password' => 'bail|required|min:6',
            "roles" => "bail|required|array|min:1",
            'roles.*' => 'bail|required|string|distinct|min:1',
        ]);
        $data = $request->all();
        if ($request->has('roles') && in_array(2, $request->roles)) {
            // $request->validate([
            //     'org_name' => 'required|unique:users,name'
            // ]);
            // $data['name'] = $request->org_name;
            $data['org_id'] = $request->organization;
        }
        $data['password'] = Hash::make($request->password);
        $data['language'] = Setting::first()->language;
        $user = User::create($data);
        $user->assignRole($request->input('roles', []));
        $roles = Role::where('id', $request->roles[0])->first()->name;
        if ($roles == "Organizer") {
            OrganizerPaymentKeys::create([
                'organizer_id' => $user->id,
            ]);
        }
        return redirect()->route('users.index')->withStatus(__('User has added successfully.'));
    }

    public function show(User $user)
    {
        return view('admin.user.show', compact('user'));
    }

    public function edit(User $user)
    {
        abort_if(Gate::denies('user_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $roles = Role::all();
        if ($user->hasRole('admin')) {
            return redirect()->route('users.index')->withStatus(__('You can not edit admin.'));
        }
        // $orgs = User::role('Organizer')->orderBy('id', 'DESC')->get();
        $directories = Directory::where('status', 1)->get()->toArray();
        return view('admin.user.edit', compact('roles', 'user', 'directories'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'first_name' => 'bail|required',
            'last_name' => 'bail|required',
            'phone' => 'bail|required',
            'email' => 'bail|required|unique:users,email,' . $user->id . ',id',
        ]);
        if ($request->has('roles') && in_array(2, $request->roles)) {
            // $request->validate([
            //     'org_name' => 'required'
            // ]);
            // $data['name'] = $request->org_name;
            $data['org_id'] = $request->organization;
        }
        $data['first_name'] = $request->first_name;
        $data['last_name'] = $request->last_name;
        $data['email'] = $request->email;
        $data['phone'] = $request->phone;
        $data['bio'] = $request->bio;
        $data['allow_referal'] = $request->allow_referal;
        if ($request->hasFile('image')) {
            $request->validate([
                'image' => 'required|mimes:jpeg,png,jpg,gif,svg|max:3048',
            ]);
            if ($user->image != "defaultuser.png") {
                Storage::delete('/images/upload' . $user->image);
            }
            $data['image'] = (new AppHelper)->saveImage($request);
        }
        $user->update($data);
        $user->syncRoles($request->input('roles', []));

        return redirect()->route('users.index')->withStatus(__('User has updated successfully.'));
    }

    public function bookTicket(Request $request)
    {

        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'))
            ->setDescription('This is all events page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'all event page',
                $setting->app_name,
                $setting->app_name . ' All-Events',
                'events page',
                $setting->app_name . ' Events',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'))
            ->setDescription('This is all events page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is all events page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'));
        SEOTools::setDescription('This is all events page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            'all event page',
            $setting->app_name,
            $setting->app_name . ' All-Events',
            'events page',
            $setting->app_name . ' Events',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);

        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $events = Event::with(['category:id,name'])
            ->where([['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d H:i:s')]]);
        $chip = array();
        if ($request->has('type') && $request->type != null) {
            $chip['type'] = $request->type;
            $events = $events->where('type', $request->type);
        }
        if ($request->has('category') && $request->category != null) {
            $chip['category'] = Category::find($request->category)->name;
            $events = $events->where('category_id', $request->category);
        }
        if ($request->has('duration') && $request->duration != null) {
            $chip['date'] = $request->duration;
            if ($request->duration == 'Today') {
                $temp = Carbon::now($timezone)->format('Y-m-d');
                $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
            } else if ($request->duration == 'Tomorrow') {
                $temp = Carbon::tomorrow($timezone)->format('Y-m-d');
                $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
            } else if ($request->duration == 'ThisWeek') {
                $now = Carbon::now($timezone);
                $weekStartDate = $now->startOfWeek()->format('Y-m-d H:i:s');
                $weekEndDate = $now->endOfWeek()->format('Y-m-d H:i:s');
                $events = $events->whereBetween('start_time', [$weekStartDate, $weekEndDate]);
            } else if ($request->duration == 'date') {
                if (isset($request->date)) {
                    $temp = Carbon::parse($request->date)->format('Y-m-d H:i:s');
                    $events = $events->whereBetween('start_time', [$request->date . ' 00:00:00', $request->date . ' 23:59:59']);
                }
            }
        }
        $events = $events->orderBy('start_time', 'ASC')->get();

        foreach ($events as $value) {
            $value->total_ticket = Ticket::where([['event_id', $value->id], ['is_deleted', 0], ['status', 1]])->sum('quantity');
            $value->sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->available_ticket = $value->total_ticket - $value->sold_ticket;
        }
        return view('admin.org_bookTicket', compact('events', 'chip'));
    }

    public function organizerEventDetails(Request $request, $id)
    {
        $tickets = Ticket::all()->where('event_id', $id);
        return view('admin.organizer.organizerBookTicket', compact('tickets'));
    }

    public function organizerCheckout(Request $request, $id)
    {
        $data = Ticket::find($id);
        $data->user = AppUser::all();
        $data->event = Event::find($data->event_id);
        $setting = Setting::first(['app_name', 'logo']);


        SEOMeta::setTitle($data->name)
            ->setDescription($data->description)
            ->addKeyword([
                $setting->app_name,
                $data->name,
                $data->event->name,
                $data->event->tags
            ]);

        OpenGraph::setTitle($data->name)
            ->setDescription($data->description)
            ->setUrl(url()->current());

        JsonLd::setTitle($data->name)
            ->setDescription($data->description);

        SEOTools::setTitle($data->name);
        SEOTools::setDescription($data->description);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $data->name,
            $data->event->name,
            $data->event->tags
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        $arr = [];
        $used = Order::where('ticket_id', $id)->sum('quantity');
        $data->available_qty = $data->quantity - $used;
        $data->tax = Tax::where([['allow_all_bill', 1], ['status', 1]])->orderBy('id', 'DESC')->get()->makeHidden(['created_at', 'updated_at']);
        foreach ($data->tax as $key => $item) {
            if ($item->amount_type == 'percentage') {

                $amount = ($item->price * $data->price) / 100;
                array_push($arr, $amount);
            }
            if ($item->amount_type == 'price') {
                $amount = $item->price;
                array_push($arr, $amount);
            }
        }
        $data->tax_total = array_sum($arr);
        // $data->tax = Tax::where([['user_id', $data->event->user_id], ['allow_all_bill', 1], ['status', 1]])->orderBy('id', 'DESC')->get()->makeHidden(['created_at', 'updated_at']);
        // $data->tax_total = intval(Tax::where([['user_id', $data->event->user_id], ['allow_all_bill', 1], ['status', 1]])->sum('price'));
        $data->currency_code = Setting::find(1)->currency;
        $seat = '';
        $orders = Order::where('event_id', $data->event['id'])->get();
        return view('admin.organizer.organizerCheckout', compact('data'));
    }
    public function organizerCreateOrder(Request $request)
    {
        $data = $request->all();
        $ticket = Ticket::find($request->ticket_id);
        $event = Event::find($ticket->event_id);
        $org = User::find($event->user_id);
        $user = $request->user;
        $data['order_id'] = '#' . rand(9999, 100000);
        $data['event_id'] = $event->id;
        $data['customer_id'] = $user;
        $data['organization_id'] = $org->id;
        $data['order_status'] = 'Pending';

        $order = Order::create($data);
        if (isset($request->tax_data)) {
            foreach (json_decode($data['tax_data']) as $value) {
                $tax['order_id'] = $order->id;
                $tax['tax_id'] = $value->id;
                $tax['price'] = $value->price;
                OrderTax::create($tax);
            }
        }
        return redirect('orders');
    }
    public function adminDashboard(Request $request)
    {
        if (\Auth::user()->hasRole('Organizer')) {
            return redirect(url('organization/home'));
        }
        $master['organizations'] = User::role('Organizer')->count();
        $master['users'] = AppUser::count();
        $master['total_order'] = Order::count();
        $master['pending_order'] = Order::where('order_status', 'Pending')->count();
        $master['complete_order'] = Order::where('order_status', 'Complete')->count();
        $master['cancel_order'] = Order::where('order_status', 'Cancel')->count();
        $master['eventDate'] = array();

        $events = Event::where([['status', 1], ['is_deleted', 0]])->orderBy('id', 'DESC')->get();
        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $events = Event::with(['category:id,name'])
            ->where([['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d H:i:s')]]);
        $chip = array();
        if ($request->has('type') && $request->type != null) {
            $chip['type'] = $request->type;
            $events = $events->where('type', $request->type);
        }
        if ($request->has('category') && $request->category != null) {
            $chip['category'] = Category::find($request->category)->name;
            $events = $events->where('category_id', $request->category);
        }
        if ($request->has('duration') && $request->duration != null) {
            $chip['date'] = $request->duration;
            if ($request->duration == 'Today') {
                $temp = Carbon::now($timezone)->format('Y-m-d');
                $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
            } else if ($request->duration == 'Tomorrow') {
                $temp = Carbon::tomorrow($timezone)->format('Y-m-d');
                $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
            } else if ($request->duration == 'ThisWeek') {
                $now = Carbon::now($timezone);
                $weekStartDate = $now->startOfWeek()->format('Y-m-d H:i:s');
                $weekEndDate = $now->endOfWeek()->format('Y-m-d H:i:s');
                $events = $events->whereBetween('start_time', [$weekStartDate, $weekEndDate]);
            } else if ($request->duration == 'date') {
                if (isset($request->date)) {
                    $temp = Carbon::parse($request->date)->format('Y-m-d H:i:s');
                    $events = $events->whereBetween('start_time', [$request->date . ' 00:00:00', $request->date . ' 23:59:59']);
                }
            }
        }
        $events = $events->orderBy('start_time', 'ASC')->get();

        $day = Carbon::parse(Carbon::now()->year . '-' . Carbon::now()->month . '-01')->daysInMonth;
        $monthEvent = Event::whereBetween('start_time', [date('Y') . "-" . date('m') . "-01 00:00:00", date('Y') . "-" . date('m') . "-" . $day . " 23:59:59"])
            ->where([['status', 1], ['is_deleted', 0]])
            ->orderBy('id', 'DESC')->get();

        foreach ($monthEvent as $value) {
            $value->tickets = Ticket::where('event_id', $value->id)->sum('quantity');
            $value->sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->average = $value->tickets == 0 ? 0 : $value->sold_ticket * 100 / $value->tickets;
        }
        foreach ($events as $value) {
            $tickets = Ticket::where('event_id', $value->id)->sum('quantity');
            $sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->avaliable = $tickets - $sold_ticket;
            array_push($master['eventDate'], $value->start_time->format('Y-m-d'));
        }
        return view('admin.dashboard', compact('events', 'monthEvent', 'master'));
    }

    public function refererIcome(Request $request)
    {
        $filters = [];
        if ($request->has('organizer')) {
            $filters = $request->except('_token');
        }
        $user = \Auth::user();
        if ($user->hasRole('Organizer') && $user->finance_status != '1') {
            return redirect("organization/income");
        }
        // if ($user->allow_referal != '1') {
        //     abort(Response::HTTP_FORBIDDEN, 'Not Allowed to Access');
        // }
        $incomes = ReferalCommission::where('commission_to', $user->id);
        if (!empty($filters)) {
            $incomes = $incomes->where('commission_from', $filters['organizer']);//->where('status', $filters['status']);
        }
        $incomes = $incomes->orderBy('created_at')->get();
        $organizations = ReferalCommission::where('commission_to', $user->id);
        $totalAmount = $organizations->sum('amount');
        // $availableToWithdraw = $organizations->where('status', 1)->sum('amount');
        $withdrawnAmount = ReferIncomeTransaction::where('user_id', $user->id)->where('status', '1')->sum('amount');
        $lockedAmount = ReferIncomeTransaction::where('user_id', $user->id)->where('status', '0')->sum('amount');
        $organizations = $organizations->groupBy('commission_from')->get();
        $availableToWithdraw = ($totalAmount - $lockedAmount - $withdrawnAmount);
        $orgs = [];
        $referedOrganizations = User::where('refered_by', $user->id)->get();
        foreach ($referedOrganizations as $referedOrganization) {
            $orgs[$referedOrganization->id] = $referedOrganization->first_name . " " . $referedOrganization->last_name . " (" . $referedOrganization->email . ")";
        }
        return view(
            'admin.referer.index',
            compact(
                'user',
                'orgs',
                'incomes',
                'request',
                'totalAmount',
                'availableToWithdraw',
                'lockedAmount',
            )
        );
    }

    public function withdrawReferIncome(Request $request)
    {
        $user = \Auth::user();
        if ($user->hasRole('organizer') && $user->finance_status != '1') {
            return redirect("organization/income");
        }
        // if ($user->allow_referal != '1') {
        //     abort(Response::HTTP_FORBIDDEN, 'Not Allowed to Access');
        // }
        $totalAmount = ReferalCommission::where('commission_to', $user->id)->sum('amount');
        $withdrawnAmount = ReferIncomeTransaction::where('user_id', $user->id)->sum('amount');
        $balanceAmount = ($totalAmount - $withdrawnAmount);
        if ($balanceAmount < 50) {
            return redirect('organization/refer-income');
        }
        return view('admin.referer.withdraw', compact('user', 'balanceAmount'));
    }

    public function approveTransaction(Request $request, ReferIncomeTransaction $transaction)
    {

        $user = \Auth::user();
        if ($user->hasRole('admin')) {
            $withdrawingAmount = $transaction->amount;
            $totalAmount = ReferalCommission::where('commission_to', $transaction->user_id)->sum('amount');
            $withdrawnAmount = ReferIncomeTransaction::where('user_id', $transaction->user_id)
                ->where('status', 1)->sum('amount');
            $balanceAmount = ($totalAmount - $withdrawnAmount);
            if ($withdrawingAmount <= $balanceAmount) {
                $transaction->status = 1;
                $transaction->approved_by = $user->id;
                $transaction->save();
                $email = $transaction->organization->email;
                Mail::to($email)->send(new WithdrawMail('admin', $transaction));
                return redirect('admin/referer-transactions')->withStatus('Successfully Updated.');
            }
            abort(Response::HTTP_FORBIDDEN, 'Withdrawal amount exceeded the balance amount');
        } else {
            abort(Response::HTTP_FORBIDDEN, 'Invalid Access');
        }
    }

    public function declineTransaction(Request $request, ReferIncomeTransaction $transaction)
    {
        if (!$request->has('reason') || empty($request->has('reason'))) {
            abort(Response::HTTP_FORBIDDEN, 'Rejected Reason not found');
        }
        $user = \Auth::user();
        if ($user->hasRole('admin')) {
            $transaction->status = 2;
            $transaction->reason = $request->reason;
            $transaction->rejected_by = $user->id;
            $transaction->save();
            $email = $transaction->organization->email;
            Mail::to($email)->send(new WithdrawMail('admin', $transaction));
            return redirect('admin/referer-transactions')->withStatus('Successfully Updated.');
        } else {
            abort(Response::HTTP_FORBIDDEN, 'Invalid Access');
        }
    }

    public function Refertransactions()
    {
        $user = \Auth::user();
        if ($user->hasRole('admin')) {
            $refTransactions = ReferIncomeTransaction::get();
        } else {
            if ($user->hasRole('Organizer') && $user->finance_status != '1') {
                return redirect("organization/income");
            }
            $refTransactions = ReferIncomeTransaction::where('user_id', $user->id)->get();
        }
        return view('admin.referer.transactions', compact('user', 'refTransactions'));
    }

    public function withdrawReferIncomeProcess(Request $request)
    {
        $request->validate([
            'amount' => 'required',
        ]);

        $user = \Auth::user();
        if ($user->hasRole('Organizer') && $user->finance_status != '1') {
            return redirect("organization/income");
        }
        $totalAmount = ReferalCommission::where('commission_to', $user->id)->sum('amount');
        $withdrawnAmount = ReferIncomeTransaction::where('user_id', $user->id)->sum('amount');
        $balanceAmount = ($totalAmount - $withdrawnAmount);

        if ($balanceAmount < 50) {
            return redirect('organization/refer-income');
        }
        if ($request->amount < $balanceAmount) {
            $transaction = ReferIncomeTransaction::create([
                'amount' => $request->amount,
                'note' => $request->note,
                'user_id' => $user->id,
            ]);
            try {
                $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                    $q->whereIn("name", ["admin"]);
                })->pluck('email')->toArray();
                Mail::to($adminUsers)->send(new WithdrawMail('organizer', $transaction));
            } catch (\Throwable $th) {
                Log::info($th->getMessage());
            }
            return redirect('organization/refer-income/withdraw')->withStatus(__('Withdrawal request successful. Once approved by the admin, you will receive your funds.'));
        } else {
            return redirect('organization/refer-income/withdraw')->withErrors(['error' => __('You have exceeded the withdrawable amount')]);
        }
    }

    public function referFriend()
    {
        $user = \Auth::user();
        // if ($user->allow_referal != '1') {
        //     abort(Response::HTTP_FORBIDDEN, 'Not Allowed to Access');
        // }
        if (!isset($user->referal_code)) {
            do {
                $ref_code = Str::upper(Str::random(10));
                $checkRefCode = User::where('referal_code', $ref_code)->first();
            } while ($checkRefCode);

            $user->referal_code = $ref_code;
            $user->save();
        }
        return view('admin.refer_friend', compact('user'));
    }

    public function saveReferSettings(Request $request)
    {
        $user = \Auth::user();
        $user->referal_settings = $request->except('_token');
        $user->save();
        return redirect('organizer-setting')->withStatus(__('Setting saved successfully.'));
    }


    public function organizationDashboard(Request $request)
    {
        $master['total_tickets'] = Ticket::where('user_id', Auth::user()->id)->sum('quantity');
        $master['used_tickets'] = Order::where('organization_id', Auth::user()->id)->sum('quantity');
        $master['events'] = Event::where([['user_id', Auth::user()->id], ['is_deleted', 0]])->count();
        $master['total_order'] = Order::where('organization_id', Auth::user()->id)->count();
        $master['pending_order'] = Order::where([['order_status', 'Pending'], ['organization_id', Auth::user()->id]])->count();
        $master['complete_order'] = Order::where([['order_status', 'Complete'], ['organization_id', Auth::user()->id]])->count();
        $master['cancel_order'] = Order::where([['order_status', 'Cancel'], ['organization_id', Auth::user()->id]])->count();

        // $user = \Auth::user();
        // if (!isset($user->org_id) || empty($user->org_id)) {
        //     return redirect('organizer-setting')->withStatus(__('Setting saved successfully.'));
        // }
        // dd($user);
        $master['cancel_order'] = Order::where([['order_status', 'Cancel'], ['organization_id', Auth::user()->id]])->count();

        $day = Carbon::parse(Carbon::now()->year . '-' . Carbon::now()->month . '-01')->daysInMonth;
        $monthEvent = Event::whereBetween('start_time', [date('Y') . "-" . date('m') . "-01 00:00:00", date('Y') . "-" . date('m') . "-" . $day . " 23:59:59"])
            ->where([['status', 1], ['user_id', Auth::user()->id], ['is_deleted', 0]])
            ->orderBy('id', 'DESC')->get();

        foreach ($monthEvent as $value) {
            $value->tickets = Ticket::where('event_id', $value->id)->sum('quantity');
            $value->sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->average = $value->tickets == 0 ? 0 : $value->sold_ticket * 100 / $value->tickets;
        }

        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $events = Event::with(['category:id,name'])
            ->where([['status', 1], ['user_id', Auth::user()->id], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d H:i:s')]]);
        $chip = array();
        if ($request->has('type') && $request->type != null) {
            $chip['type'] = $request->type;
            $events = $events->where('type', $request->type);
        }
        if ($request->has('category') && $request->category != null) {
            $chip['category'] = Category::find($request->category)->name;
            $events = $events->where('category_id', $request->category);
        }
        if ($request->has('duration') && $request->duration != null) {
            $chip['date'] = $request->duration;
            if ($request->duration == 'Today') {
                $temp = Carbon::now($timezone)->format('Y-m-d');
                $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
            } else if ($request->duration == 'Tomorrow') {
                $temp = Carbon::tomorrow($timezone)->format('Y-m-d');
                $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
            } else if ($request->duration == 'ThisWeek') {
                $now = Carbon::now($timezone);
                $weekStartDate = $now->startOfWeek()->format('Y-m-d H:i:s');
                $weekEndDate = $now->endOfWeek()->format('Y-m-d H:i:s');
                $events = $events->whereBetween('start_time', [$weekStartDate, $weekEndDate]);
            } else if ($request->duration == 'date') {
                if (isset($request->date)) {
                    $temp = Carbon::parse($request->date)->format('Y-m-d H:i:s');
                    $events = $events->whereBetween('start_time', [$request->date . ' 00:00:00', $request->date . ' 23:59:59']);
                }
            }
        }
        $events = $events->orderBy('start_time', 'ASC')->get();

        $master['eventDate'] = array();
        foreach ($events as $value) {
            $tickets = Ticket::where('event_id', $value->id)->sum('quantity');
            $sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->avaliable = $tickets - $sold_ticket;
            array_push($master['eventDate'], $value->start_time->format('Y-m-d'));
        }
        return view('admin.org_dashboard', compact('events', 'monthEvent', 'master'));
    }

    public function viewProfile()
    {
        $languages = Language::where('status', 1)->get();
        $directories = Directory::where('status', 1)->get();
        return view('admin.profile', compact('languages', 'directories'));
    }

    public function editProfile(Request $request)
    {
        $data = $request->all();
        $data['is_profile_completed'] = 1;
        if ($request->hasFile('image')) {
            $request->validate([
                'image' => 'required|mimes:jpeg,png,jpg,gif,svg|max:3048',
            ]);
            $user = User::find(Auth::user()->id);
            if ($user->image != "defaultuser.png") {
                Storage::delete('/images/upload' . $user->image);
            }
            $data['image'] = (new AppHelper)->saveImage($request);
        }
        User::find(Auth::user()->id)->update($data);

        if (session()->get('locale') != $request->language) {
            App::setLocale($request->language);
            session()->put('locale', $request->language);
            $direction = Language::where('name', $request->language)->first()->direction;
            session()->put('direction', $direction);
        }
        return redirect('profile')->withStatus(__('Profile has updated successfully.'));
    }

    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'bail|required',
            'password' => 'bail|required|min:6',
            'confirm_password' => 'bail|required|same:password|min:6'
        ]);

        if (Hash::check($request->current_password, Auth::user()->password)) {
            User::find(Auth::user()->id)->update(['password' => Hash::make($request->password)]);
            return redirect('profile')->withStatus(__('Password has updated successfully.'));
        } else {
            return Redirect::back()->with('error_msg', 'Current Password is wrong!');
        }
    }



    public function makePayment($id)
    {
        $order = Order::with(['customer'])->find($id);
        return view('createPayment', compact('order'));
    }

    public function transction_verify(Request $request, $order_id)
    {
        $order = Order::find($order_id);
        $id = $request->input('transaction_id');
        if ($request->input('status') == 'successful') {
            $order->payment_token = $id;
            $order->payment_status = 1;
            $order->save();
            return view('transction_verify');
        } else {
            return view('cancel');
        }
    }


    public function changeLanguage($lang)
    {
        App::setLocale($lang);
        session()->put('locale', $lang);
        $dir = Language::where('name', $lang)->first()->direction;
        session()->put('direction', $dir);
        return redirect()->back();
    }

    public function scanner()
    {
        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        if (Auth::user()->hasRole('admin')) {
            $scanners = User::role('scanner')->orderBy('id', 'DESC')->get();
            $events = Event::where([['status', 1], ['is_deleted', 0], ['end_time', '>', $date->format('Y-m-d H:i:s')]])->get();
        } else {
            $scanners = User::role('scanner')->where('org_id', Auth::user()->id)->orderBy('id', 'DESC')->get();
            $events = Event::where([['status', 1], ['is_deleted', 0], ['end_time', '>', $date->format('Y-m-d H:i:s')], ['user_id', Auth::user()->id]])->get();
        }
        foreach ($scanners as $value) {
            $value->total_event = 0;
            foreach ($events as $key => $event) {
                if (!str_contains($event->scanner_id, $value->id)) {
                    if (preg_match("/\b$value->id\b/", $event->scanner_id)) {
                        $value->total_event += 1;
                    }
                }

            }
        }
        return view('admin.scanner.index', compact('scanners'));
    }

    public function scannerCreate()
    {
        return view('admin.scanner.create');
    }

    public function addScanner(Request $request)
    {
        $request->validate([
            'first_name' => 'bail|required',
            'last_name' => 'bail|required',
            'email' => 'bail|required|email|unique:users',
            'phone' => 'bail|required',
            'password' => 'bail|required|min:6',
        ]);
        $data = $request->all();
        $data['org_id'] = Auth::user()->id;
        $data['password'] = Hash::make($request->password);
        $data['language'] = Setting::first()->language;
        $user = User::create($data);
        $user->assignRole('scanner');
        return redirect('scanner')->withStatus(__('Scanner is added successfully.'));
    }

    public function blockScanner($id)
    {
        $user = User::find($id);
        $user->status = $user->status == "1" ? "0" : "1";
        $user->save();
        return redirect('scanner')->withStatus(__('User status changed successfully.'));
    }

    public function getScanner($id)
    {
        $data = User::where('org_id', $id)->orderBy('id', 'DESC')->get();
        return response()->json(['data' => $data, 'success' => true], 200);
    }

    public function main_user_block($id)
    {
        $event = Event::where('user_id', $id)->get();
        foreach ($event as $item) {

            if ($item->end_time >= Carbon::now()) {
                return redirect('users')->withstatusblock(__("Please turn off all the ongoing events of the user before blocking"));
            }
        }
        $user = User::find($id);
        $user->status = $user->status == "1" ? "0" : "1";
        $user->save();
        return redirect('users')->withStatus(__('User status changed successfully.'));
    }

    public function check_email(Request $request)
    {
        $data = '';
        $setting = Setting::find(1);
        try {
            $config = array(
                'driver' => $setting->mail_mailer,
                'host' => $setting->mail_host,
                'port' => $setting->mail_port,
                'encryption' => $setting->mail_encryption,
                'username' => $setting->mail_username,
                'password' => $setting->mail_password
            );
            Config::set('mail', $config);
            Mail::send(
                'emails.check_email',
                ['data' => $data],
                function ($message) use ($request, $setting) {
                    $message->from($setting->sender_email);
                    $message->to($request->input('mail_to'));
                    $message->subject('this mail is just to check configure');
                }
            );

            return response()->json(['message' => 'Email sent successfully', 'data' => $request->mail_to, 'success' => true]);
        } catch (Exception $e) {
            $error = $e->getMessage();
            return response()->json(['message' => 'Failed to sent Email', 'data' => $error, 'success' => false]);
        }
    }
    public function editAppUser($id)
    {
        $user = AppUser::find($id);
        return View('admin.appUser.edit', compact('user'));
    }
    public function updateAppUser(Request $request)
    {
        $request->validate([
            'name' => 'bail|required',
            'last_name' => 'bail|required',
            'phone' => 'bail|required',
        ]);
        $user = AppUser::find($request->id);
        $emailcheck = AppUser::where('email', $request->email)->where('id', '!=', $user->id)->first();
        if ($emailcheck) {
            return redirect()->back()->with('email', 'The email address has already been taken.');
        }
        $data = $request->all();
        $user->update($data);
        return redirect()->back()->with('status', 'AppUser Details Update Successfully.');
    }
    public function orgincome($event = "")
    {
        $user = \Auth::user();
        $data = Order::with(['customer:id,name,last_name,email', 'event:id,name'])
            ->where('payment_status', 1)->where('organization_id', $user->id);
        if (!empty($event)) {
            $data->where('event_id', $event);
        }
        $data->orderBy('id', 'DESC')->get();
        $taxFormDetails = ($user->finance_status != '0') ? json_decode($user->finance_details, true) : [];
        return view('admin.organizer.revenue', compact('data', 'user', 'taxFormDetails'));
    }

    public function generateDateRange($startDate, $endDate, $viewType)
    {
        // Assuming the date format is 'd-m-Y'
        $start = Carbon::createFromFormat('d/m/Y', $startDate)->startOfDay();
        $end = Carbon::createFromFormat('d/m/Y', $endDate)->endOfDay();
        $dateRange = [];
        if ($viewType === 'Months') {
            // Generate months for the year of the start date
            $year = $start->year;
            $startYearMonth = Carbon::createFromDate($year, 1, 1)->startOfMonth();
            $endYearMonth = Carbon::createFromDate($year, 12, 1)->endOfMonth();

            $period = new CarbonPeriod($startYearMonth, '1 month', $endYearMonth);
            foreach ($period as $date) {
                $formattedMonth = $date->format("F-Y"); // Uniform format as "MMM-YYYY"
                $dateRange[$formattedMonth] = 0; // Store as a simple list
            }
        } else if ($viewType === 'Weeks') {
            $period = new CarbonPeriod($start, '1 week', $end);
            foreach ($period as $date) {
                $weekStartDate = $date->copy();
                $weekEndDate = $date->copy()->addDays(6); // Add 6 days to cover the entire week
                if ($weekEndDate > $end) {
                    $weekEndDate = $end->copy(); // Adjust the last week to not go past the end date
                }
                $formattedWeek = $weekStartDate->format("m/d/Y") . ' - ' . $weekEndDate->format("m/d/Y");
                $dateRange[$formattedWeek] = 0; // Initialize all weeks with zero
            }
        } else if ($viewType === 'Years') {
            $period = new CarbonPeriod($start->startOfYear(), '1 year', $end->startOfYear());
            foreach ($period as $date) {
                $year = $date->format("Y");
                $dateRange[$year] = 0; // Initialize each year with zero
            }
        } else {
            // For daily ranges, cover every day from start to end
            $period = new CarbonPeriod($start, '1 day', $end);

            foreach ($period as $date) {
                $dateRange[$date->format("m/d/Y")] = 0;  // Initialize all days with zero
            }
        }
        return $dateRange;
    }

    public function getAllIncomesChartData(Request $request)
    {
        $request->validate([
            'from_date' => 'bail|required',
            'to_date' => 'bail|required',
            'view_type' => 'bail|required',
        ]);
        $from_date = $request->from_date;
        $to_date = $request->to_date;
        $view_type = $request->view_type;
        $referalIncomeDatas = $this->getReferalIncomeDatas($from_date, $to_date, $view_type);
        $eventIncomeDatas = $this->getEventIncomeDatas($from_date, $to_date, $view_type);
        return response()->json([
            ['name' => 'Referal Income', 'label' => array_keys($referalIncomeDatas), 'data' => array_values($referalIncomeDatas)],
            ['name' => 'Event Income', 'label' => array_keys($eventIncomeDatas), 'data' => array_values($eventIncomeDatas)]
        ]);
    }

    public function getReferalIncomeChartData(Request $request)
    {
        $request->validate([
            'from_date' => 'bail|required',
            'to_date' => 'bail|required',
            'view_type' => 'bail|required',
        ]);
        $from_date = $request->from_date;
        $to_date = $request->to_date;
        $view_type = $request->view_type;
        $dateRange = $this->getReferalIncomeDatas($from_date, $to_date, $view_type);
        return response()->json([['label' => array_keys($dateRange), 'data' => array_values($dateRange)]]);
    }

    public function getAllTotalIncomes(Request $request)
    {
        $request->validate([
            'from_date' => 'bail|required',
            'to_date' => 'bail|required',
        ]);
        $from_date = $request->from_date;
        $to_date = $request->to_date;

        $eventIncomes = $this->getEventIncomeDatas($from_date, $to_date);
        $referalIncomes = $this->getReferalIncomeDatas($from_date, $to_date);
        $eventTotalIncomes = 0;
        $referalTotalIncomes = 0;
        foreach ($eventIncomes as $eventIncome) {
            $eventTotalIncomes += $eventIncome;
        }
        foreach ($referalIncomes as $referalIncome) {
            $referalTotalIncomes += $referalIncome;
        }
        return ['total_event_income' => number_format($eventTotalIncomes, 2), 'total_referal_income' => number_format($referalTotalIncomes, 2), 'total_incomes' => number_format(($eventTotalIncomes + $referalTotalIncomes), 2)];
    }

    private function getEventIncomeDatas($from_date, $to_date, $view_type = "Days")
    {
        $dateRange = $this->generateDateRange($from_date, $to_date, $view_type);
        $user = \Auth::user();

        switch ($view_type) {
            case 'Days':
                $dateFormat = '%Y-%m-%d';
                break;
            case 'Weeks':
                $dateFormat = '%Y-%m-%d'; // ISO-8601 year and week number
                break;
            case 'Months':
                $dateFormat = '%Y-%m';
                break;
            case 'Years':
                $dateFormat = '%Y';
                break;
        }
        Order::select(
            DB::raw("DATE_FORMAT(created_at, '" . $dateFormat . "') as date_group"),
            DB::raw('SUM(payment) as total_payment')
        )
            ->where('payment_status', 1)
            ->where('payment_type', 'CLOVER')
            ->where('order_status', 'Complete')
            ->where('organization_id', $user->id)
            ->whereBetween('created_at', [Carbon::createFromFormat('d/m/Y', $from_date)->startOfDay(), Carbon::createFromFormat('d/m/Y', $to_date)->endOfDay()])
            ->groupBy('date_group')
            ->get()
            ->each(function ($item) use (&$dateRange, $view_type) {
                if ($view_type === 'Months') {
                    $dateRange[date('F-Y', strtotime($item->date_group))] = (float) $item->total_payment;
                } else if ($view_type === 'Weeks') {
                    if (!empty($dateRange)) {
                        foreach ($dateRange as $date => $drange) {
                            $explodedDate = explode(' - ', $date);
                            $checkingDate = new \DateTime($item->date_group);
                            $startDate = new \DateTime($explodedDate[0]);
                            $endDate = new \DateTime($explodedDate[1]);
                            if ($checkingDate >= $startDate && $checkingDate <= $endDate) {
                                $dateRange[$date] = (float) $item->total_payment;
                            }
                        }
                    }
                } else {
                    $dateRange[date('m/d/Y', strtotime($item->date_group))] = (float) $item->total_payment;
                }
            });
        return $dateRange;
    }

    private function getReferalIncomeDatas($from_date, $to_date, $view_type = "Days")
    {
        $dateRange = $this->generateDateRange($from_date, $to_date, $view_type);
        $user = \Auth::user();
        switch ($view_type) {
            case 'Days':
                $dateFormat = '%Y-%m-%d';
                break;
            case 'Weeks':
                $dateFormat = '%Y-%m-%d'; // ISO-8601 year and week number
                break;
            case 'Months':
                $dateFormat = '%Y-%m';
                break;
            case 'Years':
                $dateFormat = '%Y';
                break;
        }
        ReferalCommission::select(
            DB::raw("DATE_FORMAT(created_at, '" . $dateFormat . "') as date_group"),
            DB::raw('SUM(amount) as total_payment')
        )
            ->where('status', 1)
            ->where('commission_to', $user->id)
            ->whereBetween('created_at', [Carbon::createFromFormat('d/m/Y', $from_date)->startOfDay(), Carbon::createFromFormat('d/m/Y', $to_date)->endOfDay()])
            ->groupBy('date_group')
            ->get()
            ->each(function ($item) use (&$dateRange, $view_type) {
                if ($view_type === 'Months') {
                    $dateRange[date('F-Y', strtotime($item->date_group))] = (float) $item->total_payment;
                } else if ($view_type === 'Weeks') {
                    if (!empty($dateRange)) {
                        foreach ($dateRange as $date => $drange) {
                            $explodedDate = explode(' - ', $date);
                            $checkingDate = new \DateTime($item->date_group);
                            $startDate = new \DateTime($explodedDate[0]);
                            $endDate = new \DateTime($explodedDate[1]);
                            if ($checkingDate >= $startDate && $checkingDate <= $endDate) {
                                $dateRange[$date] = (float) $item->total_payment;
                            }
                        }
                    }
                } else {
                    $dateRange[date('m/d/Y', strtotime($item->date_group))] = (float) $item->total_payment;
                }
            });
        return $dateRange;
    }

    public function getIncomeChartData(Request $request)
    {
        $request->validate([
            'from_date' => 'bail|required',
            'to_date' => 'bail|required',
            'view_type' => 'bail|required',
        ]);
        $from_date = $request->from_date;
        $to_date = $request->to_date;
        $view_type = $request->view_type;

        $dateRange = $this->getEventIncomeDatas($from_date, $to_date, $view_type);
        return response()->json([['label' => array_keys($dateRange), 'data' => array_values($dateRange)]]);
    }

    public function checkoutSession(Request $request)
    {
        $request->session()->put('request', $request->all());
        $key = OrganizerPaymentKeys::where('organizer_id', $request->id)->first()->stripeSecretKey;
        Stripe::setApiKey($key);
        $supportedCurrency = [
            "EUR",   # Euro
            "GBP",   # British Pound Sterling
            "CAD",   # Canadian Dollar
            "AUD",   # Australian Dollar
            "JPY",   # Japanese Yen
            "CHF",   # Swiss Franc
            "NZD",   # New Zealand Dollar
            "HKD",   # Hong Kong Dollar
            "SGD",   # Singapore Dollar
            "SEK",   # Swedish Krona
            "DKK",   # Danish Krone
            "PLN",   # Polish Złoty
            "NOK",   # Norwegian Krone
            "CZK",   # Czech Koruna
            "HUF",   # Hungarian Forint
            "ILS",   # Israeli New Shekel
            "MXN",   # Mexican Peso
            "BRL",   # Brazilian Real
            "MYR",   # Malaysian Ringgit
            "PHP",   # Philippine Peso
            "TWD",   # New Taiwan Dollar
            "THB",   # Thai Baht
            "TRY",   # Turkish Lira
            "RUB",   # Russian Ruble
            "INR",   # Indian Rupee
            "ZAR",   # South African Rand
            "AED",   # United Arab Emirates Dirham
            "SAR",   # Saudi Riyal
            "KRW",   # South Korean Won
            "CNY"    # Chinese Yuan
        ];
        $currencyCode = Setting::first()->currency;
        $amount = $request->total;
        if (!in_array($currencyCode, $supportedCurrency)) {
            $amount = $amount * 100;
        }
        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'line_items' => [
                [
                    'price_data' => [
                        'currency' => $currencyCode,
                        'product_data' => [
                            'name' => "Payment"
                        ],
                        'unit_amount' => $amount,
                    ],
                    'quantity' => 1,
                ]
            ],
            'mode' => 'payment',
            'success_url' => route('orgStripe.success'),
            'cancel_url' => route('settlementReport'),
        ]);
        return response()->json(['id' => $session->id, 'status' => 200]);
    }
    public function stripeSuccess()
    {
        $request = Session::get('request');
        $data['user_id'] = $request['id'];
        $data['payment'] = $request['total'];
        $data['payment_status'] = 1;
        $data['payment_token'] = $request['token'] ?? null;
        $data['payment_type'] = 'Stripe';
        Settlement::create($data);
        Order::where([['organization_id', $data['user_id']], ['payment_status', 1], ['org_pay_status', 0]])->update(['org_pay_status' => 1]);
        return redirect()->route('settlementReport')->withStatus(__('Payment has done successfully.'));
    }
    public function orgKey(Request $request)
    {
        $key = OrganizerPaymentKeys::where('organizer_id', $request->id)->first()->stripePublicKey;
        return response()->json(['key' => $key, 'status' => 200]);
    }
    public function orderCreateForUser(Request $request)
    {
        if ($request->isMethod('get')) {
            if (Auth::user()->hasRole('admin')) {
                $ticket = Ticket::with('event')->where('is_deleted', 0)->get();
            } else {
                $ticket = Ticket::where('is_deleted', 0)->where('user_id', Auth::user()->id)->get();
            }
            return view('admin.order.create', compact('ticket'));
        }
        if ($request->isMethod('post')) {
            $request->validate([
                'email' => 'required',
                'ticket_id' => 'required',
                'quantity' => 'required|min:1'
            ]);
            $ticket = Ticket::find($request->ticket_id, 'allday');
            if ($ticket->allday == 0 && $request->ticket_date == null) {
                return redirect()->back()->with('error', 'Please select a date');
            }
            $email = $request->email;
            $user = AppUser::where('email', $email)->first();
            if (!$user) {
                $user = AppUser::create([
                    'email' => $email,
                    'password' => bcrypt('123456'),
                    'name' => time(),
                    'provider' => 'LOCAL',
                    'is_verify' => 1,
                ]);
                $user = AppUser::where('email', $email)->first();
            }
            $data = [
                'ticket_id' => $request->ticket_id,
                'quantity' => $request->quantity,
                'user_id' => $user->id,
                'ticket_date' => $request->ticket_date,
            ];
            $this->createOrder($data);
            return redirect()->back()->with('status', 'Order has been created successfully.');
        }
    }
    public function createOrder($data)
    {
        $data['payment_type'] = 'LOCAL';
        $ticket = Ticket::findOrFail($data['ticket_id']);
        $event = Event::find($ticket->event_id);

        $org = User::find($event->user_id);
        $user = AppUser::find($data['user_id']);
        $data['order_id'] = '#' . rand(9999, 100000);
        $data['event_id'] = $event->id;
        $data['customer_id'] = $user->id;
        $data['organization_id'] = $org->id;
        $data['payment_status'] = 0;
        $data['order_status'] = 'Complete';
        $data['ticket_date'] = Carbon::parse($data['ticket_date'])->format('Y-m-d 00:00:00');
        $com = Setting::find(1, ['org_commission_type', 'org_commission']);
        $payment = $ticket->price * $data['quantity'];
        $allTax = Tax::where('status', 1)->get();
        $totalTax = [];
        foreach ($allTax as $key => $value) {
            if ($value->amount_type == 'percentage') {
                $totalTax[$key]['id'] = $value->id;
                $totalTax[$key]['price'] = $payment * $value->price / 100;
            }
            if ($value->amount_type == 'price') {
                $totalTax[$key]['id'] = $value->id;
                $totalTax[$key]['price'] = $value->price;
            }
        }
        // calculate whole tax
        $data['tax_data'] = json_encode($totalTax);
        $data['tax'] = array_sum(array_column($totalTax, 'price'));
        $p = $payment - $data['tax'];
        if ($ticket->ticket == "FREE") {
            $data['org_commission'] = 0;
        } else {
            if ($com->org_commission_type == "percentage") {
                $data['org_commission'] = $p * $com->org_commission / 100;
            } else if ($com->org_commission_type == "amount") {
                $data['org_commission'] = $com->org_commission;
            }
        }

        $data['payment'] = $payment + $data['tax'];

        $order = Order::create($data);

        for ($i = 1; $i <= $data['quantity']; $i++) {
            $child['ticket_number'] = uniqid();
            $child['ticket_id'] = $data['ticket_id'];
            $child['order_id'] = $order->id;
            $child['customer_id'] = $user->id;
            $child['checkin'] = $ticket->maximum_checkins ?? null;
            $child['paid'] = 0;
            OrderChild::create($child);
        }
        if (isset($data['tax_data'])) {
            foreach (json_decode($data['tax_data']) as $value) {
                $tax['order_id'] = $order->id;
                $tax['tax_id'] = $value->id;
                $tax['price'] = $value->price;
                OrderTax::create($tax);
            }
        }

        $user = AppUser::find($order->customer_id);
        $setting = Setting::find(1);

        // for user notification
        $message = NotificationTemplate::where('title', 'Book Ticket')->first()->message_content;
        $detail['user_name'] = $user->name . ' ' . $user->last_name;
        $detail['quantity'] = $data['quantity'];
        $detail['event_name'] = Event::find($order->event_id)->name;
        $detail['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $detail['app_name'] = $setting->app_name;
        $noti_data = ["{{user_name}}", "{{quantity}}", "{{event_name}}", "{{date}}", "{{app_name}}"];
        $message1 = str_replace($noti_data, $detail, $message);
        $notification = array();
        $notification['organizer_id'] = null;
        $notification['user_id'] = $user->id;
        $notification['order_id'] = $order->id;
        $notification['title'] = 'Ticket Booked';
        $notification['message'] = $message1;
        Notification::create($notification);
        if ($setting->push_notification == 1) {
            if ($user->device_token != null) {
                (new AppHelper)->sendOneSignal('user', $user->device_token, $message1);
            }
        }
        // for user mail
        $ticket_book = NotificationTemplate::where('title', 'Book Ticket')->first();
        $details['user_name'] = $user->name . ' ' . $user->last_name;
        $details['quantity'] = $data['quantity'];
        $details['event_name'] = Event::find($order->event_id)->name;
        $details['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $details['app_name'] = $setting->app_name;
        if ($setting->mail_notification == 1) {
            try {
                $qrcode = $order->order_id;
                Mail::to($user->email)->send(new TicketBook($ticket_book->mail_content, $details, $ticket_book->subject, $qrcode));
            } catch (\Throwable $th) {
                Log::info($th->getMessage());
            }
            $this->sendMail($order->id);
        }

        // for Organizer notification
        $org = User::find($order->organization_id);
        $or_message = NotificationTemplate::where('title', 'Organizer Book Ticket')->first()->message_content;
        $or_detail['organizer_name'] = $org->first_name . ' ' . $org->last_name;
        $or_detail['user_name'] = $user->name . ' ' . $user->last_name;
        $or_detail['quantity'] = $data['quantity'];
        $or_detail['event_name'] = Event::find($order->event_id)->name;
        $or_detail['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $or_detail['app_name'] = $setting->app_name;
        $or_noti_data = ["{{organizer_name}}", "{{user_name}}", "{{quantity}}", "{{event_name}}", "{{date}}", "{{app_name}}"];
        $or_message1 = str_replace($or_noti_data, $or_detail, $or_message);
        $or_notification = array();
        $or_notification['organizer_id'] = $org->id;
        $or_notification['user_id'] = null;
        $or_notification['order_id'] = $order->id;
        $or_notification['title'] = 'New Ticket Booked';
        $or_notification['message'] = $or_message1;
        Notification::create($or_notification);
        if ($setting->push_notification == 1) {
            if ($org->device_token != null) {
                (new AppHelper)->sendOneSignal('organizer', $org->device_token, $or_message1);
            }
        }
        // for Organizer mail
        $new_ticket = NotificationTemplate::where('title', 'Organizer Book Ticket')->first();
        $details1['organizer_name'] = $org->first_name . ' ' . $org->last_name;
        $details1['user_name'] = $user->name . ' ' . $user->last_name;
        $details1['quantity'] = $data['quantity'];
        $details1['event_name'] = Event::find($order->event_id)->name;
        $details1['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $details1['app_name'] = $setting->app_name;
        if ($setting->mail_notification == 1) {
            try {
                Mail::to($org->email)->send(new TicketBookOrg($new_ticket->mail_content, $details1, $new_ticket->subject));
            } catch (\Throwable $th) {
                Log::info($th->getMessage());
            }
        }
        return true;
    }
    function getTicketsDetails(Request $request)
    {
        $ticket = Ticket::find($request->id, 'allday');
        return response()->json(['allday' => $ticket->allday]);
    }
    public function sendMail($id)
    {
        $order = Order::with(['customer', 'event', 'organization', 'ticket'])->find($id);
        $order->tax_data = OrderTax::where('order_id', $order->id)->get();
        $order->ticket_data = OrderChild::where('order_id', $order->id)->get();
        $customPaper = array(0, 0, 720, 1440);
        $pdf = FacadePdf::loadView('ticketmail', compact('order'))->save(public_path("ticket.pdf"))->setPaper($customPaper, $orientation = 'portrait');
        $data["email"] = $order->customer->email;
        $data["title"] = "Ticket PDF";
        $data["body"] = "";
        $tempp = $pdf->output();
        $sender = Setting::select('sender_email', 'app_name')->first();
        try {
            Mail::send('mail', $data, function ($message) use ($data, $tempp, $sender) {
                $message->from($sender->sender_email, $sender->app_name)
                    ->to($data["email"])
                    ->subject($data["title"])
                    ->attachData($tempp, "ticket.pdf");
            });
        } catch (Throwable $th) {
            Log::info($th->getMessage());
        }
        return true;
    }
}
