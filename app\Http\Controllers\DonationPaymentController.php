<?php

namespace App\Http\Controllers;

use App\Helper\ApiHelper;
use App\Models\DonationPayment;
use App\Models\DonationPost;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class DonationPaymentController extends Controller
{

    public function __construct()
    {
    }

    public function sendReceipt(DonationPayment $donationPayment)
    {
        $donationPayment->status = 1;
        $donationPayment->active = 1;
        $donationPayment->save();
        $post = $donationPayment->post;
        \Mail::to($donationPayment->email)->send(new \App\Mail\DonationReceiptMail($donationPayment, $post));
        return redirect()->back()->with('success', 'Successfully Receipt sended!');
    }

    public function index()
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        // } else {
        //     $donation_payments = DonationPayment::with('post')->whereHas('post', function ($query) use ($userId) {
        //         $query->where('user_id', $userId);
        //     })->orderBy('created_at', 'desc')->get();
        // }
        $donation_payments = DonationPayment::orderBy('created_at', 'desc')->get();

        return view('admin.donations.donation_payments', compact('donation_payments'))->with('i', 1);
    }

    public function create(DonationPayment $donation_payment)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        // } else {
        //     $donation_posts = DonationPost::where('active', 1)->where('user_id', $userId)->get();
        // }
        $donation_posts = DonationPost::where('active', 1)->get();

        return view('admin.donations.donation_payments_add', compact('donation_payment', 'donation_posts'));
    }


    public function store(Request $request)
    {
        $request->validate([
            'donation_post_id' => 'required|numeric',
            'name' => 'required',
            'email' => 'required|email',
            'payment_ref' => 'required',
            'amount' => 'required||numeric',
            'description' => 'required',
            'status' => 'required',
        ]);

        DonationPayment::create([
            'donation_post_id' => $request->input('donation_post_id'),
            'name' => $request->input('name'),
            'email' => $request->input('email'),
            'payment_ref' => $request->input('payment_ref'),
            'amount' => $request->input('amount'),
            'description' => $request->input('description'),
            'status' => $request->input('status'),
            'donated_at' => Carbon::now(),
            'active' => $request->input('active') ?? 0,
        ]);

        return redirect()->route('donation_payments.index')
            ->with('success', 'Donation Payment created successfully.');
    }


    public function show($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
            $donation_payment = DonationPayment::with('post')->find($id);
        // } else {
        //     $donation_payment = DonationPayment::with('post')->whereHas('post', function ($query) use ($userId) {
        //         $query->where('user_id', $userId);
        //     })->find($id);
        // }

        return view('admin.donations.donation_payments_show', compact('donation_payment'));
    }


    public function edit($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        //     $donation_payment = DonationPayment::find($id);
        //     $donation_posts = DonationPost::where('active', 1)->get();
        // } else {
        //     $donation_payment = DonationPayment::with('post')->whereHas('post', function ($query) use ($userId) {
        //         $query->where('user_id', $userId);
        //     })->find($id);
        //     $donation_posts = DonationPost::where('active', 1)->where('user_id', $userId)->get();
        // }
        $donation_payment = DonationPayment::find($id);
        $donation_posts = DonationPost::where('active', 1)->get();
        return view('admin.donations.donation_payments_add', compact('donation_payment', 'donation_posts'));
    }

    public function update(Request $request, DonationPayment $donation_payment)
    {
        $request->validate([
            'donation_post_id' => 'required|numeric',
            'name' => 'required',
            'email' => 'required|email',
            'payment_ref' => 'required',
            'amount' => 'required||numeric',
            'description' => 'required',
            'status' => 'required',
            // 'active' => 'required',
        ]);

        $donation_payment->update([
            'donation_post_id' => $request->input('donation_post_id'),
            'name' => $request->input('name'),
            'email' => $request->input('email'),
            'payment_ref' => $request->input('payment_ref'),
            'amount' => $request->input('amount'),
            'description' => $request->input('description'),
            'status' => $request->input('status'),
            'donated_at' => Carbon::now(),
            'active' => $request->input('active') ?? 0,
        ]);

        return redirect()->route('donation_payments.index')
            ->with('success', 'Donation Payment updated successfully');
    }

    public function destroy($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        $donation_payment = DonationPayment::find($id);
        // } else {
        //     $donation_payment = DonationPayment::with('post')->whereHas('post', function ($query) use ($userId) {
        //         $query->where('user_id', $userId);
        //     })->find($id);
        // }
        $donation_payment->delete();

        return redirect()->route('donation_payments.index')
            ->with('success', 'Donation Payment deleted successfully');
    }

    public function getMyDonations(Request $request)
    {
        $user = $request->user();
        $donationPayments = DonationPayment::where('added_by', $user->id)->get();
        return ApiHelper::responseData(0, DonationPayment::getPublic($donationPayments));
    }

    public function apiGetByPostId($id)
    {
        $donation_payment = DonationPayment::where('active', 1)->where('status', DonationPayment::PS_SUCCESS)
            ->where('donation_post_id', $id)->orderBy('id', 'desc')->simplePaginate(10);

        return ApiHelper::responseData(0, DonationPayment::getPublic($donation_payment));
    }

    public function charge($reqArray)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => env('PAYMENT_APP_URL') . '/charge.php',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
    "amount":"' . $reqArray['amount'] . '",
    "source":"AMA DONATION",
    "payment_token":"' . $reqArray['payment_token'] . '"
}',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if (!empty($response) && is_array(json_decode($response, true))) {
            return json_decode($response, true);
        } else {
            return ['error' => 1, 'response' => $response];
        }
    }

    public function apiAdd(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'donation_type' => 'required',
            'amount' => 'required',
            'payment_token' => 'required',
            'post_id' => 'required',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return ApiHelper::responseFormValidation($validator);
        }
        $reqArray = $request->all();
        $encodedUser = $reqArray['user_id'];
        $user_id = 0;
        if (isset($reqArray['user_id']) && !empty($reqArray['user_id'])) {
            $userArray = json_decode(base64_decode($encodedUser), true);
            if (isset($userArray['id']) && !empty($userArray['id'])) {
                $user_id = $userArray['id'];
            } else {
                return ApiHelper::responseMessage(1, 'Invalid logged in user');
            }
        }
        if (isset($reqArray['donation_type']) && $reqArray['donation_type'] == 'Monthly') {
            $userInfo['first_name'] = $request->input('first_name');
            $userInfo['last_name'] = $request->input('last_name');
            $userInfo['email'] = $request->input('email');
            $userInfo['user_id'] = $user_id;
            $userInfo['is_recuring'] = 1;
            $tokenData['post_id'] = $reqArray['post_id'];
            $tokenData['amount'] = $reqArray['amount'];
            $tokenData['message'] = $reqArray['message'];
            return $this->doSubscription($request->payment_token, $userInfo, $tokenData, $request);
        } else {
            $chargeObject = $this->charge($reqArray);
        }
        if (isset($chargeObject['error']) && isset($chargeObject['response']) && $chargeObject['error'] == '0') {
            $payment_status = DonationPayment::PS_SUCCESS;
            $donationPayment = DonationPayment::create([
                'name' => $request->input('first_name') . " " . $request->input('last_name'),
                'donation_post_id' => $request->input('post_id'),
                'email' => $request->input('email'),
                'description' => $request->input('message') ?? "",
                'amount' => $request->input('amount'),
                'status' => $payment_status,
                'payment_ref' => $chargeObject['response']['id'],
                'active' => 1,
                'added_by' => $user_id,
                'is_recuring' => 0,
                'donated_at' => Carbon::now()
            ]);
            $post = DonationPost::find($request->input('post_id'));
            \Mail::to($donationPayment->email)->send(new \App\Mail\DonationReceiptMail($donationPayment, $post));
            return ApiHelper::responseMessage(0, 'Your donation successfully processed');
        } else {
            return ApiHelper::responseMessage(1, $chargeObject['response']);
        }
    }

    public function doSubscription($paymentToken, $userInfo, $tokenData, $request)
    {
        if (isset($tokenData['post_id']) && !empty($tokenData['post_id'])) {
            $donationPostInfo = DonationPost::find($tokenData['post_id']);
            if (isset($donationPostInfo->id) && !empty($donationPostInfo->id)) {
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => env('PAYMENT_APP_URL') . '/subscriptions.php',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => '{
                        "action":"create-plan",
                        "donation_post_id":"' . $tokenData['post_id'] . '",
                        "title":"' . $donationPostInfo->title . '",
                        "amount":' . ($tokenData['amount'] * 100) . '
                    }',
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json'
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                if (!empty($response) && is_array(json_decode($response, true))) {
                    $planResponseArray = json_decode($response, true);
                    if (
                        isset($planResponseArray['error'])
                        && isset($planResponseArray['response']['id']) &&
                        $planResponseArray['error'] == '0'
                    ) {
                        $planId = $planResponseArray['response']['id'];
                        if (isset($userInfo['email']) && !empty($userInfo['email'])) {
                            $curl = curl_init();
                            curl_setopt_array($curl, array(
                                CURLOPT_URL => env('PAYMENT_APP_URL') . '/subscriptions.php',
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_ENCODING => '',
                                CURLOPT_MAXREDIRS => 10,
                                CURLOPT_TIMEOUT => 0,
                                CURLOPT_FOLLOWLOCATION => true,
                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                CURLOPT_CUSTOMREQUEST => 'POST',
                                CURLOPT_POSTFIELDS => '{
                                "action":"create-customer",
                                "email":"' . $userInfo['email'] . '",
                                "first_name":"' . $userInfo['first_name'] . '",
                                "payment_token":"' . $paymentToken . '",
                                "last_name":"' . $userInfo['last_name'] . '"
                            }',
                                CURLOPT_HTTPHEADER => array(
                                    'Content-Type: application/json'
                                ),
                            ));
                            $response = curl_exec($curl);
                            curl_close($curl);
                            if (!empty($response) && is_array(json_decode($response, true))) {
                                $customerResponseArray = json_decode($response, true);
                                if (
                                    isset($customerResponseArray['error'])
                                    && isset($customerResponseArray['response']['id']) &&
                                    $customerResponseArray['error'] == '0'
                                ) {
                                    $customerId = $customerResponseArray['response']['id'];
                                    $curl = curl_init();
                                    curl_setopt_array($curl, array(
                                        CURLOPT_URL => env('PAYMENT_APP_URL') . '/subscriptions.php',
                                        CURLOPT_RETURNTRANSFER => true,
                                        CURLOPT_ENCODING => '',
                                        CURLOPT_MAXREDIRS => 10,
                                        CURLOPT_TIMEOUT => 0,
                                        CURLOPT_FOLLOWLOCATION => true,
                                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                        CURLOPT_CUSTOMREQUEST => 'POST',
                                        CURLOPT_POSTFIELDS => '{
                                        "action":"create-subscription",
                                        "plan_id":"' . $planId . '",
                                        "customer_id":"' . $customerId . '"
                                    }',
                                        CURLOPT_HTTPHEADER => array(
                                            'Content-Type: application/json'
                                        ),
                                    ));
                                    $response = curl_exec($curl);
                                    curl_close($curl);
                                    if (!empty($response) && is_array(json_decode($response, true))) {
                                        $subscriptionResponseArray = json_decode($response, true);
                                        if (
                                            isset($subscriptionResponseArray['error'])
                                            && isset($subscriptionResponseArray['response']['id']) &&
                                            $subscriptionResponseArray['error'] == '0'
                                        ) {
                                            $curl = curl_init();
                                            curl_setopt_array($curl, array(
                                                CURLOPT_URL => env('PAYMENT_APP_URL') . '/charge.php',
                                                CURLOPT_RETURNTRANSFER => true,
                                                CURLOPT_ENCODING => '',
                                                CURLOPT_MAXREDIRS => 10,
                                                CURLOPT_TIMEOUT => 0,
                                                CURLOPT_FOLLOWLOCATION => true,
                                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                                CURLOPT_CUSTOMREQUEST => 'POST',
                                                CURLOPT_POSTFIELDS => '{
    "amount":"' . $tokenData['amount'] . '",
    "source":"AMA DONATION",
    "payment_token":"' . $paymentToken . '"
}',
                                                CURLOPT_HTTPHEADER => array(
                                                    'Content-Type: application/json'
                                                ),
                                            ));

                                            $response = curl_exec($curl);

                                            curl_close($curl);
                                            if (!empty($response) && is_array(json_decode($response, true))) {
                                                $paymentResponseArray = json_decode($response, true);
                                                if (
                                                    isset($paymentResponseArray['error'])
                                                    && isset($paymentResponseArray['response']) &&
                                                    $paymentResponseArray['error'] == '0'
                                                ) {
                                                    \App\Models\DonationPayment::saveDonationDetails($paymentResponseArray['response']['id'], $tokenData, $userInfo);
                                                    return ApiHelper::responseMessage(0, 'Successfully Donated!');
                                                } else {
                                                    return ApiHelper::responseMessage(1, $paymentResponseArray['response']);
                                                }
                                            } else {
                                                return ApiHelper::responseMessage(1, 'Charge api returns false');
                                            }
                                        } else {
                                            return ApiHelper::responseMessage(1, $subscriptionResponseArray['response']);
                                        }
                                    } else {
                                        return ApiHelper::responseMessage(1, 'Create subscription api returns false');
                                    }
                                } else {
                                    return ApiHelper::responseMessage(1, $customerResponseArray['response']);
                                }
                            } else {
                                return ApiHelper::responseMessage(1, 'Create customer api returns false');
                            }
                        } else {
                            return ApiHelper::responseMessage(1, 'User informations not found');
                        }
                    } else {
                        return ApiHelper::responseMessage(1, $planResponseArray['response']);
                    }
                } else {
                    return ApiHelper::responseMessage(1, 'Create plan api returns false');
                }
            }
        }
        return ApiHelper::responseMessage(1, 'Something went wrong');
    }
}
