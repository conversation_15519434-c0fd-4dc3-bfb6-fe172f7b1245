<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Programs extends Model
{
    use HasFactory;
    protected $table = 'programs';
    protected $fillable = [
        'name',
        'status',
    ];

    public static function getProgramsWithCountOfDirectory()
    {
        return DB::table('programs')
            ->select('programs.*', DB::raw('COUNT(directories.id) as directory_count'))
            ->leftJoin('directories', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(directories.what_we_do, JSON_QUOTE(CAST(programs.id AS CHAR)))"), DB::raw('1'))
                    ->where('directories.status', '=', 1);
            })
            ->groupBy('programs.id', 'programs.name', 'programs.status')
            ->orderBy('programs.id', 'asc')
            ->get();
    }

    public static function getProgramsWithCountOfSchools()
    {
        return DB::table('programs')
            ->select('programs.*', DB::raw('COUNT(schools.id) as schools_count'))
            ->leftJoin('schools', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(schools.what_we_do, JSON_QUOTE(CAST(programs.id AS CHAR)))"), DB::raw('1'))
                    ->where('schools.status', '=', 1);
            })
            ->groupBy('programs.id', 'programs.name', 'programs.status')
            ->orderBy('programs.id', 'asc')
            ->get();
    }
}
