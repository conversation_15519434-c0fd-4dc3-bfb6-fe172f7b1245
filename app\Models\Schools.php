<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Schools extends Model
{
    use HasFactory;
    protected $table = 'schools';
    protected $fillable = [
        'name',
        'business_name',
        'image',
        'location',
        'directory_type',
        'tags',
        'status',
        'prime_status',
        'address_type',
        'address',
        'latitude',
        'longtitude',
        'business_url',
        'social_media',
        'contact_informations',
        'description',
        'business_hours',
        'show_business_hours',
        'gallery',
        'personal_email',
        'join_as_organizer',
        'created_by',
        'ref_id',
        'order',
        'related_directories',
        'chapters',
        'work_region',
        'where_we_work',
        'what_we_do',
        'donate_and_support',
        'external_link',
    ];

    protected $casts = [
        'directory_type' => 'array',
    ];

    public function typeLinks()
    {
        return $this->hasMany(\App\Models\DirectoryTypeLink::class, 'directory_id');
    }

    public function types()
    {
        return $this->belongsToMany(\App\Models\DirectoryType::class, 'directory_type_links', 'directory_id', 'type_id');
    }

    public function category()
    {
        return $this->belongsTo(DirectoryType::class, 'directory_type');
    }

    public static function getDirectoryLists()
    {
        return Schools::where('status', 1)->pluck('name', 'id');
    }

    public static function getBusinessUrl($contactInfo)
    {
        $contactInfoArray = json_decode($contactInfo, true);
        if (isset($contactInfoArray['website']) && !empty($contactInfoArray['website'])) {
            return $contactInfoArray['website'];
        }
        return 'N/A';
    }

    public function directoryType()
    {
        return $this->belongsTo(DirectoryType::class, 'directory_type');
    }

    public static function constructAddress($school)
    {
        if (isset($school->address) && is_array(json_decode($school->address, true))) {
            $address = json_decode($school->address, true);
            foreach ($address as $key => $addr) {
                $address[$key]['url'] = url('/schoolsDetails/' . $school->id . '/' . str_replace(' ', '-', $school->name));
            }
        } else {
            $address[0]['street'] = $school->address ?? "";
            $address[0]['lat'] = $school->latitude ?? "";
            $address[0]['long'] = $school->longtitude ?? "";
            $address[0]['title'] = $school->title ?? "";
            $address[0]['url'] = url('/schoolsDetails/' . $school->id . '/' . str_replace(' ', '-', $school->name));
        }
        return $address;
    }
    /*
     * return
     */
    public static function returnRelatedDirectoriesAndFormatting($directory_id)
    {
        $school = Schools::find($directory_id);

        if (!$school) {
            return [];
        }

        $selectedDirectories = $school->related_directories;

        if ($selectedDirectories) {
            return json_decode($selectedDirectories, true) ?? [];
        }

        return [];
    }
    /*
     * return chapters
     */
    public static function returnChaptersAndFormatting($directory_id)
    {
        $school = Schools::find($directory_id);

        if (!$school) {
            return [];
        }

        $selectedChapters = $school->chapters;

        if($selectedChapters){
            return json_decode($selectedChapters, true) ?? [];
        }

        return [];
    }

    public static function returnSimilarOrganizationOrChapters($type, $directory_id)
    {
        if($type == 'related_directories'){
            $similarOrganizationsCollection = Schools::find($directory_id)->related_directories;
        } else {
            $similarOrganizationsCollection = Schools::find($directory_id)->chapters;
        }
        $similarOrganizations = [];
        if($similarOrganizationsCollection){
            $similarOrganizationsCollection = json_decode($similarOrganizationsCollection, true) ?? [];
            foreach($similarOrganizationsCollection as $item){
                $similarOrganizations[] = DB::table('schools')
                    ->where('id', $item)
                    ->select('id', 'name', 'image')
                    ->first();
            }
        }
        return $similarOrganizations;
    }
}
