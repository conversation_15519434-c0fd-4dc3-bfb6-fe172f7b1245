<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeadersCategory extends Model
{
    use HasFactory;
    protected $table = 'leaders_categories';
    protected $fillable = [
        'category',
        'status',
        'order',
    ];

    // **********Leaders-Count**********
    public function leaders()
    {
        return $this->hasMany(Leaders::class, 'category', 'category');
    }
    // **********Leaders-Count**********
}
