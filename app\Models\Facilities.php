<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Facilities extends Model
{
    use HasFactory;
    protected $table = 'masajids_facilities';
    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    public static function getFacilitiesWithCountOfMasajids()
    {
        return DB::table('masajids_facilities')
            ->select('masajids_facilities.*', DB::raw('COUNT(masajids.id) as masajids_count'))
            ->leftJoin('masajids', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(masajids.facilities, JSON_QUOTE(CAST(masajids_facilities.id AS CHAR)))"), DB::raw('1'))
                    ->where('masajids.status', '=', 1);
            })
            ->groupBy('masajids_facilities.id', 'masajids_facilities.name', 'masajids_facilities.description', 'masajids_facilities.status')
            ->orderBy('masajids_facilities.id', 'asc')
            ->get();
    }
}
