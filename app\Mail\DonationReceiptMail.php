<?php

namespace App\Mail;

use App\Models\DonationPayment;
use App\Models\DonationPost;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class DonationReceiptMail extends Mailable
{
    use Queueable, SerializesModels;

    public $donationPayment;
    public $post;
    /**
     * Create a new message instance.
     */
    public function __construct(DonationPayment $donationPayment, DonationPost $donationPost)
    {
        $this->donationPayment = $donationPayment;
        $this->post = $donationPost;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Thank You for Helping Us Make a Difference!',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'admin.donations.donation-receipt-mail',
        );
    }


    public function build()
    {
        return $this->subject('Thank You for Helping Us Make a Difference!')
            ->view('admin.donations.donation-receipt-mail')->with([
                    'donationPayment' => $this->donationPayment,
                    'post' => $this->post,
                ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
