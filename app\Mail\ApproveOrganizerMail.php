<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ApproveOrganizerMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $role;
    public $user;
    public function __construct($role, $user)
    {
        $this->role = $role;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if ($this->role === 'admin') {
            $subject = 'Organizer has been successfully approved';
        } elseif ($this->role === 'organization') {
            $subject = 'New Organizer added';
        } elseif ($this->role === 'organizer') {
            $subject = 'Request has been approved';
        }
        return $this->subject($subject)
            ->view('admin.user.organizer-approve-mail')->with([
                    'role' => $this->role,
                    'user' => $this->user
                ]);
    }
}
