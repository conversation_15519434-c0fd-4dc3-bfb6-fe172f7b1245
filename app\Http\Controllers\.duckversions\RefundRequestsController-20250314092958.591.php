<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use \App\Models\RefundRequest;
use \App\Models\Setting;

class RefundRequestsController extends Controller
{
    public function requests()
    {
        $refunds = RefundRequest::orderBy('id', 'desc')->get();
        return view('admin.refunds.requests', compact('refunds'));
    }

    public function requestsDetails($id)
    {
        $refundDetail = RefundRequest::find($id);
        $currency = Setting::find(1)->currency_sybmol;
        $refundRequestIdsArray = $refundDetail->pluck('ticket_id')->toArray();
        return view('admin.refunds.requestsDetail', compact('refundDetail','currency'));
    }
}
