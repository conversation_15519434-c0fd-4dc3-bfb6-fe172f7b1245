<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index()
    {
        abort_if(Gate::denies('category_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $category = Category::OrderBy('id', 'DESC')->get();
        return view('admin.category.index', compact('category'));
    }

    public function create()
    {
        abort_if(Gate::denies('category_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        return view('admin.category.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'bail|required',
            'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            'pins_color' => 'nullable|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);
        $data = $request->all();

        if ($request->hasFile('image')) {
            $data['image'] = (new AppHelper)->saveImage($request);
        }
        if ($request->hasFile('icon')) {
            $data['icon'] = (new AppHelper)->saveIcon($request);
        }
        if (!empty($request->pins_color)) {
            $data['pins_color'] = strtoupper($request->pins_color);
        }
    
        Category::create($data);
        return redirect()->route('category.index')->withStatus(__('Category has added successfully.'));
    }

    public function edit(Category $category)
    {
        abort_if(Gate::denies('category_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        return view('admin.category.edit', compact('category'));
    }

    // public function update(Request $request, Category $category)
    // {

    //     $request->validate([
    //         'name' => 'bail|required',
    //     ]);
    //     $data = $request->all();
    //     if ($request->hasFile('image')) {
    //         $request->validate([
    //             'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
    //         ]);
    //         if (isset($category->image)) {
    //             (new AppHelper)->deleteFile($category->image);
    //         }
    //         $data['image'] = (new AppHelper)->saveImage($request);
    //     }
    //     if ($request->hasFile('icon')) {
    //         $request->validate([
    //             'icon' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
    //         ]);
    //         if (isset($category->icon)) {
    //             (new AppHelper)->deleteFile($category->icon);
    //         }
    //         $data['icon'] = (new AppHelper)->saveIcon($request);
    //     }
    //     Category::find($category->id)->update($data);
    //     return redirect()->route('category.index')->withStatus(__('Category has updated successfully.'));
    // }

    public function update(Request $request, Category $category)
{
    $request->validate([
        'name' => 'bail|required|string|max:255',
        'status' => 'required|in:1,0',
        'pins_color' => 'nullable|regex:/^#[0-9A-Fa-f]{6}$/', // Validate hex color
    ]);

    $data = $request->all();

    // Handle image update
    if ($request->hasFile('image')) {
        $request->validate([
            'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
        ]);
        if (!empty($category->image)) {
            (new AppHelper)->deleteFile($category->image);
        }
        $data['image'] = (new AppHelper)->saveImage($request);
    }

    // Handle icon update
    if ($request->hasFile('icon')) {
        $request->validate([
            'icon' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
        ]);
        if (!empty($category->icon)) {
            (new AppHelper)->deleteFile($category->icon);
        }
        $data['icon'] = (new AppHelper)->saveIcon($request);
    }

    // Ensure color is stored correctly
    if (!empty($request->pins_color)) {
        $data['pins_color'] = strtoupper($request->pins_color); // Store in uppercase
    } else {
        $data['pins_color'] = null; // Allow clearing the color
    }

    $category->update($data);

    return redirect()->route('category.index')->withStatus(__('Category has been updated successfully.'));
}

}
