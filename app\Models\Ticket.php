<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    use HasFactory;
    protected $fillable = [
        'event_id',
        'user_id',
        'form_id',
        'ticket_number',
        'name',
        'type',
        'quantity',
        'ticket_per_order',
        'start_time',
        'end_time',
        'price',
        'description',
        'status',
        'is_deleted',
        'allday',
        'maximum_checkins',
        'seatmap_id',
        'fee_type',
        'fee',
        'fee_2_type',
        'fee_2',
        'insurance',
        'insurance_amount_type',
        'insurance_amount',
        'donation_amount',
        'donation_amount_type',
    ];

    protected $table = 'tickets';
    protected $dates = ['start_time', 'end_time'];
    function event()
    {
        return $this->hasOne(Event::class, 'id', 'event_id');
    }
    function form()
    {
        return $this->hasOne(Form::class, 'id', 'form_id');
    }

    public static function getInsuranceAmount($ticket)
    {
        $insurance = 0;
        $ticketFee = self::getTicketFee($ticket);
        $ticketFee2 = self::getTicketFee2($ticket);
        $finalTicketPrice = ($ticket->price + $ticketFee + $ticketFee2);
        if ($ticket->insurance == '1') {
            if ($ticket->insurance_amount > 0) {
                if ($ticket->insurance_amount_type == '%') {
                    $insurance = (($ticket->insurance_amount / 100) * $finalTicketPrice);
                } else {
                    $insurance = $ticket->insurance_amount;
                }
            }
            if ($insurance >= 1) {
                return bcdiv($insurance, 1, 2);
            } else {
                return bcdiv($insurance, 1, 3);
            }
        }
        return $insurance;
    }

    public static function getTicketFee($ticket)
    {
        $price = (($ticket->type == 'paid' && $ticket->price > 0) || ($ticket->type == 'donation' && $ticket->price > 0)) ? $ticket->price : 0;
        $fee = 0;
        if ($ticket->fee > 0) {
            if ($ticket->fee_type == '%') {
                $fee = (($ticket->fee / 100) * $price);
            } else {
                $fee = $ticket->fee;
            }
        }
        if ($fee >= 1) {
            return round(bcdiv($fee, 1, 2), 2);
        } else {
            return round(bcdiv($fee, 1, 3), 2);
        }
    }

    public static function getTicketFee2($ticket)
    {
        $price = (($ticket->type == 'paid' && $ticket->price > 0) || ($ticket->type == 'donation' && $ticket->price > 0)) ? $ticket->price : 0;
        $fee = 0;
        if ($ticket->fee_2 > 0) {
            if ($ticket->fee_2_type == '%') {
                $fee = (($ticket->fee_2 / 100) * $price);
            } else {
                $fee = $ticket->fee_2;
            }
        }
        if ($fee >= 1) {
            return round(bcdiv($fee, 1, 2), 2);
        } else {
            return round(bcdiv($fee, 1, 3), 2);
        }
    }

    public static function getPaidAmount($price, $totalOrderAmount, $discountAmount)
    {
        if ($discountAmount > 0) {
            $discountPercentage = ($discountAmount / $totalOrderAmount) * 100;
            if ($discountPercentage > 0) {
                return number_format(($price - (($price * $discountPercentage) / 100)), 2);
            }
        }

        // if ($coupen) {
        //     if ($coupen->discount_type == 0) {
        //         return number_format(($price - (($price * $coupen->discount) / 100)), 2);
        //     } else {
        //         return number_format(($price - $coupen->discount), 2);
        //     }
        // }
        return $price;
    }
}
