<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class joinAsOrganizerMail extends Mailable
{
    use Queueable, SerializesModels;
 
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $directory;
    public $user;
    public function __construct($directory, $user)
    {
        $this->directory = $directory;
        $this->user = $user;
    }
 
    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        // dd(config('mail.mailers.smtp'));
        return $this->subject('New Organizer Request to Join Your Organization')
            ->view('admin.organizer.join-as-organizer-mail')->with([
                    'user' => $this->user,
                    'directory' => $this->directory,
                ]);
    }
}
