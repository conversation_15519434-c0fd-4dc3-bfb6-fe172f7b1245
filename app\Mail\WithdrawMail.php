<?php

namespace App\Mail;

use App\Models\ReferIncomeTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class WithdrawMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $refIncTransaction;
    public $role;

    public function __construct($role, ReferIncomeTransaction $referIncomeTransaction)
    {
        $this->refIncTransaction = $referIncomeTransaction;
        $this->role = $role;
        if ($role == 'organizer') {
            $this->subject = 'New Referrer Withdraw Request';
        } else {
            if ($this->refIncTransaction->status == '1') {
                $this->subject = 'Your withdrawal request has been processed';
            } elseif ($this->refIncTransaction->status == '2') {
                $this->subject = 'Your withdrawal request has been rejected';
            }
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('admin.referer.withdrawMail')->with(['role' => $this->role, 'transaction' => $this->refIncTransaction]);
    }
}
