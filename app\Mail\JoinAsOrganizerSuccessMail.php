<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class JoinAsOrganizerSuccessMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $user;
    public $directory;
    public function __construct($user, $directory)
    {
        $this->user = $user;
        $this->directory = $directory;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('You are onboard now!')
            ->view('admin.organizer.join-as-organizer-success-mail')->with([
                    'user' => $this->user,
                    'directory' => $this->directory,
                ]);
    }
}
