<?php

namespace App\Http\Controllers;

use App\Models\Directory;
use Illuminate\Http\Request;
use \App\Models\User;
use \App\Models\DonationPost;
use \App\Models\DonationCategory;
use Carbon\Carbon;

class DonationController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        if ($user->id == 1 && $user->hasRole('admin')) {
            // Admin: Show all posts
            $donation_posts = DonationPost::orderBy('id', 'desc')
                ->with('user', 'category', 'added_by_user', 'last_edited_by_user')
                ->get();
        } elseif ($user->hasRole('Organizer')) {
            // Organizer: Show only their posts
            $donation_posts = DonationPost::where('user_id', $user->id)
                ->orWhere('added_by', $user->id)
                ->orderBy('id', 'desc')
                ->with('category', 'added_by_user', 'last_edited_by_user')
                ->get();
        } else {
            // Other roles: No access or restricted data
            $donation_posts = collect(); // Return an empty collection
        }

        $today = Carbon::now();
        foreach ($donation_posts as $post) {
            if ($post->expired_date < $today && $post->active == 1) {
                $post->update(['active' => 0]);
            }
        }
        // DonationPost::where('expired_date', '<', $today)->where('active', 1)->update(['active' => 0]);
        return view('admin.donations.donation_posts', compact('donation_posts'))
            ->with('i', 1);
    }

    public function clone($donationPostId)
    {
        if (empty($donationPostId)) {
            return redirect()->back();
        }
        $oldDonation = DonationPost::find($donationPostId);

        $newDonation = $oldDonation->replicate(); // Clone the row
        $newDonation->title = $oldDonation->title . " - cloned " . date('m/d/Y'); // Clone the row
        $newDonation->active = 0; // Clone the row
        $newDonation->save(); // Save the cloned row
        return redirect()->route('donation_posts.index')
            ->with('success', 'Donation post cloned successfully.');
    }

    public function getAllTypeOfOrganizations()
    {
        $organizations = [];
        $organizationsArray = User::getOrganizationUsers();
        if (!empty($organizationsArray)) {
            foreach ($organizationsArray as $organization) {
                if ($organization->image)
                    $organization->image = AppSetting::getAssetUrl($organization->image);
                $orgTypeQuery = $organization->get_org_type;
                $org_type = $orgTypeQuery->title ?? "";
                if (!isset($organizations[$org_type])) {
                    $organizations[$org_type] = [];
                }
                if (!empty($org_type)) {
                    array_push($organizations[$org_type], $organization->toArray());
                }
            }
        }
        return $organizations;
    }

    public function create(DonationPost $donation_posts)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        $users = User::orderBy('id', 'DESC')->get();
        // $is_admin = false;
        $organizations = Directory::with('organizers')->orderBy('id', 'DESC')->get();
        // $organizations = [];
        // if (User::isAccessByAdmin()) {
        //     $is_admin = true;
        //     $organizations = $this->getAllTypeOfOrganizations();
        //     $donation_categories = DonationCategory::where('active', true)->get();
        // } else {
        $donation_categories = DonationCategory::where('active', true)->get();
        // }

        return view('admin.donations.donation_posts_add', ['users' => $users, 'organizations' => $organizations, 'donation_posts' => $donation_posts, 'donation_categories' => $donation_categories]);
    }

    public function store(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'title' => 'required',
            'donation_category_id' => 'required',
            'expired_date' => 'nullable|date',
            'description' => 'required|min:50',
            'target' => 'nullable|numeric|min:0',
            'user' => 'nullable|integer|exists:users,id',
            'organization' => 'nullable|integer|exists:directories,id',
            'intervals' => 'nullable|array', // Validate intervals
            'intervals.*' => 'string', // Validate each interval
            'supported_document' => 'nullable|file|mimes:pdf,doc,docx,xlsx,csv,txt,pptx|max:2048',
            'is_external_link' => 'nullable|in:1,0', // Validate the checkbox
            'external_link' => 'nullable|string|max:2048', // Validate the external link
            'donation_designation' => 'nullable|array', // Validate donation_designation as an array
            'donation_designation.*' => 'nullable|string', // Validate each donation_designation as a string
        ]);

        $userId = $request->input('user', $user->id); // Use authenticated user's ID if not provided
        // $organizationId = $user->org_id;
        $organizationId = $request->organization;

        // Prepare tab data
        $tabData = [];
        $rowCounter = 1; // Counter for row keys
        $intervals = $request->input('intervals', []); // Get selected intervals

        foreach ($intervals as $interval) {
            $amountKey = strtolower(str_replace(' ', '_', $interval)) . '_amount';
            $detailsKey = strtolower(str_replace(' ', '_', $interval)) . '_details';

            $amounts = $request->input($amountKey, []);
            $details = $request->input($detailsKey, []);

            foreach ($amounts as $index => $amount) {
                if (!empty($amount)) {
                    $tabData[$interval]["row_{$rowCounter}"] = [
                        'amount' => $amount,
                        'details' => $details[$index] ?? '',
                    ];
                    $rowCounter++; // Increment row key counter
                }
            }
        }

        $customAmountJson = json_encode($tabData);

        // Handle document upload
        $documentPath = null;
        if ($request->hasFile('supported_document')) {
            $document = $request->file('supported_document');
            $documentPath = $document->store('documents', 'public');
        }

        // Handle the checkbox and external link logic
        $isExternalLink = $request->has('is_external_link') ? 1 : 0;
        $externalLink = $isExternalLink ? $request->input('external_link') : null;

        // Handle multiple donation designations
        $donationDesignations = $request->input('donation_designation', []);
        $donationDesignationsJson = json_encode($donationDesignations); // Convert array to JSON

        $expiredDateInput = $request->input('expired_date');
        if ($expiredDateInput) {
            $expiredDate = \DateTime::createFromFormat('m/d/Y', $expiredDateInput);
            if ($expiredDate === false) {
                return response()->json(['error' => 'Invalid date format'], 400);
            }
            // Set the datetime format for storage in the database
            $formattedExpiredDate = $expiredDate->format('Y-m-d H:i:s');
        } else {
            // If expired_date is not provided, set it to null
            $formattedExpiredDate = null;
        }
        // Create the donation post
        $donationPost = DonationPost::create([
            'title' => $request->input('title'),
            'custom_amount' => $customAmountJson, // Store the JSON data
            'description' => $request->input('description'),
            'donation_category_id' => $request->input('donation_category_id'),
            'expired_date' => $formattedExpiredDate,
            'target' => (!empty($request->input('target'))) ? $request->input('target') : 0,
            'user_id' => $userId, // Assign the correct user ID
            'directory_id' => $organizationId,
            'added_by' => $user->id,
            'active' => $request->input('active') ?? 0,
            'supported_document' => $documentPath, // Store document path
            'is_external_link' => $isExternalLink, // Store the checkbox value
            'external_link' => $externalLink, // Store the external link value
            'donation_designation' => $donationDesignationsJson, // Store the designations as JSON
        ]);



        // Handle image uploads
        $gallery = [];
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($gallery, $imageName);
            }
        }

        if (!empty($gallery)) {
            $donationPost->update([
                'image' => json_encode($gallery),
            ]);
        }

        return redirect()->route('donation_posts.index')
            ->with('success', 'Donation post created successfully.');
    }

    public function show($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        $donation_posts = DonationPost::where('id', $id)->with('category', 'user', 'payments')->first();
        // } else {
        //     $donation_posts = DonationPost::where('user_id', $userId)->where('id', $id)->with('category', 'user', 'payments')->first();
        // }

        $images = json_decode($donation_posts->image) ?? [];
        $total_payment = DonationPost::GetPaymentTotalById($donation_posts->id);
        return view('admin.donations.donation_posts_show', compact('donation_posts', 'images', 'total_payment'));
    }

    public function edit($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);


        $donation_posts = DonationPost::find($id);
        $donation_categories = DonationCategory::where('active', true)->get();
        $organizations = Directory::orderBy('id', 'DESC')->get();
        $users = User::orderBy('id', 'DESC')->get();

        $image = [];
        if ($donation_posts->image) {
            $image = json_decode($donation_posts->image);
            $image = array_map(function ($key, $value) {
                return ['id' => $key + 1, 'src' => url('/images/upload/' . $value)];
            }, array_keys($image), $image);
        }

        // Decode custom_amount JSON
        $customAmount = json_decode($donation_posts->custom_amount, true);
        $intervals = array_keys($customAmount ?? []);
        $details = $customAmount ?? [];

        // Assign intervals and details to $donation_posts
        $donation_posts->intervals = $intervals;
        $donation_posts->details = $details;

        // Generate full URL for supported document
        $donation_posts->supported_document = $donation_posts->supported_document
            ? asset('storage/' . $donation_posts->supported_document)
            : null;

        // Include is_external_link and external_link fields
        $donation_posts->is_external_link = $donation_posts->is_external_link ?? 0;
        $donation_posts->external_link = $donation_posts->external_link ?? null;

        // Decode donation_designation JSON
        $donationDesignations = json_decode($donation_posts->donation_designation, true);
        $donation_posts->donation_designation = $donationDesignations ?? [];



        return view('admin.donations.donation_posts_add', compact(
            'donation_posts',
            'image',
            'donation_categories',
            'organizations',
            'users'
        ));
    }

    public function update(Request $request, $id)
    {
        $user = auth()->user();
        $userId = $user->id;
        $organizationId = $request->organization;
        $donation_posts = DonationPost::findOrFail($id);

        $request->validate([
            'title' => 'required',
            'donation_category_id' => 'required',
            'expired_date' => 'nullable|date',
            'description' => 'required|min:50',
            'user' => 'nullable|integer|exists:users,id',
            'organization' => 'nullable|integer|exists:directories,id',
            'intervals' => 'nullable|array', // Validate intervals
            'intervals.*' => 'string', // Validate each interval
            'supported_document' => 'nullable|file|mimes:pdf,doc,docx,xlsx,csv,txt,pptx|max:2048', // Validation for supported documents
            'external_link' => 'nullable|url', // Validate external link
            'donation_designation' => 'nullable|array', // Validate donation designations
            'donation_designation.*' => 'nullable|string', // Validate each designation

        ]);

        // Handle the expired_date field
        $expiredDateInput = $request->input('expired_date');
        if ($expiredDateInput) {
            // If expired_date is provided, format it as Y-m-d H:i:s for datetime column
            $expiredDate = \DateTime::createFromFormat('m/d/Y', $expiredDateInput);
            if ($expiredDate === false) {
                return response()->json(['error' => 'Invalid date format'], 400);
            }
            $formattedExpiredDate = $expiredDate->format('Y-m-d H:i:s');
        } else {
            // If expired_date is not provided, set it to null
            $formattedExpiredDate = null;
        }

        $donation_posts->update([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'donation_category_id' => $request->input('donation_category_id'),
            'expired_date' => $formattedExpiredDate, // Store the nullable expired date
            'target' => (!empty($request->input('target'))) ? $request->input('target') : 0,
            'last_edited_by' => $user->id,
            'user_id' => $userId,
            'directory_id' => $organizationId,
            'active' => $request->input('active') ?? 0,
            'is_external_link' => $request->has('is_external_link') ? 1 : 0, // Save the checkbox value
            'external_link' => $request->input('external_link'), // Save the external link
            'donation_designation' => json_encode($request->input('donation_designation', [])), // Save donation designations

        ]);

        // Process intervals and their custom amounts
        $tabData = [];
        $rowCounter = 1; // Counter for row keys
        $intervals = $request->input('intervals', []); // Get selected intervals

        foreach ($intervals as $interval) {
            $amountKey = strtolower(str_replace(' ', '_', $interval)) . '_amount';
            $detailsKey = strtolower(str_replace(' ', '_', $interval)) . '_details';

            $amounts = $request->input($amountKey, []);
            $details = $request->input($detailsKey, []);

            foreach ($amounts as $index => $amount) {
                if (!empty($amount)) {
                    $tabData[$interval]["row_{$rowCounter}"] = [
                        'amount' => $amount,
                        'details' => $details[$index] ?? '',
                    ];
                    $rowCounter++; // Increment row key counter
                }
            }
        }

        // Encode tab data and update
        $customAmountJson = json_encode($tabData);
        $donation_posts->update([
            'custom_amount' => $customAmountJson,
        ]);


        $images = [];
        if ($donation_posts->image && $request->old) {
            $updatedImgs = $request->old;
            $images = json_decode($donation_posts->image);
            $images = array_map(function ($key, $value) use ($updatedImgs) {
                if (in_array(($key + 1), $updatedImgs)) {
                    return $value;
                } else {
                    (new AppHelper)->deleteFile($value);
                }
            }, array_keys($images), $images);
        }
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($images, $imageName);
            }
        }
        $donation_posts->update([
            'image' => json_encode(array_values(array_filter($images)))
        ]);

        // Process supported_document
        if ($request->hasFile('supported_document')) {
            $document = $request->file('supported_document');
            $documentPath = $document->store('documents', 'public');

            // Delete the old document if it exists
            if ($donation_posts->supported_document) {
                \Storage::disk('public')->delete($donation_posts->supported_document);
            }

            $donation_posts->update([
                'supported_document' => $documentPath,
            ]);
        }

        return redirect()->route('donation_posts.index')
            ->with('success', 'DonationPost updated successfully');
    }

    public function destroy($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        $donation_posts = DonationPost::find($id);
        // } else {
        // $donation_posts = DonationPost::where('user_id', $userId)->where('id', $id)->first();
        // }

        $payments = $donation_posts->payments ?? [];
        if (count($payments) != 0) {
            return redirect()->route('donation_posts.index')
                ->with('warning', 'Could not delete!, This post has payment/s already');
        }
        $donation_posts->delete();
        return redirect()->route('donation_posts.index')
            ->with('success', 'DonationPost deleted successfully');
    }

    public function categoryIndex()
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        $categories = DonationCategory::where('user_id', $userId)->with('donation_posts', 'user', 'added_by_user', 'last_edited_by_user')->get();

        return view('admin.donations.donation_categories', compact('categories'))->with('i', 1);
        ;
    }

    public function categoryCreate(DonationCategory $category)
    {
        return view('admin.donations.donation_categories_add', compact('category'));
    }

    public function categoryStore(Request $request)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        $request->validate([
            'name' => 'required',
            'description' => 'required'
        ]);

        $category = DonationCategory::create([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'user_id' => $userId,
            'added_by' => $user->id,
        ]);

        return redirect()->route('donation_categories.index')
            ->with('success', 'Category created successfully.');
    }


    public function categoryEdit(DonationCategory $category)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        //!User::isAccessByAdmin() && 
        if ($category->user_id != $userId) {
            return abort(403, 'you dont have access to this page');
        }
        return view('admin.donations.donation_categories_add', compact('category'));
    }

    public function categoryUpdate(Request $request, DonationCategory $category)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        //!User::isAccessByAdmin() && 
        if ($category->user_id != $userId) {
            return abort(403, 'you dont have access to this page');
        }
        $request->validate([
            'name' => 'required',
            'description' => 'required'
        ]);

        $category->update([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'last_edited_by' => $user->id
        ]);

        return redirect()->route('donation_categories.index')
            ->with('success', 'Category updated successfully');
    }

    public function categoryDestroy(DonationCategory $category)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        // if (!User::isAccessByAdmin() && $category->user_id != $userId) {
        //     return abort(403, 'you dont have access to this page');
        // }
        $donations = $category->donation_posts;
        if (count($donations) != 0) {
            return redirect()->route('donation_categories.index')
                ->with('warning', 'This category not empty');
        }

        $category->delete();

        return redirect()->route('donation_categories.index')
            ->with('success', 'Category deleted successfully');
    }
}
