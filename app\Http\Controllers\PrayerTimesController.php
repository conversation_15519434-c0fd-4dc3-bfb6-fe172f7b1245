<?php

namespace App\Http\Controllers;

use App\Models\Kutbaah;
use App\Models\PrayerTimes;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

class PrayerTimesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('prayer_times_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        try {
            $month = $request->get('month');
            $prayerTimes = collect();

            // Default to current month if not passed
            $currentMonth = $month ? Carbon::createFromFormat('m-Y', $month) : Carbon::now();
            $monthYear = $currentMonth->format('m-Y');

            // Filter records based on the `d-m-Y` format stored as string
            $prayerTimes = PrayerTimes::whereRaw("DATE_FORMAT(STR_TO_DATE(`date`, '%d-%m-%Y'), '%m-%Y') = ?", [$monthYear])->orderByRaw("STR_TO_DATE(`date`, '%d-%m-%Y')")->get();
            $khutbaahTimes = Kutbaah::all();
            return view('admin.prayer.index', compact('prayerTimes', 'khutbaahTimes'));

        } catch (\Exception $e) {
            Log::error('Error fetching prayer times: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => 'Failed to load prayer times. Please try again later.'], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try{
            return view('admin.prayer.create');
        } catch(Exception $e){
            Log::error('Error to add records:'. $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load create page. Please try again later.');
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $validator = Validator::make($request->all(), [
            'date' => 'required',
            'sunrise' => 'required',
            'fajr' => 'required',
            'fajr_iqama' => 'required',
            'dhuhar' => 'required',
            'dhuhar_iqama' => 'required',
            'asr' => 'required',
            'asr_iqama' => 'required',
            'magrib' => 'required',
            'magrib_iqama' => 'required',
            'isha' => 'required',
            'isha_iqama' => 'required',
            'status' => 'required|in:0,1',
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        try {
            PrayerTimes::create([
                'date' => $request->date,
                'sunrise' => $request->sunrise,
                'fajr' => $request->fajr,
                'fajr_iqama' => $request->fajr_iqama,
                'dhuhar' => $request->dhuhar,
                'dhuhar_iqama' => $request->dhuhar_iqama,
                'asr' => $request->asr,
                'asr_iqama' => $request->asr_iqama,
                'magrib' => $request->magrib,
                'magrib_iqama' => $request->magrib_iqama,
                'isha' => $request->isha,
                'isha_iqama' => $request->isha_iqama,
                'status' => $request->status,
                'added_by' => $user->id,
            ]);
            return redirect()->route('prayer_times.index')->with('success', 'Prayer times added successfully.');
        } catch (\Exception $e) {
            Log::error('Error storing prayer times: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to add prayer times. Please try again.')->withInput();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try{
            $prayerTime = PrayerTimes::findOrFail($id);
            return view('admin.prayer.edit', compact('prayerTime'));
        } catch (Exception $e){
            Log::error('Error to edit records:'. $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load edit page. Please try again later.');
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $user = auth()->user();
        $validator = Validator::make($request->all(), [
            'date' => 'required',
            'sunrise' => 'required',
            'fajr' => 'required',
            'fajr_iqama' => 'required',
            'dhuhar' => 'required',
            'dhuhar_iqama' => 'required',
            'asr' => 'required',
            'asr_iqama' => 'required',
            'magrib' => 'required',
            'magrib_iqama' => 'required',
            'isha' => 'required',
            'isha_iqama' => 'required',
            'status' => 'required|in:0,1',
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        try {
            $prayerTime = PrayerTimes::findOrFail($id);
            $prayerTime->update([
                'date' => $request->date,
                'sunrise' => $request->sunrise,
                'fajr' => $request->fajr,
                'fajr_iqama' => $request->fajr_iqama,
                'dhuhar' => $request->dhuhar,
                'dhuhar_iqama' => $request->dhuhar_iqama,
                'asr' => $request->asr,
                'asr_iqama' => $request->asr_iqama,
                'magrib' => $request->magrib,
                'magrib_iqama' => $request->magrib_iqama,
                'isha' => $request->isha,
                'isha_iqama' => $request->isha_iqama,
                'status' => $request->status,
                'added_by' => $user->id,
            ]);
            return redirect()->route('prayer_times.index')->with('success', 'Prayer times updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating prayer times: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update prayer times. Please try again.')->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (!Auth::user()->hasRole('admin')) {
            return response()->json(['error' => 'Invalid Access'], 403);
        }
        $prayerTime = PrayerTimes::find($id);
        if (!$prayerTime) {
            return response()->json(['error' => 'Prayer time not found'], 404);
        }
        try {
            $prayerTime->delete();
            return response()->json(['success' => 'Prayer time deleted successfully.']);
        } catch (\Exception $e) {
            Log::error('Error deleting prayer time: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete prayer time. Please try again.'], 500);
        }
    }

    // *******************Index-Order-Set*******************
    public function orderPrayerTimeProcess(Request $request)
    {
        $this->validate($request, [
            'positions' => 'required|array',
        ]);
        $positions = $request->positions;
        foreach ($positions as $position) {
            if (isset($position['id']) && isset($position['position'])) {
                PrayerTimes::where('id', $position['id'])->update(['order' => $position['position']]);
            }
        }
    }
    // *******************Index-Order-Set*******************

    // *******************Khutbaah-Create*******************
    public function kutbahCreate()
    {
        try{
            return view('admin.prayer.kutbahAdd');
        }catch (Exception $e){
            Log::error('Error to create khutbaah:'. $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load add page. Please try again later.');
        }
    }
    // *******************Khutbaah-Create*******************

    // *******************Khutbaah-Store*******************
    public function kutbahStore(Request $request)
    {
        try {
            $request->validate([
                'title' => 'required|string',
                'date'  => 'required',
                'day'   => 'required|string',
                'time'  => 'required',
            ]);
            Kutbaah::create([
                'title' => $request->title,
                'date'  => $request->date,
                'day'   => $request->day,
                'time'  => $request->time,
            ]);
            return redirect()->route('prayer_times.index')->with('success', 'Khutbaah added successfully!');
        } catch (\Exception $e) {
            Log::error('Error adding Khutbaah: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to add khutbaah. Please try again later.');
        }
    }
    // *******************Khutbaah-Store*******************

    // *******************Khutbaah-Edit*******************
    public function khutbahEdit($id)
    {
        try{
            $kutbah = Kutbaah::findOrFail($id);
            return view('admin.prayer.kutbahEdit', compact('kutbah'));
        }catch (Exception $e){
            Log::error('Error to edit khutbaah:'. $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load edit page. Please try again later.');
        }
    }
    // *******************Khutbaah-Edit*******************

    // *******************Khutbaah-Update*******************
    public function khutbahUpdate(Request $request, $id)
    {
        try {
            $request->validate([
                'title' => 'required|string',
                'date'  => 'required',
                'day'   => 'required|string',
                'time'  => 'required',
            ]);

            $kutbah = Kutbaah::findOrFail($id);
            $kutbah->update([
                'title' => $request->title,
                'date'  => $request->date,
                'day'   => $request->day,
                'time'  => $request->time,
            ]);
            return redirect()->route('prayer_times.index')->with('success', 'Khutbaah updated successfully!');
        } catch (\Exception $e) {
            Log::error('Error updating Khutbaah: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update khutbaah. Please try again later.');
        }
    }
    // *******************Khutbaah-Update*******************
}
