<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use \App\Models\RefundRequest;
use \App\Models\Setting;
use \App\Models\OrderChild;

class RefundRequestsController extends Controller
{
    public function requests()
    {
        $refunds = RefundRequest::orderBy('id', 'desc')->get();
        return view('admin.refunds.requests', compact('refunds'));
    }

    public function requestsDetails($id)
    {
        $refundDetail = RefundRequest::find($id);
        $currency = Setting::find(1)->currency_sybmol;
        $paidAmount = OrderChild::where('order_id', $refundDetail->order->id)->whereHas('ticket', function ($query) {
            $query->where('type', 'paid');
        })->sum('order_child.price');
        //  = $refundDetail->pluck('ticket_id')->toArray();
        $refundRequestIdsArray = (isset($refundDetail->ticket_id) && !empty($refundDetail->ticket_id)) ? [$refundDetail->ticket_id] : [];
        return view('admin.refunds.requestsDetail', compact('refundDetail', 'paidAmount', 'currency', 'refundRequestIdsArray'));
    }

    public function acceptRefund($refundId)
    {
        if (!is_numeric($refundId))
            dd('Invalid Refund Details');
        $refundDetail = RefundRequest::find($refundId);
        if (isset($refundDetail->refund_status) && $refundDetail->refund_status != '0') {
            return redirect(url('/refund-requests'))->with('error_status', 'The necessary steps for the refund requests have already been completed.');
        }
        $paidAmount = OrderChild::where('order_id', $refundDetail->order->id)->whereHas('ticket', function ($query) {
            $query->where('type', 'paid');
        })->sum('order_child.price');
        $refundableAmount = 0;
        if (isset($refundDetail->id)) {
            foreach ($refundDetail->order->orderchild as $item) {
                if (
                    $item->ticket->type == 'paid' &&
                    ($refundDetail->refund_mode != '1' && $refundDetail->ticket_id == $item->ticket_number) ||
                    $refundDetail->refund_mode == '1'
                ) {
                    $refundableAmount += \App\Models\Ticket::getPaidAmount($item->ticket->price, $paidAmount, $refundDetail->order->coupon_discount);
                }
            }
        }
        dd($refundableAmount);
        if ($refundableAmount > 0 && !empty($refundDetail->order->payment_token)) {
            $url_name = config('custom.PAYMENT_URL') . "/refund.php";
            $data = array(
                "amount" => $refundableAmount,
                "payment_token" => $refundDetail->order->payment_token,
                // "is_debug" => true
            );

            $curl = curl_init($url_name);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt(
                $curl,
                CURLOPT_HTTPHEADER,
                array(
                    'Content-Type: application/json',
                    "Authorization: Basic " . base64_encode(config('custom.PAYMENT_AUTH')),
                )
            );
            $response = curl_exec($curl);
            curl_close($curl);
            $responseDecode = json_decode($response, true);
            if ($responseDecode && $responseDecode['error'] == 1) {
                return redirect(url('/refund-requests'))->with('error_status', $responseDecode['response']);
            } else {
                $refundDetail->refund_status = 1;
                $refundDetail->clv_response = json_encode($responseDecode['response']);
                $refundDetail->clv_refund_id = $responseDecode['response']['id'];
                $refundDetail->save();
                return redirect(url('/refund-requests'))->with('status', 'Successfully Refunded');
            }
        }
        return redirect(url('/refund-requests'))->with('error_status', 'Amount not found.');
    }
    public function declineRefund($refundId)
    {
        if (!is_numeric($refundId))
            dd('Invalid Refund Details');
        $refundDetail = RefundRequest::find($refundId);
        if (isset($refundDetail->refund_status) && $refundDetail->refund_status != '0') {
            return redirect(url('/refund-requests'))->with('error_status', 'The necessary steps for the refund requests have already been completed.');
        }
        $refundDetail->refund_status = 2;
        $refundDetail->save();
        return redirect(url('/refund-requests'))->with('status', 'Successfully Rejected.');
    }
}
