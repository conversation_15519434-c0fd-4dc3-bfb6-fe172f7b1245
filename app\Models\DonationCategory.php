<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DonationCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'user_id',
        'added_by',
        'last_edited_by',
        'active'
    ];

    public function added_by_user()
    {
        return $this->belongsTo(User::class, 'added_by', 'id');
    }

    public function last_edited_by_user()
    {
        return $this->belongsTo(User::class, 'last_edited_by', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function donation_posts()
    {
        return $this->hasMany(DonationPost::class, 'donation_category_id');
    }

    public static function getPublicCategory($donationCategories)
    {
        foreach ($donationCategories as $donationCategory) {
            $donationCategory->post_count = count($donationCategory->donation_posts);
        }
        return $donationCategories;
    }
}
