<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DirectoryType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'status',
        'pins_color',
        'is_featured',
        'image',
        'order',
    ];
    public function directories()
    {
        return $this->belongsToMany(Directory::class, 'directory_type_links', 'type_id', 'directory_id');
    }
    /*
     * return types with count data of directory
     */
    /*
    public static function getDirectoryTypeWithCountOfDirectory()
    {
        return DB::table('directory_types')
            //->leftJoin('directories', 'directory_types.id', '=', 'directories.directory_type')
            ->leftJoin('directories', function ($join) {
                $join->on('directory_types.id', '=', 'directories.directory_type')
                    ->where('directories.status', '=', 1); // Apply status filter only for joined records
            })
            ->select('directory_types.*', DB::raw('COUNT(directories.id) as directory_count'))
            ->groupBy('directory_types.id', 'directory_types.name', 'directory_types.status', 'directory_types.is_featured', 'directory_types.image', 'directory_types.order')
            ->orderBy('directory_types.order', 'asc')
            ->get();
    }
    */
    public static function getDirectoryTypeWithCountOfDirectory()
    {
        return DB::table('directory_types')
            ->leftJoin('directories', function ($join) {
                $join->whereRaw("
                FIND_IN_SET(directory_types.id,
                REPLACE(REPLACE(REPLACE(directories.directory_type, '[', ''), ']', ''), ' ', ''))
            ")
                    ->where('directories.status', '=', 1);
            })
            ->select(
                'directory_types.*',
                DB::raw('COUNT(directories.id) as directory_count')
            )
            ->groupBy(
                'directory_types.id', 'directory_types.name', 'directory_types.status',
                'directory_types.is_featured', 'directory_types.image', 'directory_types.order'
            )
            ->orderBy('directory_types.order', 'asc')
            ->get();
    }

    public static function getMasajidsWithCount()
    {
        return DB::table('directory_types')->leftJoin('masajids', function ($join) {
                $join->whereRaw("FIND_IN_SET(directory_types.id,REPLACE(REPLACE(REPLACE(masajids.directory_type, '[', ''), ']', ''), ' ', ''))")->where('masajids.status', '=', 1);
            })
            ->where('directory_types.id', 15)
            ->select('directory_types.*', DB::raw('COUNT(masajids.id) as directory_count'))
            ->groupBy('directory_types.id', 'directory_types.name', 'directory_types.status', 'directory_types.is_featured', 'directory_types.image', 'directory_types.order')
            ->orderBy('directory_types.order', 'asc')
            ->get();
    }

    public static function getSchoolsWithCount()
    {
        return DB::table('directory_types')->leftJoin('schools', function ($join) {
                $join->whereRaw("FIND_IN_SET(directory_types.id,REPLACE(REPLACE(REPLACE(schools.directory_type, '[', ''), ']', ''), ' ', ''))")->where('schools.status', '=', 1);
            })
            ->where('directory_types.id', 16)
            ->select('directory_types.*', DB::raw('COUNT(schools.id) as schools_count'))
            ->groupBy('directory_types.id', 'directory_types.name', 'directory_types.status', 'directory_types.is_featured', 'directory_types.image', 'directory_types.order')
            ->orderBy('directory_types.order', 'asc')
            ->get();
    }
}
