<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;

class DonationPayment extends Model
{
    use HasFactory;

    const PS_SUCCESS = 1;
    const PS_FAIL = 0;
    const PS_PENDING = 2;
    protected $fillable = [
        'name',
        'donation_post_id',
        'email',
        'description',
        'amount',
        'status',
        'payment_ref',
        'is_recuring',
        'added_by',
        'last_edited_by',
        'donated_at'
    ];

    public static function getPublic($donatioPayment)
    {
        foreach ($donatioPayment as $dp) {
            $dp->time_ago = Carbon::parse($dp->donated_at)->diffForHumans();
            $postInfo = DonationPost::getPublic([$dp->post]);
            $dp->post = $postInfo[0];
        }
        return $donatioPayment;
    }

    public function post()
    {
        return $this->belongsTo(DonationPost::class, 'donation_post_id');
    }

    public static function getCustomers($donationId)
    {
        return self::select('email', 'name', DB::raw('COUNT(*) AS user_donation_count'), DB::raw('sum(amount) as total_amount')) // Get booking count for each user and event
            ->where('donation_post_id', $donationId)
            ->where('status', DonationPayment::PS_SUCCESS)
            ->where('active', DonationPayment::PS_SUCCESS)
            ->groupBy('email') // Group by event_id and user_id
            ->get()->toArray(); // Execute and retrieve results
    }

    public static function getTotalDonationJsonData($donations)
    {
        $donationRows = $donations->selectRaw('DATE(donated_at) as donated_date, SUM(amount) as total_sum')
            ->where('status', DonationPayment::PS_SUCCESS)
            ->where('active', DonationPayment::PS_SUCCESS)
            ->groupBy('donated_date')
            ->orderBy('donated_date', 'desc')
            ->limit(30)
            ->get()->toArray();
        $donationData = [];
        if (isset($donationRows[0]['donated_date'])) {
            $donationData['label'] = [];
            $donationData['data'] = [];
            foreach ($donationRows as $donationRow) {
                array_push($donationData['data'], $donationRow['total_sum']);
                array_push($donationData['label'], date('Y-m-d', strtotime($donationRow['donated_date'])));
            }
        }
        return $donationData;
    }

    public static function getCustomerJsonData($donationPostId)
    {
        $sqlQuery = "WITH 
        all_dates AS (
            SELECT DISTINCT DATE(donated_at) AS booking_day FROM donation_payments where donation_post_id=$donationPostId and status=" . self::PS_SUCCESS . " and active=" . self::PS_SUCCESS . "
        ),
        first_bookings AS (
            SELECT email, MIN(donated_at) AS first_booking FROM donation_payments where donation_post_id=$donationPostId and status=" . self::PS_SUCCESS . " and active=" . self::PS_SUCCESS . " GROUP BY email
        )
    -- Keep distinct dates
    SELECT 
        a.booking_day,
        IFNULL(COUNT(fb.email), 0) AS new_user_count
    FROM 
        all_dates a
    LEFT JOIN 
        first_bookings fb
    ON 
        a.booking_day = DATE(fb.first_booking)
    GROUP BY 
        a.booking_day
    ORDER BY 
        a.booking_day DESC";
        $results = DB::select($sqlQuery);
        $customerData = [];
        if (isset($results[0]->booking_day)) {
            $customerData['label'] = [];
            $customerData['data'] = [];
            foreach ($results as $result) {
                array_push($customerData['data'], number_format($result->new_user_count));
                array_push($customerData['label'], date('Y-m-d', strtotime($result->booking_day)));
            }
        }
        return $customerData;
    }

    public static function saveDonationDetails($transactionId, $tokenData, $userInfo)
    {
        $payment = new DonationPayment();
        $payment->donation_post_id = $tokenData['post_id'];
        $payment->name = $userInfo['first_name'] . " " . $userInfo['last_name'];
        $payment->email = $userInfo['email'];
        $payment->description = $tokenData['comments'];
        $payment->amount = $tokenData['amount'];
        $payment->status = 1;
        $payment->payment_ref = $transactionId;
        $payment->active = 1;
        $payment->donated_at = date('Y-m-d H:i:s');
        $payment->added_by = $userInfo['user_id'] ?? 0;
        $payment->is_recuring = $userInfo['is_recuring'];
        $payment->designation = $tokenData['designation'];
        $payment->save();
        $post = DonationPost::find($tokenData['post_id']);
        \Mail::to($userInfo['email'])->send(new \App\Mail\DonationReceiptMail($payment, $post));
    }
}
