<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use \App\Models\RefundRequest;

class RefundRequestsController extends Controller
{
    public function requests()
    {
        $refunds = RefundRequest::orderBy('id', 'desc')->get();
        return view('admin.refunds.requests', compact('refunds'));
    }

    public function requestsDetails($id)
    {
        $refundDetail = RefundRequest::find($id);
        return view('admin.refunds.requestsDetail', compact('refundDetail'));

    }
}
