<?php

namespace App\Http\Controllers;

use App\Mail\joinAsOrganizerMail;
use App\Models\Directory;
use App\Models\DirectoryType;
use App\Models\DirectoryFollowup;
use App\Models\ReferalCommission;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use Exception;
use App\Models\Event;
use App\Models\User;
use App\Models\Review;
use App\Models\Ticket;
use App\Models\Coupon;
use App\Models\Tax;
use App\Models\OrderTax;
use App\Models\AppUser;
use App\Models\Category;
use App\Models\Blog;
use App\Models\Faq;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Twilio\Rest\Client;
use App\Models\Order;
use App\Models\Setting;
use App\Models\PaymentSetting;
use App\Models\NotificationTemplate;
use App\Models\EventReport;
use App\Models\OrderChild;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use OneSignal;
use Twilio\Rest\Client as Clients;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Rave;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Redirect;
use Carbon\Carbon;
use App\Mail\ResetPassword;
use App\Mail\TicketBook;
use App\Mail\TicketBookOrg;
use App\Models\Language;
use App\Http\Controllers\FaqController;
use App\Models\Banner;
use App\Models\ContactUs;
use App\Models\Country;
use App\Models\CouponUsageHistory;
use App\Models\Module;
use App\Models\OrganizerPaymentKeys;
use App\Models\Programs;
use App\Models\TagsModel;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Spatie\Permission\Traits\HasRoles;
use Artesaos\SEOTools\Facades\JsonLdMulti;
use Artesaos\SEOTools\Facades\SEOTools;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\JsonLd;
use FontLib\Table\Type\name;
use Illuminate\Support\Facades\Crypt;
use Spatie\Permission\Guard;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Modules\Seatmap\Entities\Rows;
use Modules\Seatmap\Entities\SeatMaps;
use Modules\Seatmap\Entities\Seats;
use stdClass;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Throwable;
use Vonage\Client as VonageClient;
use Vonage\SMS\Message\SMS;
use Vonage\SMS\Message\SMSCollection;
use Google\Service\Oauth2;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class FrontendController extends Controller
{
    protected $googleClient;
    public function __construct(Request $request)
    {
        if (strpos($request->path(), "social-login-process") === false) {
            session()->forget('ref_code');
        }
        if (env('DB_DATABASE') != null) {
            (new AppHelper)->mailConfig();
            (new AppHelper)->eventStatusChange();
        }

        $this->googleClient = new \Google\Client();
        $this->googleClient->setClientId(config('custom.GCI_CLIENT_ID'));
        $this->googleClient->setClientSecret(config('custom.GCI_CLIENT_SECRET'));
        $this->googleClient->addScope('email');
        $this->googleClient->addScope('profile');
    }

    public function fetchStates()
    {
        $directories = Directory::where('address_type', 'offline')->get();
        foreach ($directories as $directory) {
            if (!empty($directory->address)) {
                $address = $directory->constructAddress($directory);
                foreach ($address as $key => $addr) {
                    if (
                        isset($addr['lat']) && isset($addr['long'])
                        && !empty($addr['long'])
                        && !empty($addr['lat'])
                    ) {
                        $addressString = urlencode($addr['street']);
                        $apiKey = 'AIzaSyAMhFpujOTpUfQYBIBMuzn_1viQgojw6Vg';
                        $url = "https://maps.googleapis.com/maps/api/geocode/json?address=$addressString&key=$apiKey";

                        $response = file_get_contents($url);
                        $data = json_decode($response, true);
                        if (isset($data['status']) && $data['status'] == 'OK') {
                            foreach ($data['results'][0]['address_components'] as $component) {
                                if (in_array('administrative_area_level_1', $component['types'])) {
                                    $address[$key]['state'] = $component['long_name']; // Get the state name
                                    break;
                                }
                            }
                        }
                    }
                }
                $directory->address = $address;
                $directory->save();
            }
        }
    }

    public function followedOrganizations()
    {
        if (!Auth::guard('appuser')->check()) {
            return redirect(url('/user/login'));
        }
        $user = Auth::guard('appuser')->user();
        $directoryFollowUps = DirectoryFollowup::where('user_id', $user->id)->pluck('directory_id')->toArray();
        $directories = [];
        if (!empty($directoryFollowUps)) {
            $directories = Directory::whereIn('id', $directoryFollowUps)->get();
        }
        $user = \Auth::guard('appuser')->user();
        return view('frontend.followup-organizations', compact('directories', 'user'));
    }

    public function followOrganizationProcess($directoryId)
    {
        if (!Auth::guard('appuser')->check()) {
            session()->flash('directory_input', ['directory_id' => $directoryId]);
            return redirect(url('/user/login'));
        }
        $user = Auth::guard('appuser')->user();
        $directory = Directory::find($directoryId);
        $directoryFollowUp = DirectoryFollowup::where('directory_id', $directoryId)
            ->where('user_id', $user->id)
            ->first();
        if (!isset($directoryFollowUp->id) || empty($directoryFollowUp->id)) {
            $dirFollowUp = new DirectoryFollowup();
            $dirFollowUp->directory_id = $directoryId;
            $dirFollowUp->user_id = $user->id;
            $dirFollowUp->save();
            session()->flash('success', 'by following up ' . $directory->name . ' page you will
            receive notifications about thier events, newsletters,  and causes update');
        }
        return redirect(url('/organizations/' . $directory->id . '/' . str_replace(' ', '-', $directory->name)));
    }

    public function unfollowOrganizationProcess($followId)
    {
        $followRecords = DirectoryFollowup::find($followId);
        if (!Auth::guard('appuser')->check()) {
            if (isset($followRecords->id)) {
                session()->flash('directory_input', ['directory_id' => $followRecords->directory_id]);
            }
            return redirect(url('/user/login'));
        }
        $directory = Directory::find($followRecords->directory_id);
        if (isset($followRecords->id)) {
            $user = Auth::guard('appuser')->user();
            if ($user->id == $followRecords->user_id) {
                $followRecords->delete();
                session()->flash('success', 'By unfollowing this page, you will no longer
                receive notifications about their events, newsletters, and cause updates.');
            }
        }
        return redirect(url('/organizations/' . $directory->id . '/' . str_replace(' ', '-', $directory->name)));
    }

    public function getLocation()
    {
        if (session()->get('lat')) {
            $latitude = session()->get('lat');
            $longitude = session()->get('long');
            $city = session()->get('city');
            $state = session()->get('state');
            $country = session()->get('country');
            if (!session()->get('city')) {
                try {
                    $url = "https://maps.googleapis.com/maps/api/geocode/json?latlng=$latitude,$longitude&key=AIzaSyAMhFpujOTpUfQYBIBMuzn_1viQgojw6Vg";
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    $response = curl_exec($ch);
                    curl_close($ch);
                    $responseData = json_decode($response, true);
                    if ($responseData['status'] == 'OK') {
                        foreach ($responseData['results'][0]['address_components'] as $component) {
                            if (in_array('locality', $component['types'])) {
                                $city = $component;
                                session()->put('city', $city);
                            }
                            if (in_array('administrative_area_level_1', $component['types'])) {
                                $state = $component;
                                session()->put('state', $state);
                            }
                            if (in_array('country', $component['types'])) {
                                $country = $component;
                                session()->put('country', $country);
                            }
                        }
                        return response()->json([
                            'city' => session()->get('city'),
                            'state' => session()->get('state'),
                            'country' => session()->get('country')
                        ]);
                    }
                } catch (Exception $ex) {
                    return response()->json([]);
                }
            } else {
                return response()->json([
                    'city' => session()->get('city'),
                    'state' => session()->get('state'),
                    'country' => session()->get('country')
                ]);
            }
        }
        return response()->json([]);
    }

    public function saveCordinates(Request $request)
    {
        if ($request->has('denied')) {
            session()->forget('lat');
            session()->forget('long');
            session()->forget('city');
            session()->forget('state');
            session()->forget('country');
        } else {
            session()->put('lat', $request->latitude);
            session()->put('long', $request->longitude);
        }
    }

    public function confirmPurchaseGuest(Request $request)
    {
        if ($request->session()->has('success') && $request->session()->get('success') == '1') {
            return view('frontend.guest-confirm-purchase');
        }
        return redirect(url('/'));
    }

    public function socialLoginProcess($userType, Request $request)
    {
        $this->validate($request, [
            'code' => 'required',
            'scope' => 'required',
        ]);
        if ($userType == 'organizer') {
            $this->googleClient->setRedirectUri(config('custom.GCI_ORGANIZER_REDIRECT_URL'));
        } else {
            $this->googleClient->setRedirectUri(config('custom.GCI_USER_REDIRECT_URL'));
        }
        $ref_code = session('ref_code') ?? "";
        if (!empty($ref_code)) {
            session()->forget('ref_code');
        }
        $token = $this->googleClient->fetchAccessTokenWithAuthCode(urldecode($request->code));
        if (isset($token['access_token'])) {
            $this->googleClient->setAccessToken($token);
            $oauth2 = new Oauth2($this->googleClient);
            $googleUserInfo = $oauth2->userinfo->get();
            if ($googleUserInfo && isset($googleUserInfo->email) && !empty($googleUserInfo->email)) {
                $payload = (array) $googleUserInfo;
                if ($userType == 'organizer') {
                    $user = User::where('email', $payload['email'])->first();
                    if ($user === null) {
                        $data['password'] = Hash::make(rand(111111111, 999999999));
                        $data['image'] = "defaultuser.png";
                        $data['status'] = 0;
                        $data['language'] = Setting::first()->language;
                        $data['phone'] = '';
                        $data['is_verify'] = 1;
                        $data['first_name'] = $payload['givenName'];
                        $data['last_name'] = $payload['familyName'];
                        $data['email'] = $payload['email'];
                        if (!empty($ref_code)) {
                            $referal = User::where('referal_code', $ref_code)->first();
                            if (isset($referal->id)) {
                                $data['refered_by'] = $referal->id;
                            }
                        }
                        $user = User::create($data);
                        $user->assignRole('Organizer');
                        $token = Password::createToken($user);
                        $user->sendPasswordResetNotification($token);
                    }
                    Auth::loginUsingId($user->id);
                    return redirect('/organization/home');
                } else {
                    $user = AppUser::where('email', $payload['email'])->first();
                    if ($user === null) {
                        $data['password'] = Hash::make(rand(111111111, 999999999));
                        $data['image'] = "defaultuser.png";
                        $data['status'] = 1;
                        $data['provider'] = "LOCAL";
                        $data['language'] = Setting::first()->language;
                        $data['phone'] = '';
                        $data['is_verify'] = 1;
                        $data['name'] = $payload['givenName'];
                        $data['last_name'] = $payload['familyName'];
                        $data['email'] = $payload['email'];
                        $user = AppUser::create($data);
                        $token = Password::createToken($user);
                        $user->sendPasswordResetNotification($token);
                    }
                    Auth::guard('appuser')->login($user);
                    if (isset($request->state) && !empty($request->state)) {
                        $state = json_decode($request->state, true);
                        if (isset($state['directory_id']) && !empty($state['directory_id'])) {
                            return redirect('/user/follow-organization/' . $state['directory_id']);
                        }
                    }
                    return redirect('/');
                }
            } else {
                return redirect('/user/login')->with('error_msg', 'Only authorized person can login');
            }
        } else {
            return redirect('/user/login')->with('error_msg', 'Only authorized person can login');
        }
    }

    public function home()
    {
        if (env('DB_DATABASE') == null) {
            return view('admin.frontpage');
        } else {
            $setting = Setting::first(['app_name', 'logo']);

            SEOMeta::setTitle($setting->app_name . ' - Home' ?? env('APP_NAME'))
                ->setDescription('This is home page')
                ->setCanonical(url()->current())
                ->addKeyword(['home page', $setting->app_name, $setting->app_name . ' Home']);

            OpenGraph::setTitle($setting->app_name . ' - Home' ?? env('APP_NAME'))
                ->setDescription('This is home page')
                ->setUrl(url()->current());

            JsonLdMulti::setTitle($setting->app_name . ' - Home' ?? env('APP_NAME'));
            JsonLdMulti::setDescription('This is home page');
            JsonLdMulti::addImage($setting->imagePath . $setting->logo);

            SEOTools::setTitle($setting->app_name . ' - Home' ?? env('APP_NAME'));
            SEOTools::setDescription('This is home page');
            SEOTools::opengraph()->setUrl(url()->current());
            SEOTools::setCanonical(url()->current());
            SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);


            $timezone = Setting::find(1)->timezone;
            $date = Carbon::now($timezone);
            if (session()->get('lat')) {
                $latitude = session()->get('lat');
                $longitude = session()->get('long');
                $radius = 100; // 100 miles
                $earthRadius = 3959; // Earth's radius in miles
                $city = session()->get('city');
                $state = session()->get('state');
                $country = session()->get('country');
                if (!session()->get('city')) {
                    try {
                        $url = "https://maps.googleapis.com/maps/api/geocode/json?latlng=$latitude,$longitude&key=AIzaSyAMhFpujOTpUfQYBIBMuzn_1viQgojw6Vg";
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $url);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                        $response = curl_exec($ch);
                        curl_close($ch);
                        $responseData = json_decode($response, true);
                        if ($responseData['status'] == 'OK') {
                            foreach ($responseData['results'][0]['address_components'] as $component) {
                                if (in_array('locality', $component['types'])) {
                                    $city = $component;
                                    session()->put('city', $city);
                                }
                                if (in_array('administrative_area_level_1', $component['types'])) {
                                    $state = $component;
                                    session()->put('state', $state);
                                }
                                if (in_array('country', $component['types'])) {
                                    $country = $component;
                                    session()->put('country', $country);
                                }
                            }
                        }
                    } catch (Exception $ex) {
                    }
                }
                // echo $city . " == " . $state . " == " . $country;
                // die;
                $events = Event::with(['category:id,name'])
                    ->selectRaw("
                    *, ($earthRadius * acos( cos( radians(?) ) * cos( radians( lat ) ) *
                    cos( radians( lang ) - radians(?) ) + sin( radians(?) ) *
                    sin( radians( lat ) ) ) ) AS distance", [$latitude, $longitude, $latitude])
                    ->where([
                        ['status', 1],
                        ['is_deleted', 0],
                        ['event_status', 'Pending'],
                        ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                        ['end_time', '>', $date->format('Y-m-d H:i:s')]
                    ])
                    ->having('distance', '<', $radius)
                    ->orderBy('distance')
                    ->orderBy('start_time', 'desc')
                    ->orderBy('order', 'asc')->limit(4)
                    ->get();
                if ($events->isEmpty()) {
                    if (!empty($city)) {
                        $events = Event::with(['category:id,name'])
                            ->where('address', 'like', '%, ' . $city['long_name'] . ',%')
                            ->orWhere('address', 'like', '%, ' . $city['short_name'] . ',%')
                            ->where([
                                ['status', 1],
                                ['is_deleted', 0],
                                ['event_status', 'Pending'],
                                ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                                ['end_time', '>', $date->format('Y-m-d H:i:s')]
                            ])
                            ->orderBy('start_time', 'desc')->orderBy('order', 'asc')->limit(4)->get();
                    } else if (!empty($state)) {
                        $events = Event::with(['category:id,name'])
                            ->where('address', 'like', '%, ' . $state['long_name'] . '%')
                            ->orWhere('address', 'like', '%, ' . $state['short_name'] . '%')
                            ->where([
                                ['status', 1],
                                ['is_deleted', 0],
                                ['event_status', 'Pending'],
                                ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                                ['end_time', '>', $date->format('Y-m-d H:i:s')]
                            ])
                            ->orderBy('start_time', 'desc')->orderBy('order', 'asc')->limit(4)->get();
                    } else if (!empty($country)) {
                        $events = Event::with(['category:id,name'])
                            ->where('address', 'like', '%, ' . $country['long_name'] . '%')
                            ->orWhere('address', 'like', '%, ' . $country['short_name'] . '%')
                            ->where([
                                ['status', 1],
                                ['is_deleted', 0],
                                ['event_status', 'Pending'],
                                ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                                ['end_time', '>', $date->format('Y-m-d H:i:s')]
                            ])
                            ->orderBy('start_time', 'desc')->orderBy('order', 'asc')->limit(4)->get();
                    }
                }
            } else {
                $events = Event::with(['category:id,name'])
                    ->where([
                        ['status', 1],
                        ['is_deleted', 0],
                        ['event_status', 'Pending'],
                        ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                        ['end_time', '>', $date->format('Y-m-d H:i:s')]
                    ])
                    ->orderBy('start_time', 'desc')->orderBy('order', 'asc')->limit(4)->get();
            }
            // dd($events);
            // $organizer = User::role('Organizer')->orderBy('id', 'DESC')->get();
            $category = Category::where('status', 1)->orderBy('id', 'DESC')->get();
            // $blog = Blog::with(['category:id,name'])->where('status', 1)->orderBy('id', 'DESC')->get();
            $constructedEvents = [];
            foreach ($events as $event) {
                $categories = $event->categories->pluck('name') ?? [];
                if (!empty($categories) && is_array($categories)) {
                    foreach ($categories as $category) {
                        if (!isset($constructedEvents[$category])) {
                            $constructedEvents[$category] = [];
                        }
                        array_push($constructedEvents[$category], $event);
                    }
                }
                // $value->total_ticket = Ticket::where([['event_id', $value->id], ['is_deleted', 0], ['status', 1]])->sum('quantity');
                // $value->sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
                // $value->available_ticket = $value->total_ticket - $value->sold_ticket;
            }
            // $banner = Banner::with('event')->where('status', 1)->get();
            $user = Auth::guard('appuser')->user();
            $donationCampaigns = \App\Models\DonationPost::where('active', 1)->orderBy('created_at', 'desc')->limit(4)->get();
            $directories = Directory::where('status', 1)->orderBy('order', 'asc')->get();
            $constructedDirectories = [];
            foreach ($directories as $directory) {
                $types = $directory->types;
                if (isset($types) && !empty($types)) {
                    foreach ($types as $type) {
                        if (isset($type->is_featured) && $type->is_featured != '1') {
                            continue;
                        }
                        if (!isset($constructedDirectories[$type->name . "/-/" . $type->id])) {
                            $constructedDirectories[$type->name . "/-/" . $type->id] = [];
                        }
                        array_push($constructedDirectories[$type->name . "/-/" . $type->id], $directory);
                    }
                }
            }
            // return view('frontend.home-old', compact('events', 'organizer', 'category', 'blog', 'banner', 'user'));
            return view('frontend.home', compact('events', 'donationCampaigns', 'user', 'constructedEvents', 'constructedDirectories'));
        }
    }
    public function login()
    {
        if (Auth::guard('appuser')->check() || Auth::check()) {
            return redirect()->back();
        }
        $setting = Setting::first(['app_name', 'logo']);
        SEOMeta::setTitle($setting->app_name . ' - Login' ?? env('APP_NAME'))
            ->setDescription('This is login page')
            ->setCanonical(url()->current())
            ->addKeyword(['login page', $setting->app_name, $setting->app_name . ' Login', 'sign-in page', $setting->app_name . ' sign-in']);

        OpenGraph::setTitle($setting->app_name . ' - Login' ?? env('APP_NAME'))
            ->setDescription('This is login page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - Login' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is login page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - Login' ?? env('APP_NAME'));
        SEOTools::setDescription('This is login page');
        SEOTools::opengraph()->addProperty(
            'keywords',
            [
                'login page',
                $setting->app_name,
                $setting->app_name . ' Login',
                'sign-in page',
                $setting->app_name . ' sign-in'
            ]
        );
        SEOTools::opengraph()->addProperty('image', $setting->imagePath . $setting->logo);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        $this->googleClient->setRedirectUri(config('custom.GCI_USER_REDIRECT_URL'));
        if (!empty(session('directory_input'))) {
            $this->googleClient->setState(json_encode(session('directory_input')));
        } else if (!empty(old('directory_id'))) {
            $this->googleClient->setState(json_encode(['directory_id' => old('directory_id')]));
        }
        $data['user_gci_login_url'] = $this->googleClient->createAuthUrl();
        $this->googleClient->setRedirectUri(config('custom.GCI_ORGANIZER_REDIRECT_URL'));
        if (!empty(session('directory_input'))) {
            $this->googleClient->setState(json_encode(session('directory_input')));
        } else if (!empty(old('directory_id'))) {
            $this->googleClient->setState(json_encode(['directory_id' => old('directory_id')]));
        }
        $data['org_gci_login_url'] = $this->googleClient->createAuthUrl();
        return view('frontend.auth.login')->with($data);
    }
    public function userLogin(Request $request)
    {
        $request->validate([
            'email' => 'bail|required|email',
            'password' => 'bail|required',
        ]);

        $userdata = array(
            'email' => $request->email,
            'password' => $request->password,
        );
        $remember = $request->get('remember');
        if ($request->type == 'user') {
            if (Auth::guard('appuser')->attempt($userdata, $remember)) {
                $user = Auth::guard('appuser')->user();
                $setting = Setting::first(['app_name', 'logo']);
                if ($user->status == 0) {
                    return redirect('user/login?type=user')->with('error_msg', 'Blocked By Admin.')->withInput();
                }
                if (!$setting->user_verify) {
                    if ($request->has('directory_id') && !empty($request->directory_id)) {
                        return redirect()->intended('/user/follow-organization/' . $request->directory_id);
                    } else {
                        return redirect()->intended('/user/profile');
                    }
                } else {
                    if (!$user->is_verify) {
                        $details = [
                            'id' => $user->id,
                        ];
                        \Mail::to($user->email)->send(new \App\Mail\VerifyMail($details));
                        return redirect('user/login?type=user')->with(['success' => "Verification link has been sent to your email. Please visit that link to complete the verification"])->withInput();
                    }
                }
                $this->setLanguage($user);
            } else {
                // return redirect('user/login?type=user')->with('error_msg', 'Only authorized person can login.');
                return redirect('user/login?type=user')->with('error_msg', 'Incorrect username/password.')->withInput();
            }
        }
        if ($request->type == 'org') {
            if (Auth::attempt($userdata, $remember)) {
                if (Auth::user()->hasRole('Organizer')) {
                    $user = Auth::user();
                    if ($user->status == 1) {
                        if ($user->is_verify == 0) {
                            Auth::logout();
                            return redirect('user/login?type=org')->with('error_msg', 'Only verified persons can access the portal please check your email to verify.')->withInput();
                        } else {
                            $this->setLanguage($user);
                            return redirect()->intended('organization/home');
                        }
                    } else {
                        Auth::logout();
                        return redirect('user/login?type=org')->with('error_msg', 'Verification is in pending status please wait for further notice.')->withInput();
                    }
                } else {
                    Auth::logout();
                    return redirect('user/login?type=org')->with('error_msg', 'Only authorized person can login.')->withInput();
                }
            } else {
                return redirect('user/login?type=org')->with('error_msg', 'Incorrect username/password.')->withInput();
            }
        }
    }

    public function userLogout(Request $request)
    {
        if (Auth::guard('appuser')->check()) {
            Auth::guard('appuser')->logout();
            return redirect('/user/login');
        }
    }
    public function register(Request $request)
    {
        if (Auth::guard('appuser')->check() || Auth::check()) {
            return redirect()->back();
        }
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - Register' ?? env('APP_NAME'))
            ->setDescription('This is register page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'register page',
                $setting->app_name,
                $setting->app_name . ' Register',
                'sign-up page',
                $setting->app_name . ' sign-up'
            ]);

        OpenGraph::setTitle($setting->app_name . ' - Register' ?? env('APP_NAME'))
            ->setDescription('This is register page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - Register' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is register page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - Register' ?? env('APP_NAME'));
        SEOTools::setDescription('This is register page');
        SEOTools::opengraph()->addProperty(
            'keywords',
            [
                'register page',
                $setting->app_name,
                $setting->app_name . ' Register',
                'sign-up page',
                $setting->app_name . ' sign-up'
            ]
        );
        SEOTools::opengraph()->addProperty('image', $setting->imagePath . $setting->logo);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        $logo = Setting::find(1)->logo;
        $phone = Country::get();
        $ref_code = $request->referer ?? "";
        if (!empty($ref_code)) {
            session(['ref_code' => $ref_code]);
        }
        $this->googleClient->setRedirectUri(config('custom.GCI_USER_REDIRECT_URL'));
        $data['user_gci_login_url'] = $this->googleClient->createAuthUrl();
        $this->googleClient->setRedirectUri(config('custom.GCI_ORGANIZER_REDIRECT_URL'));
        $data['org_gci_login_url'] = $this->googleClient->createAuthUrl();
        $directories = Directory::where('status', 1)->get();
        return view('frontend.auth.register', compact('logo', 'phone', 'ref_code', 'directories'))->with($data);
    }
    public function userRegister(Request $request)
    {
        // dd($request->all());
        // $request->validate([
        //     'name' => 'bail|required',
        //     'last_name' => 'bail|required',
        //     'email' => 'bail|required|email|unique:app_user|unique:users',
        //     'phone' => 'bail|required|numeric',
        //     'password' => 'bail|required|min:6',
        //     'Countrycode' => 'bail|required',
        // ]);
        $rules = [
            'name' => 'bail|required',
            'last_name' => 'bail|required',
            'email' => 'bail|required|email|unique:app_user|unique:users',
            'phone' => 'bail|required|numeric',
            'password' => 'bail|required|min:6',
            'Countrycode' => 'bail|required',
        ];

        // Validate the request
        $validator = Validator::make($request->all(), $rules);
        if (!$validator->fails() && $request->user_type == 'organizer') {
            $rules = [
                'organize' => 'bail|required',
            ];
            $validator = Validator::make($request->all(), $rules);
            if (!$validator->fails() && $request->organize == 'None') {
                $rules = [
                    'organizeInput' => 'bail|required',
                ];
                $validator = Validator::make($request->all(), $rules);
            }
        }
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            if ($request->user_type == 'organizer') {
                return redirect('user/register?type=org')->withInput()->with('errors', $errors);
            } else {
                return redirect('user/register?type=user')->withInput()->with('errors', $errors);
            }
        }

        $verify = Setting::first()->user_verify == 1 ? 0 : 1;
        $data = $request->all();
        $data['password'] = Hash::make($request->password);
        $data['image'] = "defaultuser.png";
        $data['provider'] = "LOCAL";
        $data['language'] = Setting::first()->language;
        if ($request->has('ref_code') && !empty($request->ref_code)) {
            $referal = User::where('referal_code', $request->ref_code)->first();
            if (isset($referal->id)) {
                $data['refered_by'] = $referal->id;
            }
        }
        $data['phone'] = "+" . $request->Countrycode . $request->phone;
        $data['is_verify'] = $verify;
        $data['status'] = 1;
        if ($data['user_type'] == 'organizer') {
            $data['first_name'] = $request->name;
            $data['org_id'] = ($request->organize != 'None') ? $request->organize : 0;
            if (!empty($data['org_id'])) {
                $directory = Directory::find($data['org_id']);
                $email = "";
                if (isset($directory->personal_email) && !empty($directory->personal_email)) {
                    $email = $directory->personal_email;
                }
                if (isset($directory->join_as_organizer) && $directory->join_as_organizer == '1') {
                    $data['status'] = 0;
                }
                $website = "";
                if (!empty($directory->contact_informations)) {
                    $contactInformationArray = json_decode($directory->contact_informations, true);
                    if (isset($contactInformationArray['website']) && !empty($contactInformationArray['website'])) {
                        $website = $contactInformationArray['website'];
                    }
                }
                $domain = "";
                if (!empty($email)) {
                    if (!empty($website)) {
                        $websiteArray = parse_url($website);
                        $emailArray = explode('@', $request->email);
                        if (isset($websiteArray['host']) && !empty($websiteArray['host'])) {
                            $domain = str_replace('www.', '', $websiteArray['host']);
                        }
                        if (isset($emailArray[1]) && !empty($emailArray[1]) && !empty($domain) && $emailArray[1] != $domain) {
                            return redirect()->back()->with('error', 'Organization domain doesnt matched with your email address.');
                        }
                    }
                }
                if ($data['status']) {
                    $data['organization_status'] = 1;
                }
            } else {
                $data['settings'] = json_encode(($request->organize == 'None') ? ['custom_org_name' => $request->organizeInput] : []);
            }
            $user = User::create($data);
            if (isset($directory->id) && !empty($directory->id) && !$data['status']) {
                $data['settings'] = json_encode(['org_id' => $data['org_id']]);
                $data['org_id'] = 0;
                \Mail::to($user->email)->send(new joinAsOrganizerMail($directory, $user));
            }
            $user->assignRole('Organizer');
            OrganizerPaymentKeys::create([
                'organizer_id' => $user->id,
            ]);
        } else {
            $user = AppUser::create($data);
        }
        if ($user->is_verify == 0) {

            if (Setting::first()->verify_by == 'email' && Setting::first()->mail_host != NULL) {
                if ($data['user_type'] == 'organizer') {
                    $details = [
                        'url' => url('organizer/VerificationConfirm/' . $user->id)
                    ];
                } else {
                    $details = [
                        'url' => url('user/VerificationConfirm/' . $user->id)
                    ];
                }
                \Mail::to($user->email)->send(new \App\Mail\VerifyMail($details));
                return redirect('user/login')->with(['success' => "Verification link has been sent to your email. Please visit that link to complete the verification"]);
            }
            if (Setting::first()->verify_by == 'phone') {

                $setting = Setting::first();
                $otp = rand(100000, 999999);
                $to = $user->phone;
                $message = "Your phone verification code is $otp for $setting->app_name.";
                if ($setting->enable_twillio == 1) {
                    $twilio_sid = $setting->twilio_account_id;
                    $twilio_token = $setting->twilio_auth_token;
                    $twilio_phone_number = $setting->twilio_phone_number;
                    try {
                        $twilio = new Clients($twilio_sid, $twilio_token);
                        $twilio->messages->create(
                            $to,
                            [
                                'from' => $twilio_phone_number,
                                'body' => $message,
                            ]
                        );
                    } catch (\Throwable $th) {
                        return redirect()->back()->with('error', 'Somthing Went Wrong');
                    }
                }
                if ($setting->enable_vonage == 1) {
                    $apiKey = $setting->vonege_api_key;
                    $apiSecret = $setting->vonage_account_secret;
                    $virtualNumber = $setting->vonage_sender_number;
                    $response = Http::post('https://rest.nexmo.com/sms/json', [
                        'api_key' => $apiKey,
                        'api_secret' => $apiSecret,
                        'to' => $to,
                        'from' => $virtualNumber,
                        'text' => $message,
                    ]);
                }
                if ($data['user_type'] == 'organizer') {
                    $user = User::find($user->id);
                    $user->otp = $otp;
                    $user->update();
                    return redirect('organizer/otp-verify/' . $user->id)->with(['success' => "Phone verification code sent via SMS."]);
                } else {
                    $user = AppUser::find($user->id);
                    $user->otp = $otp;
                    $user->update();
                    return redirect('user/otp-verify/' . $user->id)->with(['success' => "Phone verification code sent via SMS."]);
                }
            }
        }
        return redirect('user/login')->with(['success' => "Congratulations! Your account registration was successful. You can now log in to your account and start using our services. Thank you for choosing our platform"]);
    }
    public function LoginByMail($id)
    {
        $user = AppUser::find($id);
        if ($user->is_verify == '0' && Auth::guard('appuser')->loginUsingId($id)) {
            $user = Auth::guard('appuser')->user();
            $verify = AppUser::find($user->id);
            $verify->email_verified_at = Carbon::now();
            $verify->is_verify = 1;
            $verify->update();
            $this->setLanguage($user);
            if ($user->status == '0') {
                \Auth::logout();
                return redirect()->route('user.login')->with('error', 'You have successfully verified your account.please wait until admin approves.');
            } else {
                return redirect('/');
            }
        } elseif ($user->is_verify == '1') {
            \Auth::logout();
            return redirect()->route('user.login')->with('error', 'You already verified your account');
        }
        return redirect()->route('user.login')->with('error', 'Something went wrong on verification process');
    }
    public function LoginByMailOrganizer($id)
    {
        $user = User::find($id);
        if ($user->is_verify == '0' && Auth::loginUsingId($id)) {
            $user = Auth::user();
            $verify = User::find($user->id);
            $verify->email_verified_at = Carbon::now();
            $verify->is_verify = 1;
            $verify->update();
            $this->setLanguage($user);
            if ($user->status == '0') {
                \Auth::logout();
                return redirect()->route('user.login')->with('error', 'You have successfully verified your account.please wait until admin approves.');
            } else {
                return redirect('/organization/home');
            }
        } elseif ($user->is_verify == '1') {
            \Auth::logout();
            return redirect()->route('user.login')->with('error', 'You already verified your account');
        }
        return redirect()->route('user.login')->with('error', 'Something went wrong on verification process');
    }
    public function resetPassword()
    {
        $setting = Setting::first(['app_name', 'logo']);
        SEOMeta::setTitle($setting->app_name . ' - reset password' ?? env('APP_NAME'))
            ->setDescription('This is reset password page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'reset password page',
                $setting->app_name,
                $setting->app_name . ' reset password',
                'forgot password page',
                $setting->app_name . ' forgot password'
            ]);

        OpenGraph::setTitle($setting->app_name . ' - reset password' ?? env('APP_NAME'))
            ->setDescription('This is reset password page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - reset password' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is reset password page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - reset password' ?? env('APP_NAME'));
        SEOTools::setDescription('This is reset password page');
        SEOTools::opengraph()->addProperty(
            'keywords',
            [
                'reset password page',
                $setting->app_name,
                $setting->app_name . ' reset password',
                'forgot password page',
                $setting->app_name . ' forgot password'
            ]
        );
        SEOTools::opengraph()->addProperty('image', $setting->imagePath . $setting->logo);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        return view('frontend.auth.resetPassword');
    }

    public function userResetPassword(Request $request)
    {
        $request->validate([
            'email' => 'bail|required|email',
        ]);
        if ($request->type == 'user') {
            $user = AppUser::where('email', $request->email)->first();
        } else {
            $user = User::where('email', $request->email)->first();
        }
        $password = rand(100000, 999999);
        if ($user) {
            $content = NotificationTemplate::where('title', 'Reset Password')->first()->mail_content;
            $detail['user_name'] = $user->name;
            $detail['password'] = $password;
            $detail['app_name'] = Setting::find(1)->app_name;
            if ($request->type == 'user') {
                AppUser::find($user->id)->update(['password' => Hash::make($password)]);
            } else {
                User::find($user->id)->update(['password' => Hash::make($password)]);
            }
            try {
                \Mail::to($user->email)->send(new ResetPassword($content, $detail));
            } catch (\Throwable $th) {
                return redirect()->back()->with('error', $th->getMessage());
            }
            return redirect()->route('user.login')->with('success', 'New password will send in your mail, please check it.');
        } else {
            return Redirect::back()->with('error', 'Invalid Email Id, Please try another.');
        }
    }

    public function orgRegister()
    {
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - Organizer Register' ?? env('APP_NAME'))
            ->setDescription('This is organizer register page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'organizer register page',
                $setting->app_name,
                $setting->app_name . ' Organizer Register',
                'organizer sign-up page',
                $setting->app_name . ' organizer sign-up'
            ]);

        OpenGraph::setTitle($setting->app_name . ' - Organizer Register' ?? env('APP_NAME'))
            ->setDescription('This is organizer register page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - Organizer Register' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is organizer register page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - Organizer Register' ?? env('APP_NAME'));
        SEOTools::setDescription('This is register page');
        SEOTools::opengraph()->addProperty(
            'keywords',
            [
                'register page',
                $setting->app_name,
                $setting->app_name . ' Organizer Register',
                'organizer sign-up page',
                $setting->app_name . ' organizer sign-up'
            ]
        );
        SEOTools::opengraph()->addProperty('image', $setting->imagePath . $setting->logo);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        return view('frontend.auth.orgRegister');
    }

    public function organizerRegister(Request $request)
    {
        $request->validate([
            'name' => 'bail|required|unique:users,name',
            'first_name' => 'bail|required',
            'last_name' => 'bail|required',
            'email' => 'bail|required|email|unique:users',
            'phone' => 'bail|required|numeric',
            'password' => 'bail|required|min:6',
            'confirm_password' => 'bail|required|min:6|same:password',
            'country' => 'bail|required',
        ]);
        $data = $request->all();
        $data['password'] = Hash::make($request->password);
        $data['image'] = 'defaultuser.png';
        $data['language'] = Setting::first()->language;
        $data['is_profile_completed'] = 1;
        $user = User::create($data);
        $user->assignRole('Organizer');
        OrganizerPaymentKeys::create([
            'organizer_id' => $user->id,
        ]);

        return redirect('login');
    }

    public function allEvents(Request $request)
    {
        (new AppHelper)->eventStatusChange();
        $setting = Setting::first(['app_name', 'logo']);
        // dd(\Route::currentRouteName());
        $currentRouteName = \Route::currentRouteName();
        $showAll = false;
        if (isset($_REQUEST['display'])) {
            $showAll = true;
        }
        $pastEvents = [];
        SEOMeta::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'))
            ->setDescription('This is all events page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'all event page',
                $setting->app_name,
                $setting->app_name . ' All-Events',
                'events page',
                $setting->app_name . ' Events',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'))
            ->setDescription('This is all events page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is all events page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'));
        SEOTools::setDescription('This is all events page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            'all event page',
            $setting->app_name,
            $setting->app_name . ' All-Events',
            'events page',
            $setting->app_name . ' Events',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);

        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);

        if (session()->get('lat')) {
            $latitude = session()->get('lat');
            $longitude = session()->get('long');
            $radius = 100; // 100 miles
            $earthRadius = 3959; // Earth's radius in miles
            $city = session()->get('city');
            $state = session()->get('state');
            $country = session()->get('country');
            if (!session()->get('city')) {
                try {
                    $url = "https://maps.googleapis.com/maps/api/geocode/json?latlng=$latitude,$longitude&key=AIzaSyAMhFpujOTpUfQYBIBMuzn_1viQgojw6Vg";
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    $response = curl_exec($ch);
                    curl_close($ch);
                    $responseData = json_decode($response, true);
                    if ($responseData['status'] == 'OK') {
                        foreach ($responseData['results'][0]['address_components'] as $component) {
                            if (in_array('locality', $component['types'])) {
                                $city = $component;
                                session()->put('city', $city);
                            }
                            if (in_array('administrative_area_level_1', $component['types'])) {
                                $state = $component;
                                session()->put('state', $state);
                            }
                            if (in_array('country', $component['types'])) {
                                $country = $component;
                                session()->put('country', $country);
                            }
                        }
                    }
                } catch (Exception $ex) {
                }
            }
            $events = Event::with(['category:id,name'])
                ->selectRaw("
                *, ($earthRadius * acos( cos( radians(?) ) * cos( radians( lat ) ) *
                cos( radians( lang ) - radians(?) ) + sin( radians(?) ) *
                sin( radians( lat ) ) ) ) AS distance", [$latitude, $longitude, $latitude])
                ->where([
                    ['status', 1],
                    ['is_deleted', 0],
                    ['event_status', 'Pending']
                ])->having('distance', '<', $radius);
            if ($currentRouteName == 'all-events') {
                $events = $events->where([
                    ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                    ['end_time', '>', $date->format('Y-m-d H:i:s')]
                ]);
            } else {
                $events = $events->where([
                    ['end_time', '<=', $date->format('Y-m-d H:i:s')]
                ]);
            }
        } else {
            $events = Event::with(['category:id,name'])
                ->where([
                    ['status', 1],
                    ['is_deleted', 0],
                    ['event_status', 'Pending']
                    // ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                    // ['end_time', '>', $date->format('Y-m-d H:i:s')]
                ]);
            if ($currentRouteName == 'all-events') {
                $events = $events->where([
                    ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                    ['end_time', '>', $date->format('Y-m-d H:i:s')]
                ]);
            } else {
                $events = $events->where([
                    ['end_time', '<=', $date->format('Y-m-d H:i:s')]
                ]);
            }
        }

        list($events, $chip) = $this->filterEvent($request, $events, $timezone);
        if (session()->get('lat')) {
            $events = $events->orderBy('distance')->orderBy('order', 'asc');
            if (!$showAll) {
                $events = $events->limit(12);
            }
        }
        $events = $events->orderBy('start_time', 'ASC')->orderBy('order', 'asc');
        if (!$showAll) {
            $events = $events->limit(12);
        }
        $events = $events->get();
        if ($events->isEmpty() && session()->get('lat')) {
            if (!empty($city)) {
                $events = Event::with(['category:id,name'])
                    ->where('address', 'like', '%, ' . $city['long_name'] . ',%')
                    ->orWhere('address', 'like', '%, ' . $city['short_name'] . ',%')
                    ->where([
                        ['status', 1],
                        ['is_deleted', 0],
                        ['event_status', 'Pending'],
                        // ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                        // ['end_time', '>', $date->format('Y-m-d H:i:s')]
                    ]);
            } else if (!empty($state)) {
                $events = Event::with(['category:id,name'])
                    ->where('address', 'like', '%, ' . $state['long_name'] . '%')
                    ->orWhere('address', 'like', '%, ' . $state['short_name'] . '%')
                    ->where([
                        ['status', 1],
                        ['is_deleted', 0],
                        ['event_status', 'Pending'],
                        // ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                        // ['end_time', '>', $date->format('Y-m-d H:i:s')]
                    ]);
            } else if (!empty($country)) {
                $events = Event::with(['category:id,name'])
                    ->where('address', 'like', '%, ' . $country['long_name'] . '%')
                    ->orWhere('address', 'like', '%, ' . $country['short_name'] . '%')
                    ->where([
                        ['status', 1],
                        ['is_deleted', 0],
                        ['event_status', 'Pending']
                        // ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                        // ['end_time', '>', $date->format('Y-m-d H:i:s')]
                    ]);
            }
            if ($currentRouteName == 'all-events') {
                $events = $events->where([
                    ['start_time', '>=', $date->format('Y-m-d H:i:s')],
                    ['end_time', '>', $date->format('Y-m-d H:i:s')]
                ]);
            } else {
                $events = $events->where([
                    ['end_time', '<=', $date->format('Y-m-d H:i:s')]
                ]);
            }
            list($events, $chip) = $this->filterEvent($request, $events, $timezone);
            $events = $events->orderBy('start_time', 'ASC')->orderBy('order', 'asc');
            if (!$showAll) {
                $events = $events->limit(12);
            }
            $events = $events->get();
        }
        foreach ($events as $value) {
            $value->total_ticket = Ticket::where([['event_id', $value->id], ['is_deleted', 0], ['status', 1]])->sum('quantity');
            $value->sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->available_ticket = $value->total_ticket - $value->sold_ticket;
        }
        $user = Auth::guard('appuser')->user();
        $offlinecount = 0;
        $onlinecount = 0;
        foreach ($events as $key => $value) {
            if ($value->type == 'online') {
                $onlinecount += 1;
            }
            if ($value->type == 'offline') {
                $offlinecount += 1;
            }
        }
        if ($currentRouteName == 'all-events') {
            $pastEvents = Event::with(['category:id,name'])
                ->where([
                    ['status', 1],
                    ['is_deleted', 0],
                    ['event_status', 'Pending'],
                    ['end_time', '<=', $date->format('Y-m-d H:i:s')]
                ])->limit(12)->orderBy('start_time', 'ASC')->orderBy('order', 'asc')->get();
        }
        return view('frontend.events', compact('user', 'events', 'chip', 'onlinecount', 'offlinecount', 'pastEvents'));
    }


    public function filterEvent($request, $events, $timezone)
    {
        $chip = array();
        if ($request->has('type') && $request->type != null) {
            $chip['type'] = $request->type;
            $events = $events->where('type', $request->type);
        }
        if ($request->has('category') && $request->category != null) {
            $chip['category'] = Category::find($request->category)->name;
            $events = $events->where('category_id', $request->category);
        }
        if ($request->has('duration') && $request->duration != null) {
            $chip['date'] = $request->duration;
            if ($request->duration == 'Today') {
                $temp = Carbon::now($timezone);
                $events = $events->where('start_time', '<=', $temp);
            } else if ($request->duration == 'Tomorrow') {
                $temp = Carbon::tomorrow($timezone);
                $events = $events->where('start_time', '<=', $temp);
            } else if ($request->duration == 'ThisWeek') {
                $now = Carbon::now($timezone);
                $weekStartDate = $now->startOfWeek()->format('Y-m-d H:i:s');
                $weekEndDate = $now->endOfWeek()->format('Y-m-d H:i:s');
                $events = $events->where('start_time', '<=', $weekEndDate);
            } else if ($request->duration == 'date') {
                if (isset($request->date)) {
                    $dateArray = explode(' to ', $request->date);
                    $from_date = Carbon::parse($dateArray[0])->format('Y-m-d H:i:s');
                    $to_date = Carbon::parse($dateArray[1])->format('Y-m-d H:i:s');
                    $events = $events->where('start_time', '<=', $from_date)
                        ->where('end_time', '>=', $to_date);
                }
            }
        }
        return [$events, $chip];
    }

    public function donations()
    {
        $donationCampaigns = \App\Models\DonationPost::where('active', 1)->orderBy('created_at', 'desc')->limit(8)->get();
        $directories = Directory::where('status', 1)->orderBy('order', 'desc')->get();
        $constructedDirectories = [];
        foreach ($directories as $directory) {
            $types = $directory->types;
            if (isset($types) && !empty($types)) {
                foreach ($types as $type) {
                    if (isset($type->is_featured) && $type->is_featured != '1') {
                        continue;
                    }
                    if (!isset($constructedDirectories[$type->name . "/-/" . $type->id])) {
                        $constructedDirectories[$type->name . "/-/" . $type->id] = [];
                    }
                    array_push($constructedDirectories[$type->name . "/-/" . $type->id], $directory);
                }
            }
        }
        // Fetch donation categories with their donation posts
        $categories = \App\Models\DonationCategory::with([
            'donation_posts' => function ($query) {
                $query->where('active', 1); // Filter active donation posts
            }
        ])->get();
        return view('frontend.donations', compact('donationCampaigns', 'categories'));
    }

    public function donationDetails($id)
    {
        $donationPost = \App\Models\DonationPost::select('donation_posts.title', 'donation_posts.image as donation_post_image', 'donation_posts.description', 'donation_posts.target', 'donation_posts.started_date', 'donation_posts.expired_date', 'donation_posts.donation_category_id', 'donation_posts.shared_count', 'donation_posts.added_by', 'donation_posts.last_edited_by', 'donation_posts.active', 'donation_posts.is_external_link', 'directories.image as org_image', 'directories.name as org_name', 'donation_posts.donation_designation')->leftJoin('directories', 'donation_posts.directory_id', '=', 'directories.id')->where('donation_posts.id', $id)->first();
        $donationPaymentData = \App\Models\DonationPayment::where('donation_post_id', $id)->get();
        $count = $donationPaymentData->count();
        $totalAmount = $donationPaymentData->sum('amount');
        $percentageCollected = 0;
        if ($donationPost->target > 0) {
            $percentageCollected = ($totalAmount / $donationPost->target) * 100;
        }
        $percentageCollected = round($percentageCollected, 2);
        if (!$donationPost) {
            abort(404, 'Donation Post not found.');
        }
        return view('frontend.donationDetails', compact('donationPost', 'totalAmount', 'count', 'donationPaymentData', 'percentageCollected'));
    }


    public function allOrganizations(Request $request)
    {
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - All-Organizations' ?? env('APP_NAME'))->setDescription('This is all organizations page')->setCanonical(url()->current())
            ->addKeyword([
                'all organizations page',
                $setting->app_name,
                $setting->app_name . ' All-Organizations',
                'organizations page',
                $setting->app_name . ' Organizations',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - All-Organizations' ?? env('APP_NAME'))->setDescription('This is all organizations page')->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - All-Organizations' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is all organizations page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - All-Organizations' ?? env('APP_NAME'));
        SEOTools::setDescription('This is all organizations page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            'all organizations page',
            $setting->app_name,
            $setting->app_name . ' All-Organizations',
            'organizations page',
            $setting->app_name . ' Organizations',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $directories = Directory::where('directories.status', 1);

        if (isset($_REQUEST['state']) && !empty($_REQUEST['state'])) {
            $directories = $directories->where('address', 'like', '%' . $_REQUEST['state'] . '%');
        }
        if ($request->has('country') && !empty($request->country)) {
            $directories->whereJsonContains('where_we_work', $request->country);
        }
        if ($request->search !== null) {
            $directories = $directories
                ->leftJoin('directory_types', function ($join) {
                    $join->on('directory_types.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.directory_type, '$[0]'))"));
                })
                ->leftJoin('country', function ($join) {
                    $join->on('country.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.where_we_work, '$[0]'))"));
                })
                ->leftJoin('programs', function ($join) {
                    $join->on('programs.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.what_we_do, '$[0]'))"));
                })
                ->where('directories.name', 'LIKE', '%' . $request->search . '%')->orWhere('directories.business_name', 'LIKE', '%' . $request->search . '%')->orWhere('programs.name', 'LIKE', '%' . $request->search . '%')->orWhereJsonContains('directories.tags', $request->search)->select('directories.id', 'directories.name', 'directories.image')->orderBy('directory_types.order', 'asc')->paginate(48)->appends(['search' => $request->search]);

            $loadAddressAll = DB::table('directories')
                ->leftJoin('country', function ($join) {
                    $join->on('country.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.where_we_work, '$[0]'))"));
                })
                ->leftJoin('programs', function ($join) {
                    $join->on('programs.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.what_we_do, '$[0]'))"));
                })
                ->where('directories.name', 'LIKE', '%' . $request->search . '%')->orWhere('directories.business_name', 'LIKE', '%' . $request->search . '%')->orWhereJsonContains('directories.tags', $request->search)->orWhere('country.name', 'LIKE', '%' . $request->search . '%')->orWhere('programs.name', 'LIKE', '%' . $request->search . '%')->select('directories.*', 'country.name as country_name', 'programs.name as program_name')->get();
        } elseif ($request->has('category') || $request->has('tag') || $request->has('program')) {
            $directories = $directories->where(function ($query) use ($request) {
                if ($request->has('category') && $request->has('program') && $request->has('tag')) {
                    $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('what_we_do', $request->program)->whereJsonContains('tags', $request->tag);
                } elseif ($request->has('category') && $request->has('program')) {
                    $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('what_we_do', $request->program);
                } elseif ($request->has('category') && $request->has('tag')) {
                    $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('tags', $request->tag);
                } elseif ($request->has('program') && $request->has('tag')) {
                    $query->whereJsonContains('what_we_do', $request->program)->whereJsonContains('tags', $request->tag);
                } else {
                    if ($request->has('category')) {
                        $query->whereJsonContains('directory_type', intval($request->category));
                    }
                    if ($request->has('tag')) {
                        $query->whereJsonContains('tags', $request->tag);
                    }
                    if ($request->has('program')) {
                        $query->whereJsonContains('what_we_do', $request->program);
                    }
                }
            })->orderBy('order', 'asc')->paginate(48);
            if (isset($_REQUEST['state']) && !empty($_REQUEST['state'])) {
                $loadAddressAll = DB::table('directories')->where('address', 'like', '%' . $_REQUEST['state'] . '%')
                    ->where(function ($query) use ($request) {
                        if (is_numeric($request->state)) {
                            $query->whereJsonContains('directory_type', intval($_REQUEST['state']))->orWhereJsonContains('what_we_do', intval($_REQUEST['state']));
                        } else {
                            $query->orWhereJsonContains('tags', $_REQUEST['state']);
                        }
                    })->get();
            } else {
                $loadAddressAll = DB::table('directories')->where(function ($query) use ($request) {
                    if ($request->has('category') && $request->has('program') && $request->has('tag')) {
                        $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('what_we_do', $request->program)->whereJsonContains('tags', $request->tag);
                    } elseif ($request->has('category') && $request->has('program')) {
                        $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('what_we_do', $request->program);
                    } elseif ($request->has('category') && $request->has('tag')) {
                        $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('tags', $request->tag);
                    } elseif ($request->has('program') && $request->has('tag')) {
                        $query->whereJsonContains('what_we_do', $request->program)->whereJsonContains('tags', $request->tag);
                    } else {
                        if ($request->has('category')) {
                            $query->whereJsonContains('directory_type', intval($request->category));
                        }
                        if ($request->has('tag')) {
                            $query->whereJsonContains('tags', $request->tag);
                        }
                        if ($request->has('program')) {
                            $query->whereJsonContains('what_we_do', $request->program);
                        }
                    }
                })->get();
            }
        } else {
            $directories = $directories->leftJoin('directory_types', function ($join) {
                $join->on('directory_types.id', '=', DB::raw("CAST(JSON_UNQUOTE(JSON_EXTRACT(directories.directory_type, '$[0]')) AS UNSIGNED)"));
            })->whereRaw("NOT JSON_CONTAINS(directories.directory_type, '15')")->select('directories.*')->orderBy('directory_types.order', 'asc')
                ->orderBy('directories.order', 'asc')->paginate(48);
            $loadAddressAll = DB::table('directories')->whereRaw("NOT JSON_CONTAINS(directory_type, '15')")->select('address')->get();
        }

        $filteredDirectoryIds = $directories->pluck('id');
        $user = Auth::guard('appuser')->user();
        $categories = DirectoryType::getDirectoryTypeWithCountOfDirectory();
        $tags = TagsModel::getTagsWithCountOfDirectory();
        $programs = Programs::getProgramsWithCountOfDirectory();
        $countries = Country::getCountryWithCountOfDirectory();

        $states = [];
        $allDirectories = Directory::where('status', 1)->get();
        foreach ($directories as $directory) {
            $address = \App\Models\Directory::constructAddress($directory);
            $directory->address = $address;
        }
        foreach ($allDirectories as $directory) {
            $address = \App\Models\Directory::constructAddress($directory);
            $directory->address = $address;
            if (!empty($address)) {
                foreach ($address as $addr) {
                    if (isset($addr['state'])) {
                        array_push($states, $addr['state']);
                    }
                }
            }
        }
        $states = array_unique($states);
        return view('frontend.organizations', compact('user', 'states', 'directories', 'categories', 'loadAddressAll', 'tags', 'programs', 'countries'));
    }

    public function allMasajids(Request $request)
    {
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - All-Organizations' ?? env('APP_NAME'))->setDescription('This is all organizations page')->setCanonical(url()->current())
            ->addKeyword([
                'all organizations page',
                $setting->app_name,
                $setting->app_name . ' All-Organizations',
                'organizations page',
                $setting->app_name . ' Organizations',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - All-Organizations' ?? env('APP_NAME'))->setDescription('This is all organizations page')->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - All-Organizations' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is all organizations page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - All-Organizations' ?? env('APP_NAME'));
        SEOTools::setDescription('This is all organizations page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            'all organizations page',
            $setting->app_name,
            $setting->app_name . ' All-Organizations',
            'organizations page',
            $setting->app_name . ' Organizations',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $directories = Directory::where('directories.status', 1);

        if (isset($_REQUEST['state']) && !empty($_REQUEST['state'])) {
            $directories = $directories->where('address', 'like', '%' . $_REQUEST['state'] . '%');
        }
        if ($request->has('country') && !empty($request->country)) {
            $directories->whereJsonContains('where_we_work', $request->country);
        }
        if ($request->search !== null) {
            $directories = $directories
                ->leftJoin('directory_types', function ($join) {
                    $join->on('directory_types.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.directory_type, '$[0]'))"));
                })
                ->leftJoin('country', function ($join) {
                    $join->on('country.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.where_we_work, '$[0]'))"));
                })
                ->leftJoin('programs', function ($join) {
                    $join->on('programs.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.what_we_do, '$[0]'))"));
                })
                ->where('directories.name', 'LIKE', '%' . $request->search . '%')->orWhere('directories.business_name', 'LIKE', '%' . $request->search . '%')->orWhere('programs.name', 'LIKE', '%' . $request->search . '%')->orWhereJsonContains('directories.tags', $request->search)->select('directories.id', 'directories.name', 'directories.image')->orderBy('directory_types.order', 'asc')->paginate(48)->appends(['search' => $request->search]);

            $loadAddressAll = DB::table('directories')
                ->leftJoin('country', function ($join) {
                    $join->on('country.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.where_we_work, '$[0]'))"));
                })
                ->leftJoin('programs', function ($join) {
                    $join->on('programs.id', '=', DB::raw("JSON_UNQUOTE(JSON_EXTRACT(directories.what_we_do, '$[0]'))"));
                })
                ->where('directories.name', 'LIKE', '%' . $request->search . '%')->orWhere('directories.business_name', 'LIKE', '%' . $request->search . '%')->orWhereJsonContains('directories.tags', $request->search)->orWhere('country.name', 'LIKE', '%' . $request->search . '%')->orWhere('programs.name', 'LIKE', '%' . $request->search . '%')->select('directories.*', 'country.name as country_name', 'programs.name as program_name')->get();
        } elseif ($request->has('category') || $request->has('tag') || $request->has('program')) {
            $directories = $directories->where(function ($query) use ($request) {
                if ($request->has('category') && $request->has('program') && $request->has('tag')) {
                    $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('what_we_do', $request->program)->whereJsonContains('tags', $request->tag);
                } elseif ($request->has('category') && $request->has('program')) {
                    $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('what_we_do', $request->program);
                } elseif ($request->has('category') && $request->has('tag')) {
                    $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('tags', $request->tag);
                } elseif ($request->has('program') && $request->has('tag')) {
                    $query->whereJsonContains('what_we_do', $request->program)->whereJsonContains('tags', $request->tag);
                } else {
                    if ($request->has('category')) {
                        $query->whereJsonContains('directory_type', intval($request->category));
                    }
                    if ($request->has('tag')) {
                        $query->whereJsonContains('tags', $request->tag);
                    }
                    if ($request->has('program')) {
                        $query->whereJsonContains('what_we_do', $request->program);
                    }
                }
            })->orderBy('order', 'asc')->paginate(48);
            if (isset($_REQUEST['state']) && !empty($_REQUEST['state'])) {
                $loadAddressAll = DB::table('directories')->where('address', 'like', '%' . $_REQUEST['state'] . '%')
                    ->where(function ($query) use ($request) {
                        if (is_numeric($request->state)) {
                            $query->whereJsonContains('directory_type', intval($_REQUEST['state']))->orWhereJsonContains('what_we_do', intval($_REQUEST['state']));
                        } else {
                            $query->orWhereJsonContains('tags', $_REQUEST['state']);
                        }
                    })->get();
            } else {
                $loadAddressAll = DB::table('directories')->where(function ($query) use ($request) {
                    if ($request->has('category') && $request->has('program') && $request->has('tag')) {
                        $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('what_we_do', $request->program)->whereJsonContains('tags', $request->tag);
                    } elseif ($request->has('category') && $request->has('program')) {
                        $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('what_we_do', $request->program);
                    } elseif ($request->has('category') && $request->has('tag')) {
                        $query->whereJsonContains('directory_type', intval($request->category))->whereJsonContains('tags', $request->tag);
                    } elseif ($request->has('program') && $request->has('tag')) {
                        $query->whereJsonContains('what_we_do', $request->program)->whereJsonContains('tags', $request->tag);
                    } else {
                        if ($request->has('category')) {
                            $query->whereJsonContains('directory_type', intval($request->category));
                        }
                        if ($request->has('tag')) {
                            $query->whereJsonContains('tags', $request->tag);
                        }
                        if ($request->has('program')) {
                            $query->whereJsonContains('what_we_do', $request->program);
                        }
                    }
                })->get();
            }
        } else {
            $directories = $directories->leftJoin('directory_types', function ($join) {
                $join->on('directory_types.id', '=', DB::raw("CAST(JSON_UNQUOTE(JSON_EXTRACT(directories.directory_type, '$[0]')) AS UNSIGNED)"));
            })->whereRaw("JSON_CONTAINS(directories.directory_type, '15')")->select('directories.*')->orderBy('directory_types.order', 'asc')->orderBy('directories.order', 'asc')->paginate(48);
            $loadAddressAll = DB::table('directories')->whereRaw("JSON_CONTAINS(directories.directory_type, '15')")->select('address')->get();
        }

        $filteredDirectoryIds = $directories->pluck('id');
        $user = Auth::guard('appuser')->user();
        $categories = DirectoryType::getMasajidsWithCount();
        $tags = TagsModel::getTagsWithCountOfDirectory();
        $programs = Programs::getProgramsWithCountOfDirectory();
        $countries = Country::getCountryWithCountOfDirectory();

        $states = [];
        $allDirectories = Directory::where('status', 1)->get();
        foreach ($directories as $directory) {
            $address = \App\Models\Directory::constructAddress($directory);
            $directory->address = $address;
        }
        foreach ($allDirectories as $directory) {
            $address = \App\Models\Directory::constructAddress($directory);
            $directory->address = $address;
            if (!empty($address)) {
                foreach ($address as $addr) {
                    if (isset($addr['state'])) {
                        array_push($states, $addr['state']);
                    }
                }
            }
        }
        $states = array_unique($states);
        return view('frontend.masajids', compact('user', 'states', 'directories', 'categories', 'loadAddressAll', 'tags', 'programs', 'countries'));
    }

    public function allBusiness(Request $request)
    {
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - All-Business' ?? env('APP_NAME'))
            ->setDescription('This is all business page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'all business page',
                $setting->app_name,
                $setting->app_name . ' All-Business',
                'business page',
                $setting->app_name . ' Business',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - All-Business' ?? env('APP_NAME'))
            ->setDescription('This is all business page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - All-Business' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is all business page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - All-Business' ?? env('APP_NAME'));
        SEOTools::setDescription('This is all business page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            'all business page',
            $setting->app_name,
            $setting->app_name . ' All-Business',
            'business page',
            $setting->app_name . ' Business',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);

        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $directories = Directory::where('status', 1)->orderBy('created_at', 'ASC')->get();
        $user = Auth::guard('appuser')->user();
        $categories = DirectoryType::where('is_featured', 1)->withCount('directories')->get();
        return view('frontend.directories', compact('user', 'directories', 'categories'));
    }

    public function directoryDetails(Directory $directory, $name)
    {
        $setting = Setting::first(['app_name', 'logo']);
        $currency = Setting::first(['currency_sybmol']);
        $data = Directory::with('category')->find($directory->id);
        SEOMeta::setTitle($data->name)
            ->setDescription($data->description)
            ->addMeta('directory:category', $data->category?->name, 'property')
            ->addKeyword([
                $setting->app_name,
                $data->name,
                $setting->app_name . ' - ' . $data->name,
                $data->category?->name,
                $data->tags
            ]);

        OpenGraph::setTitle($data->name)
            ->setDescription($data->description)
            ->setUrl(url()->current())
            ->addImage($setting->imagePath . $data->image)
            ->setArticle([
                'name' => $data->name,
                'business_name' => $data->business_name,
                'description' => $data->description,
                'location' => $data->location,
                'category' => $data->category?->name,
            ]);

        JsonLd::setTitle($data->name)
            ->setDescription($data->description)
            ->setType('Article')
            ->addImage($setting->imagePath . $data->image);

        SEOTools::setTitle($data->name);
        SEOTools::setDescription($data->description);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $data->name,
            $setting->app_name . ' - ' . $data->name,
            $data->category?->name,
            $data->tags
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        SEOTools::jsonLd()->addImage($setting->imagePath . $data->image);
        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $events = $data->organization->events()->with(['category:id,name'])
            ->where([['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d H:i:s')]])
            ->orderBy('start_time', 'desc')->limit(4)->get();

        $tags = explode(",", $data->tags);
        $user = Auth::guard('appuser')->user();
        $business_hours = json_decode($data->business_hours, true);
        $social_media = json_decode($data->social_media, true);
        $contactInformations = json_decode($data->contact_informations, true);
        // $rate = round(Review::where('event_id', $data->id)->avg('rate'));
        return view('frontend.directoryDetail', compact('currency', 'data', 'events', 'tags', 'user', 'business_hours', 'social_media', 'contactInformations'));
    }

    public function eventDetail($id, $name = null)
    {
        $setting = Setting::first(['app_name', 'logo']);
        $currency = Setting::first(['currency_sybmol']);
        $data = Event::with(['category:id,name,image', 'organization:id,business_name'])->find($id);
        SEOMeta::setTitle($data->name)
            ->setDescription(strip_tags($data->description))
            ->addMeta('event:category', $data->categories->pluck('name')->join(', '), 'property')
            ->addKeyword([
                $setting->app_name,
                $data->name,
                $setting->app_name . ' - ' . $data->name,
                $data->categories->pluck('name')->join(', '),
                $data->tags
            ]);
        OpenGraph::setTitle($data->name)
            ->setDescription(strip_tags($data->description))
            ->setUrl(url()->current())
            ->addImage($data->imagePath . $data->image)
            ->setArticle([
                'start_time' => $data->start_time,
                'end_time' => $data->end_time,
                'organization' => $data->organizers->business_name ?? $data->organizers->name ?? "",
                'catrgory' => $data->categories->pluck('name')->join(', '),
                'type' => $data->type,
                'address' => $data->address,
                'tag' => $data->tags,
            ]);

        JsonLd::setTitle($data->name)
            ->setDescription(strip_tags($data->description))
            ->setType('Article')
            ->addImage($data->imagePath . $data->image);

        SEOTools::setTitle($data->name);
        SEOTools::setDescription(strip_tags($data->description));
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $data->name,
            $setting->app_name . ' - ' . $data->name,
            $data->categories->pluck('name')->join(', '),
            $data->tags
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        SEOTools::jsonLd()->addImage($data->imagePath . $data->image);
        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $data->free_ticket = Ticket::where([['event_id', $data->id], ['is_deleted', 0], ['type', 'free'], ['status', 1], ['end_time', '>=', $date->format('Y-m-d H:i:s')], ['start_time', '<=', $date->format('Y-m-d H:i:s')]])->orderBy('id', 'DESC')->get();
        $data->paid_ticket = Ticket::where([['event_id', $data->id], ['is_deleted', 0], ['type', 'paid'], ['status', 1], ['end_time', '>=', $date->format('Y-m-d H:i:s')], ['start_time', '<=', $date->format('Y-m-d H:i:s')]])->orderBy('id', 'DESC')->get();
        $data->donation_tickets = Ticket::where([['event_id', $data->id], ['is_deleted', 0], ['type', 'donation'], ['status', 1], ['end_time', '>=', $date->format('Y-m-d H:i:s')], ['start_time', '<=', $date->format('Y-m-d H:i:s')]])->orderBy('id', 'DESC')->get();
        $data->review = Review::where('event_id', $data->id)->orderBy('id', 'DESC')->get();
        foreach ($data->paid_ticket as $value) {
            $used = OrderChild::where('ticket_id', $value->id)->sum('qty');
            $value->available_qty = $value->quantity - $used;
        }

        foreach ($data->free_ticket as $value) {
            $used = OrderChild::where('ticket_id', $value->id)->sum('qty');
            $value->available_qty = $value->quantity - $used;
        }
        $images = explode(",", $data->gallery);
        $tags = explode(",", $data->tags);
        $tags = array_filter($tags);
        $user = Auth::guard('appuser')->user();
        $rate = round(Review::where('event_id', $data->id)->avg('rate'));
        // return view('frontend.eventDetail-dup', compact('currency', 'data', 'images', 'tags', 'user', 'rate'));
        return view('frontend.eventDetail-custom', compact('currency', 'data', 'images', 'tags', 'user', 'rate'));
        // return view('frontend.eventDetail', compact('currency', 'data', 'images', 'tags', 'user', 'rate'));
    }

    public function orgDetail(Directory $directory, $name)
    {
        $setting = Setting::first(['app_name', 'logo']);
        $currency = Setting::first(['currency_sybmol']);

        $data = Directory::select('directories.*')
            ->leftJoin('country', function ($join) {
                $join->whereRaw("JSON_CONTAINS(directories.where_we_work, CONCAT('\"', country.id, '\"'))");
            })
            ->leftJoin('programs', function ($join) {
                $join->whereRaw("JSON_CONTAINS(directories.what_we_do, CONCAT('\"', programs.id, '\"'))");
            })
            ->where('directories.id', $directory->id)
            ->selectRaw("
                directories.*,
                GROUP_CONCAT(DISTINCT country.name ORDER BY country.name ASC) as country_names,
                GROUP_CONCAT(DISTINCT programs.name ORDER BY programs.name ASC) as program_names
            ")
            ->groupBy('directories.id')
            ->first();

        SEOMeta::setTitle(($data->first_name ?? '') . ' ' . ($data->last_name ?? ''))
            ->setDescription($data->bio)
            ->addKeyword([
                $setting->app_name,
                $data->name,
                ($data->first_name ?? '') . ' ' . ($data->last_name ?? ''),
            ]);

        OpenGraph::setTitle(($data->first_name ?? '') . ' ' . $data->last_name ?? '')
            ->setDescription($data->bio)
            ->setType('profile')
            ->setUrl(url()->current())
            ->addImage($data->imagePath . $data->image)
            ->setProfile([
                'first_name' => ($data->first_name ?? ''),
                'last_name' => ($data->last_name ?? ''),
                'username' => $data->name,
                'email' => $data->email,
                'bio' => $data->bio,
                'country' => $data->country,
            ]);

        JsonLd::setTitle(($data->first_name ?? '') . ' ' . ($data->last_name ?? ''))
            ->setDescription($data->bio)
            ->setType('Profile')
            ->addImage($data->imagePath . $data->image);

        SEOTools::setTitle(($data->first_name ?? '') . ' ' . ($data->last_name ?? ''));
        SEOTools::setDescription($data->bio);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $data->name,
            ($data->first_name ?? '') . ' ' . ($data->last_name ?? ''),
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        SEOTools::jsonLd()->addImage($data->imagePath . $data->image);

        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $events = $data->events()->with(['category:id,name'])
            ->where([
                ['status', 1],
                ['is_deleted', 0],
                ['event_status', 'Pending'],
            ])
            ->orderBy('start_time', 'desc')->inRandomOrder() // Randomize the order of results
            ->take(4) // Limit to 4 events
            ->get();

        $whatwedo = Programs::whereIn('id', json_decode($directory->what_we_do))->pluck('name');

        $tags = json_decode($data->tags, true);
        $tags = is_array($tags) ? $tags : [];
        $user = Auth::guard('appuser')->user();
        $business_hours = json_decode($data->business_hours, true);
        $social_media = json_decode($data->social_media, true);
        $contactInformations = json_decode($data->contact_informations, true);
        $wherewework = $data->country_names;
        $gallery = explode(',', $data->gallery) ?? [];
        $randomCategoriesRecords = DirectoryType::where('status', 1)->inRandomOrder()->limit(6)->get();
        $categories = $randomCategoriesRecords->split(3);
        /*
         * get similar organization
         */
        $similarOrganizations = Directory::returnSimilarOrganizationOrChapters('related_directories', $directory->id);
        /**
         * get chapters lists
         */
        $similarChapters = Directory::returnSimilarOrganizationOrChapters('chapters', $directory->id);


        $gallery = array_filter($gallery);
        $address = array_values(Directory::constructAddress($data));
        $followupLinks = false;
        if (!auth()->guard('appuser')->guest()) {
            $user = Auth::guard('appuser')->user();
            $followupLinks = DirectoryFollowup::where('directory_id', $directory->id)
                ->where('user_id', $user->id)->first();
        }
        // $rate = round(Review::where('event_id', $data->id)->avg('rate'));

        //return view('frontend.orgDetail', compact('currency', 'followupLinks', 'address', 'similarOrganizations', 'similarChapters', 'categories', 'gallery', 'data', 'events', 'tags', 'user', 'business_hours', 'social_media', 'contactInformations'));

        return view('frontend.orgDetail', compact('currency', 'followupLinks', 'address', 'similarOrganizations', 'similarChapters', 'gallery', 'data', 'events', 'tags', 'user', 'business_hours', 'social_media', 'contactInformations', 'wherewework', 'whatwedo'));
    }

    public function reportEvent(Request $request)
    {
        $data = $request->all();
        if (Auth::guard('appuser')->check()) {
            $data['user_id'] = Auth::guard('appuser')->user()->id;
        }
        EventReport::create($data);
        return redirect()->back()->withStatus(__('Report is submitted successfully.'));
    }

    public function checkoutPost(Request $request)
    {
        $request->validate([
            'cart' => [
                'required',
                function ($attribute, $value, $fail) {
                    $data = json_decode($value, true);
                    if (is_array($data) && empty($data)) {
                        $fail('Cart cannot be empty.');
                    }
                }
            ]
        ]);
        $cart = json_decode($request->cart, true);
        $eventDetails = [];
        $totalPrice = 0;
        $ticketCartDetails = new stdClass();
        $fees = 0;
        $fees2 = 0;
        $insuranceAmount = 0;
        if (!empty($cart) && is_array($cart)) {
            $module = Module::where('module', 'Seatmap')->first();
            foreach ($cart as $ticketId => $purchaseQty) {
                $ticketInfo = Ticket::find($ticketId);
                $ticketCartDetails->tickets[$ticketId] = $ticketInfo;
                $used = OrderChild::where('ticket_id', $ticketId)->sum('qty');
                // dd($purchaseQty);
                // $availableQty = $purchaseQty - $used;
                // if ($availableQty === 0) {
                //     continue;
                // }
                $qty = 0;
                $availableQty = 0;
                if ($ticketInfo->type == 'donation') {
                    $totalPrice += $purchaseQty;
                    $ticketInfo->price = $purchaseQty;
                    $qty = 1;
                    $availableQty = 1;
                    $fees += (Ticket::getTicketFee($ticketInfo) * $qty);
                    $fees2 += (Ticket::getTicketFee2($ticketInfo) * $qty);
                    $ticketCartDetails->tickets[$ticketId]->price = $purchaseQty;
                } else {
                    $totalPrice += ($ticketInfo->price * $purchaseQty);
                    $qty = $purchaseQty;
                    $availableQty = ($ticketInfo->quantity - $used);
                    $fees += (Ticket::getTicketFee($ticketInfo) * $qty);
                    $fees2 += (Ticket::getTicketFee2($ticketInfo) * $qty);
                }
                $ticketCartDetails->tickets[$ticketId]->qty = $qty;
                $ticketCartDetails->tickets[$ticketId]->available_qty = $availableQty;
                // $ticketCartDetails->tickets[$ticketId]['ticket_details']->seatmap = $ticketInfo->seatmap_id;
                if ($ticketInfo->seatmap_id != null && $module->is_install == 1 && $module->is_enable == 1) {
                    $seat_map = SeatMaps::findOrFail($ticketInfo->seatmap_id);
                    $rows = Rows::where('seat_map_id', $ticketInfo->seatmap_id)->get();
                    foreach ($rows as $row) {
                        $seats = Seats::where('row_id', $row->id)->get();
                        $seatsByRow[$row->id] = $seats;
                    }
                    $ticketCartDetails->tickets[$ticketId]->seat_map = $seat_map;
                    $ticketCartDetails->tickets[$ticketId]->rows = $rows;
                    $ticketCartDetails->tickets[$ticketId]->seatsByRow = $seatsByRow;
                }
                $calculatedInsuranceAmount = Ticket::getInsuranceAmount($ticketInfo);
                $ticketCartDetails->tickets[$ticketId]->insuranceAmount = $calculatedInsuranceAmount;
                $insuranceAmount += ($calculatedInsuranceAmount * $qty);
            }
            $event = Event::find($ticketInfo->event_id);
            $eventDetails = $event;
        }
        $data = $ticketCartDetails;
        $data->total_fees = ($fees + $fees2);
        $data->total_insurance = $insuranceAmount;
        $data->event = $eventDetails;
        $constructedCart = [];
        foreach ($cart as $ticketId => $qty) {
            if (isset($ticketCartDetails->tickets[$ticketId]['type']) && $ticketCartDetails->tickets[$ticketId]['type'] == 'donation') {
                $constructedCart[$ticketId]['qty'] = 1;
                // $constructedCart[$ticketId]['price'] = $qty;
                // $constructedCart[$ticketId]['insurance'] = 0;
            } else {
                $constructedCart[$ticketId]['qty'] = $qty;
                // $constructedCart[$ticketId]['price'] = $data->tickets[$ticketId]->price;
                // $constructedCart[$ticketId]['insurance'] = Ticket::getInsuranceAmount($data->tickets[$ticketId]);
            }
            // $constructedCart[$ticketId]['fee_2'] = Ticket::getTicketFee2($data->tickets[$ticketId]);
            // $constructedCart[$ticketId]['fee'] = Ticket::getTicketFee($data->tickets[$ticketId]);
        }
        $data->cart = json_encode($constructedCart);
        $setting = Setting::first();
        $seoName = $eventDetails->name . " | Checkout";
        $seoDescription = strip_tags($eventDetails->description);
        SEOMeta::setTitle($seoName)
            ->setDescription(strip_tags($eventDetails->description))
            ->addKeyword([
                $setting->app_name,
                $seoName,
                strip_tags($seoDescription),
                $eventDetails->tags
            ]);

        OpenGraph::setTitle($seoName)
            ->setDescription(strip_tags($seoDescription))
            ->setUrl(url()->current());

        JsonLd::setTitle($seoName)
            ->setDescription(strip_tags($seoDescription));

        SEOTools::setTitle($seoName);
        SEOTools::setDescription(strip_tags($seoDescription));
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $seoName,
            strip_tags($seoDescription),
            $eventDetails->tags
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);

        $arr = [];

        $data->tax = Tax::where([['allow_all_bill', 1], ['status', 1]])->orderBy('id', 'DESC')->get()->makeHidden(['created_at', 'updated_at']);
        foreach ($data->tax as $key => $item) {
            if ($item->amount_type == 'percentage') {
                $amount = ($item->price * $totalPrice) / 100;
                array_push($arr, $amount);
            }
            if ($item->amount_type == 'price') {
                $amount = $item->price;
                array_push($arr, $amount);
            }
        }
        $data->tax_total = array_sum($arr);
        $data->tax_total = round($data->tax_total, 2);
        $data->currency_code = $setting->currency;
        $data->currency = $setting->currency_sybmol;
        $data->module = Module::where('module', 'Seatmap')->first();
        $data->totalPersTax = Tax::where([['allow_all_bill', 1], ['status', 1], ['amount_type', 'percentage']])->sum('price');
        $data->totalAmountTax = Tax::where([['allow_all_bill', 1], ['status', 1], ['amount_type', 'price']])->sum('price');
        // dd($data);
        return view('frontend.checkout', compact('data'));
    }

    public function checkout(Request $request, $id)
    {
        $data = Ticket::find($id);
        $data->event = Event::find($data->event_id);

        $setting = Setting::first();

        SEOMeta::setTitle($data->name)
            ->setDescription($data->description)
            ->addKeyword([
                $setting->app_name,
                $data->name,
                $data->event->name,
                $data->event->tags
            ]);

        OpenGraph::setTitle($data->name)
            ->setDescription($data->description)
            ->setUrl(url()->current());

        JsonLd::setTitle($data->name)
            ->setDescription($data->description);

        SEOTools::setTitle($data->name);
        SEOTools::setDescription($data->description);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $data->name,
            $data->event->name,
            $data->event->tags
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);

        $arr = [];
        $used = Order::where('ticket_id', $id)->sum('quantity');
        $data->available_qty = $data->quantity - $used;
        $data->tax = Tax::where([['allow_all_bill', 1], ['status', 1]])->orderBy('id', 'DESC')->get()->makeHidden(['created_at', 'updated_at']);
        foreach ($data->tax as $key => $item) {
            if ($item->amount_type == 'percentage') {

                $amount = ($item->price * $data->price) / 100;
                array_push($arr, $amount);
            }
            if ($item->amount_type == 'price') {
                $amount = $item->price;
                array_push($arr, $amount);
            }
        }
        $data->tax_total = array_sum($arr);
        $data->tax_total = round($data->tax_total, 2);
        $data->currency_code = $setting->currency;
        $data->currency = $setting->currency_sybmol;
        $data->module = Module::where('module', 'Seatmap')->first();
        if ($data->seatmap_id != null && $data->module->is_install == 1 && $data->module->is_enable == 1) {
            $seat_map = SeatMaps::findOrFail($data->seatmap_id);
            $rows = Rows::where('seat_map_id', $data->seatmap_id)->get();
            foreach ($rows as $row) {
                $seats = Seats::where('row_id', $row->id)->get();
                $seatsByRow[$row->id] = $seats;
            }
            $data->seat_map = $seat_map;
            $data->rows = $rows;
            $data->seatsByRow = $seatsByRow;
        }
        $data->totalPersTax = Tax::where([['allow_all_bill', 1], ['status', 1], ['amount_type', 'percentage']])->sum('price');
        $data->totalAmountTax = Tax::where([['allow_all_bill', 1], ['status', 1], ['amount_type', 'price']])->sum('price');
        return view('frontend.checkout', compact('data'));
    }

    public function getTotalTicketAmountFromCart($cart)
    {
        $total = 0;
        if (!empty($cart) && is_array($cart)) {
            foreach ($cart as $ticketId => $crt) {
                $ticket = Ticket::find($ticketId);
                if (isset($ticket->type) && $ticket->type == 'paid' && $ticket->price > 0) {
                    $total += ($ticket->price * $crt['qty']);
                    $total += (Ticket::getTicketFee($ticket) * $crt['qty']);
                    $total += (Ticket::getTicketFee2($ticket) * $crt['qty']);
                    $total += (Ticket::getInsuranceAmount($ticket) * $qty);
                }
            }
        }
        return $total;
    }

    public function applyCoupon(Request $request)
    {
        $total = $this->getTotalTicketAmountFromCart($request->cart);
        // if ($total != 0) {
        //     $convertedAmount = $this->convertAmount($total, $request->iv, $request->key);
        //     if (is_array($convertedAmount)) {
        //         return response([
        //             'success' => false,
        //             'message' => $convertedAmount['error']
        //         ]);
        //     }
        //     $total = $convertedAmount;
        // }
        $date = Carbon::now()->format('Y-m-d');
        $coupon = Coupon::where([
            ['coupon_code', $request->coupon_code],
            ['status', 1],
            ['event_id', $request->event_id]
        ])->first();
        if ($coupon) {
            $couponHistory = CouponUsageHistory::where([
                ['coupon_id', $coupon->id],
                ['appuser_id', Auth::guard('appuser')->user()->id]
            ])->get();
            if (count($couponHistory) >= $coupon->max_use_per_user) {
                return response([
                    'success' => false,
                    'message' => 'This coupon is reached max use!'
                ]);
            }
            if (Carbon::parse($date)->between(Carbon::parse($coupon->start_date), Carbon::parse($coupon->end_date))) {
                if ($coupon->max_use > $coupon->use_count) {
                    if ($total >= $coupon->minimum_amount) {
                        if ((float) $total > (float) $coupon->maximum_discount) {
                            return response([
                                'success' => false,
                                'message' => 'you can get maximum discount from this coupen is $' . $coupon->maximum_discount . '.'
                            ]);
                        }
                        if ($coupon->discount_type == 0) {
                            $discount = $total * ($coupon->discount / 100);
                        } else {
                            $discount = $coupon->discount;
                        }
                        if ($discount > $coupon->maximum_discount) {
                            $discount = $coupon->maximum_discount;
                        }
                        $subtotal = $total - $discount;

                        return response([
                            'success' => true,
                            'payableamount' => $discount,
                            'total_price' => $subtotal,
                            'total' => $total,
                            'discount' => $coupon->discount,
                            'coupon_id' => $coupon->id,
                            'coupon_type' => $coupon->discount_type
                        ]);
                    } else {
                        return response([
                            'success' => false,
                            'total' => $total,
                            'message' => 'you can use this coupen when the ticket amount is higher than $' . $coupon->minimum_amount . '.'
                        ]);
                    }
                } else {
                    return response([
                        'success' => false,
                        'total' => $total,
                        'message' => 'This coupon is reached max use!'
                    ]);
                }
            } else {
                return response([
                    'success' => false,
                    'total' => $total,
                    'message' => 'This coupon is expire!'
                ]);
            }
        } else {
            return response([
                'success' => false,
                'total' => $total,
                'message' => 'Invalid Coupon code for this event!'
            ]);
        }
    }

    public function processCCPayment($input, $event)
    {
        $prefix = $event->prefix ?? $event->id;
        if (isset($input['payment']) && !empty($input['payment']) && isset($input['payment_token']) && !empty($input['payment_token'])) {
            try {
                $url_name = env('PAYMENT_URL') . "/charge.php";
                $data = array(
                    "amount" => $input['payment'],
                    "source" => 'MB-' . substr($prefix, 0, 9),
                    "payment_token" => $input['payment_token'],
                    // "is_debug" => true
                );
                $curl = curl_init($url_name);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
                curl_setopt(
                    $curl,
                    CURLOPT_HTTPHEADER,
                    array(
                        'Content-Type: application/json',
                        "Authorization: Basic " . base64_encode(env('PAYMENT_AUTH')),
                    )
                );
                $response = curl_exec($curl);
                // echo $response;
                // die;
                curl_close($curl);
                $responseDecode = json_decode($response, true);
                if ($responseDecode && $responseDecode['error'] == 1) {
                    return array('status' => false, 'message' => $responseDecode['response']);
                } else {
                    return array('status' => true, 'data' => $responseDecode['response'], 'message' => 'success');
                }
            } catch (\Exception $exception) {
                return array('status' => false, 'message' => $exception->getMessage());
            }
        }
        return array('status' => false, 'message' => 'Payment token not found');
    }

    public function base64UrlDecode($input)
    {
        return base64_decode(strtr($input, '-_', '+/'));
    }

    public function convertAmount($amount, $iv, $key)
    {
        $encryptedData = base64_decode($amount);
        $iv = base64_decode($iv);
        $key = base64_decode($key);
        $cipher = "aes-256-gcm";
        $tagLength = 16;

        $ciphertext = substr($encryptedData, 0, -$tagLength);
        $tag = substr($encryptedData, -$tagLength);

        $decryptedData = openssl_decrypt($ciphertext, $cipher, $key, OPENSSL_RAW_DATA, $iv, $tag);
        if (!$decryptedData) {
            return ['error' => 'Invalid Payment'];
        }
        return $decryptedData;
    }

    public function createOrder(Request $request)
    {
        $data = $request->all();
        // if (isset($data['payment']) && $data['payment'] != 0) {
        //     $encryptedData = base64_decode($data['payment']);
        //     $iv = base64_decode($data['iv']);
        //     $key = base64_decode($data['key']);
        //     $cipher = "aes-256-gcm";
        //     $tagLength = 16;

        //     $ciphertext = substr($encryptedData, 0, -$tagLength);
        //     $tag = substr($encryptedData, -$tagLength);

        //     $decryptedData = openssl_decrypt($ciphertext, $cipher, $key, OPENSSL_RAW_DATA, $iv, $tag);
        //     if (!$decryptedData) {
        //         return response()->json(['error' => 'Invalid Payment']);
        //     }
        //     $data['payment'] = $decryptedData;
        // }
        if (isset($data['payment']) && $data['payment'] != 0) {
            $convertedAmount = $this->convertAmount($data['payment'], $data['iv'], $data['key']);
            if (is_array($convertedAmount)) {
                return response()->json($convertedAmount);
            }
            $data['payment'] = $convertedAmount;
            dd($data);
            dd($data['payment']);
        }
        $additionalFormData = "";
        parse_str($data['additional_form_data'], $additionalFormData);
        $ticket = Ticket::findOrFail($data['ticket_id']);
        if ($ticket->allday == 0) {
            $request->validate([
                'ticket_date' => 'bail|required',
            ]);
        }
        $request->validate([
            'cart' => 'bail|required',
        ]);
        $cartArray = json_decode($data['cart'], true);
        if (auth()->guard('appuser')->guest()) {
            $request->validate([
                'email' => 'bail|required',
                // 'first_name' => 'bail|required',
                // 'last_name' => 'bail|required',
            ]);
            $user = AppUser::where('email', $data['email'])->first();
            if (!$user) {
                $emailArray = explode('@', $data['email']);
                $userdata['password'] = Hash::make(rand(111111111, 999999999));
                $userdata['image'] = "defaultuser.png";
                $userdata['status'] = 1;
                $userdata['provider'] = "LOCAL";
                $userdata['language'] = Setting::first()->language;
                $userdata['phone'] = "";
                $userdata['is_verify'] = 0;
                $userdata['name'] = $emailArray[0];
                $userdata['last_name'] = "";
                $userdata['email'] = $data['email'];
                $user = AppUser::create($userdata);
            }
        } else {
            $user = AppUser::find(Auth::guard('appuser')->user()->id);
        }
        $event = Event::find($ticket->event_id);
        if ($event->disclaimer == '1' && $request->disclaimer == 'false') {
            return response()->json([
                'message' => 'Validation failed.',
                'errors' => 'Disclaimer field is required'
            ], 422);
        }
        if (isset($data['payment']) && $data['payment'] > 0) {
            $ccPaymentResponse = $this->processCCPayment($data, $event);
            if (isset($ccPaymentResponse['status']) && !$ccPaymentResponse['status']) {
                return response()->json($ccPaymentResponse);
            }
        }
        // if ($request->payment_type == 'WALLET') {
        //     $user = Auth::guard('appuser')->user()->id;
        //     $user = AppUser::find($user);
        //     if ($user->balance >= $request->payment) {
        //         $user->withdraw($request->payment, ['event_id' => $request->ticket_id]);
        //     } else {
        //         return response()->json(['success' => false, 'message' => 'Insufficient balance']);
        //     }
        // } else if ($request->payment_type == 'CLOVER') {

        // }

        $org = User::find($event->user_id);
        $data['order_id'] = '#' . rand(9999, 100000);
        $data['event_id'] = $event->id;
        $data['customer_id'] = $user->id;
        $data['organization_id'] = $org->id;
        $data['order_status'] = 'Pending';

        if ($data['payment_type'] == 'LOCAL') {
            $data['payment_status'] = 0;
            $data['order_status'] = 'Pending';
        } else {
            $data['payment_status'] = 1;
            $data['order_status'] = 'Complete';
        }

        $com = Setting::find(1, ['org_commission_type', 'org_commission']);


        if ($data['coupon_code'] != null) {
            $count = Coupon::find($data['coupon_code'])->use_count;
            $count = $count + 1;
            Coupon::find($data['coupon_code'])->update(['use_count' => $count]);
            CouponUsageHistory::create([
                'coupon_id' => $data['coupon_code'],
                'appuser_id' => $user->id
            ]);
        }

        $data['book_seats'] = isset($data['selectedSeatsId']) ? $data['selectedSeatsId'] : null;
        $data['seat_details'] = isset($data['selectedSeats']) ? $data['selectedSeats'] : null;
        $data['is_guest'] = (auth()->guard('appuser')->guest()) ? 1 : 0;
        $order = Order::create($data);
        $module = Module::where('module', 'Seatmap')->first();
        if ($module->is_enable == 1 && $module->is_install == 1) {
            $seats = explode(',', $data['selectedSeatsId']);
            foreach ($seats as $key => $value) {
                $seat = Seats::find($value);
                if ($seat) {
                    $seat->update(['type' => 'occupied']);
                }
            }
        }

        // for ($i = 1; $i <= $request->quantity; $i++) {
        // $child['ticket_number'] = uniqid();
        // $child['ticket_id'] = $request->ticket_id;
        // $child['order_id'] = $order->id;
        // $child['customer_id'] = Auth::guard('appuser')->user()->id;
        // $child['checkin'] = $ticket->maximum_checkins ?? null;
        // $child['paid'] = $request->payment_type == 'LOCAL' ? 0 : 1;
        // $child['qty'] = $request->payment_type == 'LOCAL' ? 0 : 1;
        // $child['price'] = $request->payment_type == 'LOCAL' ? 0 : 1;
        // $child['field_data'] = $request->payment_type == 'LOCAL' ? 0 : 1;
        // OrderChild::create($child);
        // }
        $qtyBooked = 0;
        $totalFee = 0;
        $totalFee2 = 0;
        $totalInsurance = 0;
        $totalTicketAmount = 0;
        if (!empty($cartArray)) {
            foreach ($cartArray as $ticketId => $cart) {
                $qtyBooked += $cart['qty'];
                $form_json = Ticket::find($ticketId)->form->field_data;
                for ($x = 1; $x <= $cart['qty']; $x++) {
                    $form_inputs = (isset($additionalFormData['additional_inputs'][$ticketId][$x])) ? $additionalFormData['additional_inputs'][$ticketId][$x] : [];
                    $child['ticket_number'] = uniqid();
                    $child['ticket_id'] = $ticketId;
                    $child['order_id'] = $order->id;
                    $child['customer_id'] = $user->id;
                    $child['checkin'] = $ticket->maximum_checkins ?? null;
                    $child['paid'] = $data['payment_type'] == 'LOCAL' ? 0 : 1;
                    $child['qty'] = 1;
                    $child['price'] = $cart['price'];
                    $child['fee'] = $cart['fee'];
                    $child['fee_2'] = $cart['fee_2'];
                    $child['field_data'] = json_encode($form_inputs);
                    $child['form_json'] = $form_json;
                    $child['insurance'] = (isset($cart['has_insurance']) && $cart['has_insurance'] == '1') ? $cart['insurance'] : 0;
                    $totalFee += $child['fee'];
                    $totalFee2 += $child['fee_2'];
                    $totalInsurance += $child['insurance'];
                    OrderChild::create($child);
                    $totalTicketAmount += $cart['price'];
                }
            }
        }
        if ($org->refered_by > 0) {
            $referedUser = User::find($org->refered_by);
            if ($referedUser) {
                ReferalCommission::create([
                    'order_id' => $order->id,
                    'commission_from' => $org->id,
                    'commission_to' => $referedUser->id,
                    'amount' => (($totalTicketAmount * 1) / 100),
                ]);
            }
        }
        $order->fees = $totalFee;
        $order->fees_2 = $totalFee2;
        $order->insurance = $totalInsurance;
        // $p = $data['payment'] - $data['tax'];
        $p = $data['payment'];
        if ($data['payment_type'] == "FREE") {
            $order->org_commission = 0;
        } else {
            if ($com->org_commission_type == "percentage") {
                $order->org_commission = $p * $com->org_commission / 100;
            } else if ($com->org_commission_type == "amount") {
                $order->org_commission = $com->org_commission;
            }
        }
        $order->save();
        if (isset($data['tax_data'])) {
            foreach (json_decode($data['tax_data']) as $value) {
                $tax['order_id'] = $order->id;
                $tax['tax_id'] = $value->id;
                $tax['price'] = $value->price;
                OrderTax::create($tax);
            }
        }

        $user = AppUser::find($order->customer_id);
        $setting = Setting::find(1);

        // for user notification
        $message = NotificationTemplate::where('title', 'Book Ticket')->first()->message_content;
        $detail['user_name'] = $user->name . ' ' . $user->last_name;
        $detail['quantity'] = $qtyBooked;
        $detail['event_name'] = Event::find($order->event_id)->name;
        $detail['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $detail['app_name'] = $setting->app_name;
        $noti_data = ["{{user_name}}", "{{quantity}}", "{{event_name}}", "{{date}}", "{{app_name}}"];
        $message1 = str_replace($noti_data, $detail, $message);
        $notification = array();
        $notification['organizer_id'] = null;
        $notification['user_id'] = $user->id;
        $notification['order_id'] = $order->id;
        $notification['title'] = 'Ticket Booked';
        $notification['message'] = $message1;
        Notification::create($notification);
        if ($setting->push_notification == 1) {
            if ($user->device_token != null) {
                (new AppHelper)->sendOneSignal('user', $user->device_token, $message1);
            }
        }
        // for user mail
        $ticket_book = NotificationTemplate::where('title', 'Book Ticket')->first();
        $details['user_name'] = $user->name . ' ' . $user->last_name;
        $details['quantity'] = $qtyBooked;
        $details['event_name'] = Event::find($order->event_id)->name;
        $details['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $details['app_name'] = $setting->app_name;
        if ($setting->mail_notification == 1) {
            $invoiceOutput = $this->getInvoiceOutput($order->id);
            try {
                $qrcode = $order->order_id;
                \Mail::to($user->email)->send(new TicketBook($ticket_book->mail_content, $details, $ticket_book->subject, $qrcode, $invoiceOutput));
            } catch (\Throwable $th) {
                Log::info($th->getMessage());
            }
            // $this->sendMail($order->id);
        }

        // for Organizer notification
        $org = User::find($order->organization_id);
        $or_message = NotificationTemplate::where('title', 'Organizer Book Ticket')->first()->message_content;
        $or_detail['organizer_name'] = $org->first_name . ' ' . $org->last_name;
        $or_detail['user_name'] = $user->name . ' ' . $user->last_name;
        $or_detail['quantity'] = $request->quantity;
        $or_detail['event_name'] = Event::find($order->event_id)->name;
        $or_detail['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $or_detail['app_name'] = $setting->app_name;
        $or_noti_data = ["{{organizer_name}}", "{{user_name}}", "{{quantity}}", "{{event_name}}", "{{date}}", "{{app_name}}"];
        $or_message1 = str_replace($or_noti_data, $or_detail, $or_message);
        $or_notification = array();
        $or_notification['organizer_id'] = $org->id;
        $or_notification['user_id'] = null;
        $or_notification['order_id'] = $order->id;
        $or_notification['title'] = 'New Ticket Booked';
        $or_notification['message'] = $or_message1;
        Notification::create($or_notification);
        if ($setting->push_notification == 1) {
            if ($org->device_token != null) {
                (new AppHelper)->sendOneSignal('organizer', $org->device_token, $or_message1);
            }
        }
        // for Organizer mail
        $new_ticket = NotificationTemplate::where('title', 'Organizer Book Ticket')->first();
        $details1['organizer_name'] = $org->first_name . ' ' . $org->last_name;
        $details1['user_name'] = $user->name . ' ' . $user->last_name;
        $details1['quantity'] = $qtyBooked;
        $details1['event_name'] = Event::find($order->event_id)->name;
        $details1['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $details1['app_name'] = $setting->app_name;
        if ($setting->mail_notification == 1) {
            try {
                \Mail::to($org->email)->send(new TicketBookOrg($new_ticket->mail_content, $details1, $new_ticket->subject));
            } catch (\Throwable $th) {
                Log::info($th->getMessage());
            }
        }
        if ($data['is_guest']) {
            $request->session()->flash('success', true);
        }
        return response()->json(['success' => true, 'message' => 'Payment successful', 'is_guest' => $data['is_guest']]);
    }
    public function sendMail($id)
    {
        $order = Order::with(['customer', 'event', 'organization', 'ticket'])->find($id);
        $order->tax_data = OrderTax::where('order_id', $order->id)->get();
        $order->ticket_data = OrderChild::where('order_id', $order->id)->get();
        $customPaper = array(0, 0, 720, 1440);
        $pdf = FacadePdf::loadView('ticketmail', compact('order'))->save(public_path("ticket.pdf"))->setPaper($customPaper, $orientation = 'portrait');
        $data["email"] = $order->customer->email;
        $data["title"] = "Ticket PDF";
        $data["body"] = "";
        $tempp = $pdf->output();
        $sender = Setting::select('sender_email', 'app_name')->first();
        try {
            Mail::send('mail', $data, function ($message) use ($data, $tempp, $sender) {
                $message->from($sender->sender_email, $sender->app_name)
                    ->to($data["email"])
                    ->subject($data["title"])
                    ->attachData($tempp, "ticket.pdf");
            });
        } catch (Throwable $th) {
            Log::info($th->getMessage());
        }
        return true;
    }

    public function getInvoiceOutput($id)
    {
        $order = Order::with(['customer', 'event', 'organization', 'ticket'])->find($id);
        $order->tax_data = OrderTax::where('order_id', $order->id)->get();
        $order->ticket_data = OrderChild::where('order_id', $order->id)->get();
        $customPaper = array(0, 0, 720, 1440);
        $pdf = FacadePdf::loadView('ticketmail', compact('order'))->save(public_path("ticket.pdf"))->setPaper($customPaper, $orientation = 'portrait');
        $data["email"] = $order->customer->email;
        $data["title"] = "Ticket PDF";
        $data["body"] = "";
        $tempp = $pdf->output();
        return $tempp;
    }
    public function categoryEvents($id, $name)
    {
        $setting = Setting::first(['app_name', 'logo']);
        $category = Category::find($id);

        SEOMeta::setTitle($setting->app_name . '- Events' ?? env('APP_NAME'))
            ->setDescription('This is category events page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'category event page',
                $category->name . ' - Events',
                $setting->app_name,
                $setting->app_name . ' Events',
                'events page',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - Events' ?? env('APP_NAME'))
            ->setDescription('This is category events page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - Events' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is category events page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - Events' ?? env('APP_NAME'));
        SEOTools::setDescription('This is category events page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            'category event page',
            $category->name . ' - Events',
            $setting->app_name,
            $setting->app_name . ' Events',
            'events page',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);

        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $events = Event::with(['category:id,name'])
            ->where([['status', 1], ['is_deleted', 0], ['category_id', $id], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d H:i:s')]])
            ->orderBy('start_time', 'ASC')->get();
        $offlinecount = 0;
        $onlinecount = 0;
        foreach ($events as $key => $value) {
            if ($value->type == 'online') {
                $onlinecount += 1;
            }
            if ($value->type == 'offline') {
                $offlinecount += 1;
            }
        }
        $user = Auth::guard('appuser')->user();
        $catactive = $name;
        return view('frontend.events', compact('events', 'category', 'onlinecount', 'offlinecount', 'user', 'catactive'));
    }

    public function eventType($type)
    {
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'))
            ->setDescription('This is all events page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'all event page',
                $setting->app_name,
                $setting->app_name . ' All-Events',
                'events page',
                $setting->app_name . ' Events',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'))
            ->setDescription('This is all events page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is all events page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - All-Events' ?? env('APP_NAME'));
        SEOTools::setDescription('This is all events page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            'all event page',
            $setting->app_name,
            $setting->app_name . ' All-Events',
            'events page',
            $setting->app_name . ' Events',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);


        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        if ($type == "all") {
            $events = Event::with(['category:id,name'])
                ->where([['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d H:i:s')]])
                ->orderBy('start_time', 'ASC')->get();

            return view('frontend.events', compact('events'));
        } else {
            $events = Event::with(['category:id,name'])
                ->where([['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['type', $type], ['end_time', '>', $date->format('Y-m-d H:i:s')]])
                ->orderBy('start_time', 'ASC')->get();
            return view('frontend.events', compact('events', 'type'));
        }
    }

    public function allCategory()
    {
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - Category' ?? env('APP_NAME'))
            ->setDescription('This is all category page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'all event page',
                $setting->app_name,
                $setting->app_name . ' Category',
                'category page',
                $setting->app_name . ' category',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - Category' ?? env('APP_NAME'))
            ->setDescription('This is all category page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - Category' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is all category page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - Category' ?? env('APP_NAME'));
        SEOTools::setDescription('This is all category page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            'all event page',
            $setting->app_name,
            $setting->app_name . ' Category',
            'category page',
            $setting->app_name . ' category',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        $data = Category::where('status', 1)->orderBy('id', 'DESC')->get();
        $catactive = 'all';

        return view('frontend.allCategory', compact('data', 'catactive'));
    }

    public function blogs()
    {
        $blogs = Blog::where('status', 1)->orderBy('id', 'DESC')->get();
        $category = Category::where('status', 1)->orderBy('id', 'DESC')->get();
        $setting = Setting::first(['app_name', 'logo']);
        SEOMeta::setTitle($setting->app_name . ' - Blogs' ?? env('APP_NAME'))
            ->setDescription('This is blogs page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'blogs page',
                $setting->app_name,
                $setting->app_name . ' Blogs',
                'blog page',
            ]);
        OpenGraph::setDescription('This is blogs page');
        OpenGraph::setTitle($setting->app_name . ' - Blogs' ?? env('APP_NAME'));
        OpenGraph::setUrl(url()->current());
        OpenGraph::addProperty('type', 'blogs');
        JsonLd::setTitle($setting->app_name . ' - Blogs' ?? env('APP_NAME'));
        JsonLd::setDescription('This is blogs page');
        JsonLd::addImage($setting->imagePath . $setting->logo);
        SEOTools::setTitle($setting->app_name . ' - Blogs' ?? env('APP_NAME'));
        SEOTools::setDescription('This is blogs page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('type', 'blogs');
        $user = Auth::guard('appuser')->user();
        return view('frontend.blog', compact('blogs', 'category', 'user'));
    }

    public function blogDetail($id, $name)
    {
        $setting = Setting::first(['app_name', 'logo']);

        $data = Blog::find($id);
        $data->category = Category::find($data->category_id);
        $tags = explode(',', $data->tags);
        SEOMeta::setTitle($data->title);
        SEOMeta::setDescription($data->description);
        SEOMeta::addMeta('blog:published_time', $data->created_at->toW3CString(), 'property');
        SEOMeta::addMeta('blog:category', $data->category->name, 'property');
        SEOMeta::addKeyword($data->tags);

        OpenGraph::setTitle($data->title)
            ->setDescription($data->description)
            ->setType('blog')
            ->addImage($data->imagePath . $data->image)
            ->setArticle([
                'published_time' => $data->created_at,
                'modified_time' => $data->updated_at,
                'section' => $data->category->name,
                'tag' => $data->tags
            ]);

        JsonLd::setTitle($data->title);
        JsonLd::setDescription($data->description);
        JsonLd::setType('Blog');
        JsonLd::addImage($data->imagePath . $data->image);
        $user = Auth::guard('appuser')->user();

        return view('frontend.blogDetail', compact('data', 'tags', 'user'));
    }

    public function profile()
    {
        $user = Auth::guard('appuser')->user();
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle('User Profile')
            ->setDescription('This is user profile page')
            ->addKeyword([
                $setting->app_name,
                $user->name,
                $user->name . ' ' . $user->last_name,
            ]);

        OpenGraph::setTitle('User Profile')
            ->setDescription('This is user profile page')
            ->setType('profile')
            ->setUrl(url()->current())
            ->addImage($user->imagePath . $user->image)
            ->setProfile([
                'first_name' => $user->name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'bio' => $user->bio,
                'country' => $user->country,
            ]);

        JsonLd::setTitle('User Profile' ?? env('APP_NAME'))
            ->setDescription('This is user profile page')
            ->setType('Profile')
            ->addImage($user->imagePath . $user->image);

        SEOTools::setTitle('User Profile' ?? env('APP_NAME'));
        SEOTools::setDescription('This is user profile page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $user->name,
            $user->name . ' ' . $user->last_name,
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        SEOTools::jsonLd()->addImage($user->imagePath . $user->image);

        $user->saved_event = Event::whereIn('id', array_filter(explode(',', $user->favorite)))->where([['status', 1], ['is_deleted', 0]])->get();
        $user->saved_blog = Blog::whereIn('id', array_filter(explode(',', $user->favorite_blog)))->where('status', 1)->get();
        $user->following = User::whereIn('id', array_filter(explode(',', $user->following)))->get();
        foreach ($user->saved_event as $value) {
            $value->total_ticket = Ticket::where([['event_id', $value->id], ['is_deleted', 0], ['status', 1]])->sum('quantity');
            $value->sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->available_ticket = $value->total_ticket - $value->sold_ticket;
        }
        return view('frontend.profile', compact('user'));
    }

    public function update_profile()
    {
        $user = Auth::guard('appuser')->user();
        $phone = Country::get();
        $languages = Language::where('status', 1)->get();
        return view('frontend.user_profile', compact('user', 'languages', 'phone'));
    }

    public function update_user_profile(Request $request)
    {
        $data = $request->all();
        $user = Auth::guard('appuser')->user();
        $user->update($data);
        $this->setLanguage($user);
        return redirect('/user/profile');
    }

    public function setLanguage($user)
    {
        $name = $user->language;
        if (!$name) {
            $name = 'English';
        }
        App::setLocale($name);
        session()->put('locale', $name);
        $direction = Language::where('name', $name)->first()->direction;
        session()->put('direction', $direction);
        return true;
    }

    public function addFavorite($id, $type)
    {
        $users = AppUser::find(Auth::guard('appuser')->user()->id);
        if ($type == "event") {
            $likes = array_filter(explode(',', $users->favorite));
            if (count(array_keys($likes, $id)) > 0) {
                if (($key = array_search($id, $likes)) !== false) {
                    unset($likes[$key]);
                }
                $msg = "Remove event from Favorite!";
            } else {
                array_push($likes, $id);
                $msg = "Add event in Favorite!";
            }
            $client = AppUser::find(Auth::guard('appuser')->user()->id);
            $client->favorite = implode(',', $likes);
        } else if ($type == "blog") {
            $likes = array_filter(explode(',', $users->favorite_blog));
            if (count(array_keys($likes, $id)) > 0) {
                if (($key = array_search($id, $likes)) !== false) {
                    unset($likes[$key]);
                }
                $msg = "Remove blog from Favorite!";
            } else {
                array_push($likes, $id);
                $msg = "Add blog in Favorite!";
            }
            $client = AppUser::find(Auth::guard('appuser')->user()->id);
            $client->favorite_blog = implode(',', $likes);
        }
        $client->update();
        return response()->json(['msg' => $msg, 'success' => true, 'type' => $type], 200);
    }

    public function addFollow($id)
    {
        $users = AppUser::find(Auth::guard('appuser')->user()->id);
        $likes = array_filter(explode(',', $users->following));
        if (count(array_keys($likes, $id)) > 0) {
            if (($key = array_search($id, $likes)) !== false) {
                unset($likes[$key]);
            }
            $msg = "Remove from following list!";
        } else {
            array_push($likes, $id);
            $msg = "Add in following!";
        }
        $client = AppUser::find(Auth::guard('appuser')->user()->id);
        $client->following = implode(',', $likes);
        $client->update();
        return response()->json(['msg' => $msg, 'success' => true], 200);
    }

    public function addBio(Request $request)
    {
        $success = AppUser::find(Auth::guard('appuser')->user()->id)->update(['bio' => $request->bio]);
        return response()->json(['data' => $request->bio, 'success' => $success], 200);
    }

    public function changePassword()
    {
        $setting = Setting::first(['app_name', 'logo']);

        SEOMeta::setTitle($setting->app_name . ' - Change Password' ?? env('APP_NAME'))
            ->setDescription('This is change password page')
            ->setCanonical(url()->current())
            ->addKeyword([
                'change password page',
                $setting->app_name,
                $setting->app_name . ' Change Password'
            ]);

        OpenGraph::setTitle($setting->app_name . ' - Change Password' ?? env('APP_NAME'))
            ->setDescription('This is change password page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - Change Password' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is change password page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - Change Password' ?? env('APP_NAME'));
        SEOTools::setDescription('This is change password page');
        SEOTools::opengraph()->addProperty('keywords', [
            'change password page',
            $setting->app_name,
            $setting->app_name . ' Change Password'
        ]);
        SEOTools::opengraph()->addProperty('image', $setting->imagePath . $setting->logo);
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);

        return view('frontend.auth.changePassword');
    }

    public function changeUserPassword(Request $request)
    {
        $request->validate([
            // 'old_password' => 'bail|required',
            'password' => 'bail|required|min:6',
            'password_confirmation' => 'bail|required|same:password|min:6'
        ]);
        // if (Hash::check($request->old_password, Auth::guard('appuser')->user()->password)) {

        // } else {
        //     return Redirect::back()->with('error_msg', 'Current Password is wrong!');
        // }
        AppUser::find(Auth::guard('appuser')->user()->id)->update(['password' => Hash::make($request->password)]);
        return redirect('user/profile')->withStatus(__('Password is changed successfully.'));
    }

    public function uploadProfileImage(Request $request)
    {
        $appuser = AppUser::find(Auth::guard('appuser')->user());
        if ($request->hasFile('image') != 'defaultuser.png') {
            $request->validate([
                'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:3048',
            ]);
            (new AppHelper)->deleteFile($appuser->image);
            $imageName = (new AppHelper)->saveImage($request);
            AppUser::find(Auth::guard('appuser')->user()->id)->update(['image' => $imageName]);
        } else {
            $request->validate([
                'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:3048',
            ]);
            $imageName = (new AppHelper)->saveImage($request);
            AppUser::find(Auth::guard('appuser')->user()->id)->update(['image' => $imageName]);
        }
        return response()->json(['data' => $imageName, 'success' => true], 200);
    }

    public function contact()
    {
        $setting = Setting::first(['app_name', 'logo']);
        $data = ContactUs::find(1);
        SEOMeta::setTitle($setting->app_name . ' - Contact Us' ?? env('APP_NAME'))
            ->setDescription('This is contact us page')
            ->setCanonical(url()->current())
            ->addKeyword([
                $setting->app_name,
                $setting->app_name . ' Contact Us',
                'contact us page',
            ]);

        OpenGraph::setTitle($setting->app_name . ' - Contact Us' ?? env('APP_NAME'))
            ->setDescription('This is contact us page')
            ->setUrl(url()->current());

        JsonLdMulti::setTitle($setting->app_name . ' - Contact Us' ?? env('APP_NAME'));
        JsonLdMulti::setDescription('This is contact us page');
        JsonLdMulti::addImage($setting->imagePath . $setting->logo);

        SEOTools::setTitle($setting->app_name . ' - Contact Us' ?? env('APP_NAME'));
        SEOTools::setDescription('This is contact us page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $setting->app_name . ' Contact Us',
            'contact us page',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        if ($data) {
            return view('frontend.contact', compact('data'));
        }
        return view('frontend.contact');
    }

    public function support()
    {
        return view('frontend.support');
    }


    public function Volunteering()
    {
        return view('frontend.Volunteering');
    }


    public function userTickets()
    {

        $user = Auth::guard('appuser')->user();
        $setting = Setting::first(['app_name', 'logo', 'currency']);
        SEOMeta::setTitle('User Tickets')
            ->setDescription('This is user tickets page')
            ->addKeyword([
                $setting->app_name,
                $user->name,
                $user->name . ' ' . $user->last_name,
                $user->name . ' ' . $user->last_name . ' tickets',
            ]);

        OpenGraph::setTitle('User Tickets')
            ->setDescription('This is user tickets page')
            ->setUrl(url()->current())
            ->addImage($user->imagePath . $user->image);


        JsonLd::setTitle('User Tickets' ?? env('APP_NAME'))
            ->setDescription('This is user tickets page')
            ->addImage($user->imagePath . $user->image);

        SEOTools::setTitle('User Tickets' ?? env('APP_NAME'));
        SEOTools::setDescription('This is user tickets page');
        SEOTools::opengraph()->setUrl(url()->current());
        SEOTools::setCanonical(url()->current());
        SEOTools::opengraph()->addProperty('keywords', [
            $setting->app_name,
            $user->name,
            $user->name . ' ' . $user->last_name,
            $user->name . ' ' . $user->last_name . ' tickets',
        ]);
        SEOTools::jsonLd()->addImage($setting->imagePath . $setting->logo);
        SEOTools::jsonLd()->addImage($user->imagePath . $user->image);
        (new AppHelper)->eventStatusChange();
        $ordertax = array();
        $tax = array();

        $ticket['upcoming'] = Order::with(['event:id,name,image,start_time,type,end_time,address', 'ticket:id,ticket_number,start_time,name,price,type', 'organization:id,first_name,last_name,image'])
            ->where([['customer_id', Auth::guard('appuser')->user()->id], ['order_status', 'Pending'], ['is_guest', '0']])
            ->orWhere([['customer_id', Auth::guard('appuser')->user()->id], ['order_status', 'Complete']])
            ->orderBy('id', 'DESC')->paginate(10);
        $event = [];

        if (count($ticket['upcoming']) > 0) {
            foreach ($ticket['upcoming'] as $events) {
                if ($events->event->start_time <= Carbon::now() && $events->event->end_time >= Carbon::now()) {
                    $event[] = $events;
                }
            }
            $ticket['upcoming']->event = $event;
        }

        $ordertax = array();
        $tax = array();
        foreach ($ticket['upcoming'] as $item) {
            $ordertaxs = OrderTax::where('order_id', $item->id)->get();
            $ordertax = $ordertaxs;
        }
        foreach ($ordertax as $item) {
            $taxs = Tax::find($item->tax_id)->get();
            $tax = $taxs;
        }
        $ticket['upcoming']->maintax = $tax;


        $ticket['past'] = Order::with(['event:id,name,image,start_time,type,end_time,address', 'ticket:id,ticket_number,name,type,price', 'organization:id,first_name,last_name,image'])
            ->where([['customer_id', Auth::guard('appuser')->user()->id], ['order_status', 'Cancel'], ['is_guest', '0']])
            ->orderBy('id', 'DESC')->paginate(10);
        if (count($ticket['past']) > 0) {
            foreach ($ticket['past'] as $events) {
                if ($events->event->end_time <= Carbon::now()) {
                    $event[] = $events;
                }
            }
            $ticket['past']->event = $event;
        }
        foreach ($ticket['past'] as $item) {
            $ordertaxs = OrderTax::where('order_id', $item->id)->get();
            $ordertax = $ordertaxs;
        }

        foreach ($ordertax as $item) {
            $taxs = Tax::find($item->tax_id)->get();
            $tax = $taxs;
        }
        $ticket['past']->maintax = $tax;

        $likedEvents = Event::whereIn('id', array_filter(explode(',', $user->favorite)))->where([['status', 1], ['is_deleted', 0]])->orderBy('id', 'DESC')->get();
        foreach ($likedEvents as $value) {
            $value->description = str_replace("&nbsp;", " ", strip_tags($value->description));
            $value->time = $value->start_time->format('d F Y h:i a');
        }
        $likedBlogs = Blog::whereIn('id', array_filter(explode(',', $user->favorite_blog)))->where([['status', 1]])->orderBy('id', 'DESC')->get();
        $userFollowing = User::whereIn('id', array_filter(explode(',', $user->following)))->where([['status', 1]])->orderBy('id', 'DESC')->get();
        $wallet = PaymentSetting::first()->wallet;
        return view('frontend.userTickets', compact('likedEvents', 'ticket', 'likedBlogs', 'userFollowing', 'wallet'));
    }
    public function userOrderTicket($id)
    {
        $order = Order::with(['event', 'organization'])->find($id);
        $taxes_id = OrderTax::where('order_id', $order->id)->get();
        // $coupon = Coupon::find($order->coupon_id);
        $taxes = [];
        foreach ($taxes_id as $key => $value) {
            $temp_tax[] = Tax::find($value->tax_id);
            $taxes = $temp_tax;
        }
        $orderchild = OrderChild::where('order_id', $order->id)->get();
        $review = Review::where('order_id', $order->id)->first();
        return view('frontend.userOrderTicket', compact('order', 'taxes', 'review', 'orderchild'));
    }
    public function getOrder($id)
    {
        $data = Order::with(['event:id,name,image,start_time,type,end_time,address', 'ticket:id,ticket_number,name,price,type', 'organization:id,first_name,last_name,image'])->find($id);
        $data->review = Review::where('order_id', $id)->first();
        $data->time = $data->created_at->format('D') . ', ' . $data->created_at->format('d M Y') . ' at ' . $data->created_at->format('h:i a');
        $data->start_time = $data->event->start_time->format('d M Y') . ', ' . $data->event->start_time->format('h:i a');
        $data->end_time = $data->event->end_time->format('d M Y') . ', ' . $data->event->end_time->format('h:i a');
        $taxs = array();
        $ordertax = OrderTax::where('order_id', $id)->get();
        foreach ($ordertax as $item) {
            $taxs = Tax::find($item->tax_id)->get();
            $taxs = $taxs;
        }
        $data->maintax = $taxs;

        return response()->json(['data' => $data, 'success' => true], 200);
    }

    public function addReview(Request $request)
    {
        $data = $request->all();
        $data['organization_id'] = Order::find($request->order_id)->organization_id;
        $data['event_id'] = Order::find($request->order_id)->event_id;
        $data['user_id'] = Auth::guard('appuser')->user()->id;
        $data['status'] = 0;
        $review = Review::where('order_id', $data['order_id'])->first();
        if (isset($review->id)) {
            $review->message = $data['message'];
            $review->rate = $data['rate'];
            $review->save();
        } else {
            Review::create($data);
        }
        return redirect()->back();
    }

    public function sentMessageToAdmin(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'email' => 'required',
            'subject' => 'required',
            'msg' => 'required',
            'g-recaptcha-response' => 'required',
        ]);
        $secretKey = '6LdjlfUpAAAAADIrjR7RT6YhkUwm_IvjeEunfzEn';
        $recaptchaResponse = $request->input('g-recaptcha-response');

        $response = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret={$secretKey}&response={$recaptchaResponse}");
        $responseKeys = json_decode($response, true);
        if ($responseKeys['success'] && $responseKeys['score'] >= 0.5) {
            $data = $request->all();
            try {
                Mail::send('emails.message', ['data' => $data], function ($message) use ($data) {
                    $setting = Setting::first();
                    $message->from($setting->sender_email);
                    $message->to(User::find(1)->email);
                    $message->subject($data['subject']);
                });
            } catch (Throwable $th) {
                Log::info($th->getMessage());
            }
            return redirect('/contact')->withStatus(__('We have received your message once we reviewed we will contact you soon.'));
        } else {
            return response()->json(['error' => 1, 'Message' => 'reCAPTCHA verification failed. Please try again.']);
        }
    }

    public function privacypolicy()
    {
        $policy = Setting::find(1)->privacy_policy_organizer;
        return view('frontend.privacy-policy', compact('policy'));
    }

    public function appuserPrivacyPolicyShow(Request $request)
    {
        $policy = Setting::find(1)->appuser_privacy_policy;
        return view('frontend.privacy-policy', compact('policy'));
    }
    public function searchEvent(Request $request)
    {
        $search = $request->search ?? '';
        if ($search == '') {
            return redirect()->back();
        }
        $timezone = Setting::find(1)->timezone;
        $date = Carbon::now($timezone);
        $events = Event::with(['category:id,name'])
            ->where([['address', 'LIKE', "%$search%"], ['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d')]])
            ->orWhere([['name', 'LIKE', "%$search%"], ['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d')]])
            // ->orWhere([['description', 'LIKE', "%$search%"], ['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d')]]);
            ->orWhere([['tags', 'LIKE', "%$search%"], ['status', 1], ['is_deleted', 0], ['event_status', 'Pending'], ['end_time', '>', $date->format('Y-m-d')]]);
        $chip = array();
        if ($request->has('type') && $request->type != null) {
            $chip['type'] = $request->type;
            $events = $events->where('type', $request->type);
        }
        if ($request->has('category') && $request->category != null) {
            $chip['category'] = Category::find($request->category)->name;
            $events = $events->where('category_id', $request->category);
        }
        if ($request->has('duration') && $request->duration != null) {
            $chip['date'] = $request->duration;
            if ($request->duration == 'Today') {
                $temp = Carbon::now($timezone)->format('Y-m-d');
                $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
            } else if ($request->duration == 'Tomorrow') {
                $temp = Carbon::tomorrow($timezone)->format('Y-m-d');
                $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
            } else if ($request->duration == 'ThisWeek') {
                $now = Carbon::now($timezone);
                $weekStartDate = $now->startOfWeek()->format('Y-m-d H:i:s');
                $weekEndDate = $now->endOfWeek()->format('Y-m-d H:i:s');
                $events = $events->whereBetween('start_time', [$weekStartDate, $weekEndDate]);
            } else if ($request->duration == 'date') {
                if (isset($request->date)) {
                    $temp = Carbon::parse($request->date)->format('Y-m-d H:i:s');
                    $events = $events->whereBetween('start_time', [$request->date . ' 00:00:00', $request->date . ' 23:59:59']);
                }
            }
        }
        $events = $events->orderBy('start_time', 'ASC')->get();
        foreach ($events as $value) {
            $value->total_ticket = Ticket::where([['event_id', $value->id], ['is_deleted', 0], ['status', 1]])->sum('quantity');
            $value->sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->available_ticket = $value->total_ticket - $value->sold_ticket;
        }
        $user = Auth::guard('appuser')->user();
        $offlinecount = 0;
        $onlinecount = 0;
        foreach ($events as $key => $value) {
            if ($value->type == 'online') {
                $onlinecount += 1;
            }
            if ($value->type == 'offline') {
                $offlinecount += 1;
            }
        }

        return view('frontend.events', compact('user', 'events', 'chip', 'onlinecount', 'offlinecount'));
    }
    public function eventsByTag($tag)
    {
        $events = Event::where([['tags', 'LIKE', "%$tag%"], ['is_deleted', 0]])->get();
        $onlinecount = 0;
        $offlinecount = 0;
        foreach ($events as $key => $value) {
            if ($value->type == 'online') {
                $onlinecount += 1;
            } else {
                $offlinecount += 1;
            }
        }
        if (Auth::guard('appuser')->check()) {
            $user = Auth::guard('appuser')->user();
            return view('frontend.events', compact('events', 'onlinecount', 'offlinecount', 'user'));
        }
        return view('frontend.events', compact('events', 'onlinecount', 'offlinecount'));
    }
    public function blogByTag($tag)
    {
        $blogs = Blog::where('tags', 'LIKE', "%$tag%")->where('status', 1)->orderBy('id', 'DESC')->get();
        $category = Category::where('status', 1)->orderBy('id', 'DESC')->get();
        if (Auth::guard('appuser')->user()) {
            $user = Auth::guard('appuser')->user();
            return view('frontend.blog', compact('blogs', 'category', 'user'));
        }
        return view('frontend.blog', compact('blogs', 'category'));
    }
    public function Faqs()
    {
        $data = Faq::where('status', 1)->get();
        return view('frontend.show_faq', compact('data'));
    }
    public function otpView($id)
    {
        $user = AppUser::find($id);
        return view('frontend.auth.otp', compact('user'));
    }
    public function otpViewOrganizer($id)
    {
        $user = User::find($id);
        return view('frontend.auth.otporganizer', compact('user'));
    }
    public function otpVerify(Request $request)
    {
        $request->validate([
            'otp' => 'required',
        ]);
        $user = AppUser::find($request->id);
        if ($user->otp == $request->otp) {
            $user->otp = null;
            $user->is_verify = 1;
            $user->update();
            Auth::guard('appuser')->login($user);
            return redirect('/');
        } else {
            return redirect()->back()->with('error', 'Wrong OTP. Please try again.');
        }
    }
    public function otpVerifyOrganizer(Request $request)
    {
        $request->validate([
            'otp' => 'required',
        ]);
        $user = User::find($request->id);
        if ($user->otp == $request->otp) {
            $user->otp = null;
            $user->update();
            Auth::login($user);
            return redirect('/');
        } else {
            return redirect()->back()->with('error', 'Wrong OTP. Please try again.');
        }
    }
    public function checkoutSession(Request $request)
    {
        $request->session()->put('request', $request->all());
        $key = PaymentSetting::first()->stripeSecretKey;
        Stripe::setApiKey($key);
        $supportedCurrency = [
            "EUR",   # Euro
            "GBP",   # British Pound Sterling
            "CAD",   # Canadian Dollar
            "AUD",   # Australian Dollar
            "JPY",   # Japanese Yen
            "CHF",   # Swiss Franc
            "NZD",   # New Zealand Dollar
            "HKD",   # Hong Kong Dollar
            "SGD",   # Singapore Dollar
            "SEK",   # Swedish Krona
            "DKK",   # Danish Krone
            "PLN",   # Polish Złoty
            "NOK",   # Norwegian Krone
            "CZK",   # Czech Koruna
            "HUF",   # Hungarian Forint
            "ILS",   # Israeli New Shekel
            "MXN",   # Mexican Peso
            "BRL",   # Brazilian Real
            "MYR",   # Malaysian Ringgit
            "PHP",   # Philippine Peso
            "TWD",   # New Taiwan Dollar
            "THB",   # Thai Baht
            "TRY",   # Turkish Lira
            "RUB",   # Russian Ruble
            "INR",   # Indian Rupee
            "ZAR",   # South African Rand
            "AED",   # United Arab Emirates Dirham
            "SAR",   # Saudi Riyal
            "KRW",   # South Korean Won
            "CNY"    # Chinese Yuan
        ];
        $amount = $request->payment;
        if (!in_array($request->currency, $supportedCurrency)) {
            $amount = $amount * 100;
        }
        $currencyCode = Setting::first()->currency;
        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'line_items' => [
                [
                    'price_data' => [
                        'currency' => $currencyCode,
                        'product_data' => [
                            'name' => "Payment"
                        ],
                        'unit_amount' => $amount,
                    ],
                    'quantity' => 1,
                ]
            ],
            'mode' => 'payment',
            'success_url' => route('stripe.success'),
            'cancel_url' => route('stripe.cancel'),
        ]);
        return response()->json(['id' => $session->id, 'status' => 200]);
    }
    public function stripeSuccess()
    {
        $request = Session::get('request');

        $ticket = Ticket::findOrFail($request['ticket_id']);

        $event = Event::find($ticket->event_id);

        $org = User::find($event->user_id);
        $user = AppUser::find(Auth::guard('appuser')->user()->id);
        $data['order_id'] = '#' . rand(9999, 100000);
        $data['event_id'] = $event->id;
        $data['customer_id'] = $user->id;
        $data['organization_id'] = $org->id;
        $data['order_status'] = 'Pending';
        $data['ticket_id'] = $request['ticket_id'];
        $data['quantity'] = $request['quantity'];
        $data['payment_type'] = 'Stripe';
        $data['payment'] = $request['payment'];
        $data['tax'] = $request['tax'];
        $data['coupon_id'] = $request['coupon_id'] ?? null;
        $data['payment_status'] = 1;
        $data['order_status'] = 'Complete';
        $com = Setting::find(1, ['org_commission_type', 'org_commission']);
        $p = $request['payment'] - $request['tax'];
        if ($request['payment_type'] == "FREE") {
            $data['org_commission'] = 0;
        } else {
            if ($com->org_commission_type == "percentage") {
                $data['org_commission'] = $p * $com->org_commission / 100;
            } else if ($com->org_commission_type == "amount") {
                $data['org_commission'] = $com->org_commission;
            }
        }

        if (isset($request['coupon_id'])) {
            $count = Coupon::find($request['coupon_id'])->use_count;
            $count = $count + 1;
            Coupon::find($request['coupon_id'])->update(['use_count' => $count]);
        }

        $data['book_seats'] = isset($request['selectedSeatsId']) ? $request['selectedSeatsId'] : null;
        $data['seat_details'] = isset($request['selectedSeats']) ? $request['selectedSeats'] : null;
        $order = Order::create($data);
        $module = Module::where('module', 'Seatmap')->first();
        if ($module->is_enable == 1 && $module->is_install == 1) {
            $seats = explode(',', $data['selectedSeatsId']);
            foreach ($seats as $key => $value) {
                $seat = Seats::find($value);
                if ($seat) {
                    $seat->update(['type' => 'occupied']);
                }
            }
        }

        for ($i = 1; $i <= $request['quantity']; $i++) {
            $child['ticket_number'] = uniqid();
            $child['ticket_id'] = $request['ticket_id'];
            $child['order_id'] = $order->id;
            $child['customer_id'] = Auth::guard('appuser')->user()->id;
            $child['checkin'] = $ticket->maximum_checkins ?? null;
            $child['paid'] = 1;
            OrderChild::create($child);
        }
        if (isset($request['tax_data'])) {
            foreach (json_decode($data['tax_data']) as $value) {
                $tax['order_id'] = $order->id;
                $tax['tax_id'] = $value->id;
                $tax['price'] = $value->price;
                OrderTax::create($tax);
            }
        }

        $user = AppUser::find($order->customer_id);
        $setting = Setting::find(1);

        // for user notification
        $message = NotificationTemplate::where('title', 'Book Ticket')->first()->message_content;
        $detail['user_name'] = $user->name . ' ' . $user->last_name;
        $detail['quantity'] = $request['quantity'];
        $detail['event_name'] = Event::find($order->event_id)->name;
        $detail['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $detail['app_name'] = $setting->app_name;
        $noti_data = ["{{user_name}}", "{{quantity}}", "{{event_name}}", "{{date}}", "{{app_name}}"];
        $message1 = str_replace($noti_data, $detail, $message);
        $notification = array();
        $notification['organizer_id'] = null;
        $notification['user_id'] = $user->id;
        $notification['order_id'] = $order->id;
        $notification['title'] = 'Ticket Booked';
        $notification['message'] = $message1;
        Notification::create($notification);
        if ($setting->push_notification == 1) {
            if ($user->device_token != null) {
                (new AppHelper)->sendOneSignal('user', $user->device_token, $message1);
            }
        }
        // for user mail
        $ticket_book = NotificationTemplate::where('title', 'Book Ticket')->first();
        $details['user_name'] = $user->name . ' ' . $user->last_name;
        $details['quantity'] = $request['quantity'];
        $details['event_name'] = Event::find($order->event_id)->name;
        $details['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $details['app_name'] = $setting->app_name;
        if ($setting->mail_notification == 1) {

            try {
                $qrcode = $order->order_id;
                \Mail::to($user->email)->send(new TicketBook($ticket_book->mail_content, $details, $ticket_book->subject, $qrcode));
            } catch (\Throwable $th) {
                Log::info($th->getMessage());
            }
            $this->sendMail($order->id);
        }

        // for Organizer notification
        $org = User::find($order->organization_id);
        $or_message = NotificationTemplate::where('title', 'Organizer Book Ticket')->first()->message_content;
        $or_detail['organizer_name'] = $org->first_name . ' ' . $org->last_name;
        $or_detail['user_name'] = $user->name . ' ' . $user->last_name;
        $or_detail['quantity'] = $request['quantity'];
        $or_detail['event_name'] = Event::find($order->event_id)->name;
        $or_detail['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $or_detail['app_name'] = $setting->app_name;
        $or_noti_data = ["{{organizer_name}}", "{{user_name}}", "{{quantity}}", "{{event_name}}", "{{date}}", "{{app_name}}"];
        $or_message1 = str_replace($or_noti_data, $or_detail, $or_message);
        $or_notification = array();
        $or_notification['organizer_id'] = $org->id;
        $or_notification['user_id'] = null;
        $or_notification['order_id'] = $order->id;
        $or_notification['title'] = 'New Ticket Booked';
        $or_notification['message'] = $or_message1;
        Notification::create($or_notification);
        if ($setting->push_notification == 1) {
            if ($org->device_token != null) {
                (new AppHelper)->sendOneSignal('organizer', $org->device_token, $or_message1);
            }
        }
        // for Organizer mail
        $new_ticket = NotificationTemplate::where('title', 'Organizer Book Ticket')->first();
        $details1['organizer_name'] = $org->first_name . ' ' . $org->last_name;
        $details1['user_name'] = $user->name . ' ' . $user->last_name;
        $details1['quantity'] = $request['quantity'];
        $details1['event_name'] = Event::find($order->event_id)->name;
        $details1['date'] = Event::find($order->event_id)->start_time->format('d F Y h:i a');
        $details1['app_name'] = $setting->app_name;
        if ($setting->mail_notification == 1) {
            try {
                $setting = Setting::first();
                \Mail::to($org->email)->send(new TicketBookOrg($new_ticket->mail_content, $details1, $new_ticket->subject));
            } catch (\Throwable $th) {
                Log::info($th->getMessage());
            }
        }
        Session::forget('request');
        return redirect()->route('myTickets');
    }
    public function striepCancel()
    {
        return redirect()->back();
    }


    public function calendar()
    {
        $events = Event::where('status', 1)->where('is_deleted', 0)->get();
        $organizations = Directory::where('status', 1)->get();

        $formattedOrganizations = $organizations->map(function ($orgnizations) {
            return [
                'id' => $orgnizations->id,
                'name' => $orgnizations->name
            ];
        });
        return view('frontend.calendar', compact('events', 'organizations'));
    }

    public function getEvents(Request $request, $year, $month)
    {
        $allEvents = Event::all();

        $events = Event::where('status', 1)
            ->where('is_deleted', 0)
            ->whereYear('start_time', $year)
            ->whereMonth('start_time', $month)
            ->get(['id', 'name', 'start_time', 'end_time', 'description', 'image', 'address']); // Include required fields


        // Format events to include readable time
        $formattedEvents = $events->map(function ($event) {
            return [
                'id' => $event->id,
                'name' => $event->name,
                'start_date' => $event->start_time->format('Y-m-d'),
                'start_time' => $event->start_time->format('h:i A'),
                'end_date' => $event->end_time->format('Y-m-d'),
                'end_time' => $event->end_time->format('h:i A'),
                'description' => $event->description,
                'image' => url('/images/upload/' . $event->image),
                'address' => $event->address,
            ];
        });
        return response()->json(['events' => $formattedEvents, $allEvents]);
    }


    public function getFilteredEvents(Request $request)
    {
        $query = Event::query();

        if ($request->filled('fromDate') && $request->filled('toDate')) {
            $query->where(function ($q) use ($request) {
                $q->whereBetween('start_time', [
                    Carbon::parse($request->fromDate)->format('Y-m-d H:i:s'),
                    Carbon::parse($request->toDate)->format('Y-m-d H:i:s')
                ])->orWhereBetween('end_time', [
                            Carbon::parse($request->fromDate)->format('Y-m-d H:i:s'),
                            Carbon::parse($request->toDate)->format('Y-m-d H:i:s')
                        ]);
            });
        }


        if ($request->filled('tags')) {
            $query->where('tags', 'LIKE', '%' . $request->tags . '%');
        }
        if ($request->filled('location')) {
            $query->where('address', 'LIKE', '%' . $request->location . '%');
        }



        if ($request->filled('organization')) {
            $query->where('id', $request->organization);  // Ensure your events table has an `organization_id` column
        }

        if ($request->filled('keyword')) {
            $query->where('name', 'LIKE', '%' . $request->keyword . '%')
                ->orWhere('description', 'LIKE', '%' . $request->keyword . '%');
        }

        $events = $query->get();
        $formattedEvents = $events->map(function ($event) {
            return [
                'id' => $event->id,
                'name' => $event->name,
                'date' => $event->start_time->format('Y-m-d'),
                'time' => $event->start_time->format('h:i A'),
                'description' => $event->description,
                'image' => url('/images/upload/' . $event->image),
                'address' => $event->address,
            ];
        });

        return response()->json(['events' => $formattedEvents]);
    }

    public function addEvent()
    {
        $organizations = Directory::where('status', 1)->get();
        $categories = DirectoryType::where('status', 1)->orderBy('id', 'DESC')->get();

        return view('frontend.add-event', compact('organizations', 'categories'));
    }

    public function getFilteredOrganizations(Request $request)
    {
        $directories = Directory::where('status', 1)
            ->where('directory_type', $request->id) // Ensure column name matches your database schema
            ->get();

        return response()->json($directories);
    }

    public function store(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'name' => 'bail|required',
            // 'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            'start_time' => 'bail|required',
            'end_time' => 'bail|required',
            'category_id' => 'bail|required|array',
            'type' => 'bail|required',
            'address' => 'bail|required_if:type,offline',
            'lat' => 'bail|required_if:type,offline',
            'lang' => 'bail|required_if:type,offline',
            // 'status' => 'bail|required',
            'url' => 'bail|required_if:type,online',
            'description' => 'bail|required',
            // 'scanner_id' => 'bail|required_if:type,offline',
            // 'people' => 'bail|required',
        ], [
            'lat.required_if' => 'Invalid Location',
            'lang.required_if' => 'Invalid Location',
        ]);
        $data = $request->all();

        if ($request->has('disclaimer')) {
            $request->validate([
                'disclaimer_text' => 'required'
            ]);
            $data['disclaimer'] = 1;
        } else {
            $data['disclaimer'] = 0;
        }

        // if ($request->type == 'offline') {
        //     $data['scanner_id'] = $request->scanner_id;
        // }
        $data['gallery'] = [];
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        if (!empty($data['gallery'])) {
            $data['image'] = $data['gallery'][0];
            unset($data['gallery'][0]);
        }

        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['security'] = 1;
        if ($request->hasFile('image')) {
            $data['image'] = (new AppHelper)->saveImage($request);
        }


        $data['is_external_link'] = $request->is_external_link ?? 0;
        $data['external_link'] = "";
        if ($data['is_external_link']) {
            $request->validate([
                'external_link' => 'required'
            ]);
            $data['external_link'] = $request->external_link;
        }

        $prefixUniqueCheck = false;
        while (!$prefixUniqueCheck) {
            $prefix = Str::random(9);
            $prefixUniqueCheckQuery = Event::where('prefix', $prefix)->first();
            if (!$prefixUniqueCheckQuery) {
                $data['prefix'] = $prefix;
                break;
            } else {
                $prefixUniqueCheck = false;
            }
        }
        if (!$data['is_external_link']) {
            $data['status'] = 0;
        }

        if (!empty($request->yt_url) && !filter_var($request->yt_url, FILTER_VALIDATE_URL)) {
            $data['yt_url'] = 'https://www.youtube.com/embed/' . $request->yt_url;
        }
        $data['people'] = $data['people'] ?? 0;
        $categoryIds = $data['category_id'];
        unset($data['category_id']);
        // dd($data);
        $event = Event::create($data);

        return redirect()->route('calendar')->withStatus(__('Event has added successfully. Please create the tickets to publish the event.'));
    }
}
