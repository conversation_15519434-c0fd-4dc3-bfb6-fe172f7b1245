<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RefundRequest extends Model
{
    use HasFactory;

    public static function manage($requestData)
    {
        if ($requestData['refund_for'] == '2') {
            foreach ($requestData['tickets'] as $ticket) {
                $rfRequest = new RefundRequest();
                $rfRequest->user_id = $requestData['user_id'];
                $rfRequest->order_id = $requestData['order_id'];
                $rfRequest->refund_mode = $requestData['refund_for'];
                $rfRequest->ticket_id = $ticket;
                $rfRequest->save();
            }
        } else {
            $rfRequest = new RefundRequest();
            $rfRequest->user_id = $requestData['user_id'];
            $rfRequest->order_id = $requestData['order_id'];
            $rfRequest->refund_mode = $requestData['refund_for'];
            $rfRequest->save();
        }
    }

    public function user()
    {
        return $this->hasOne('\App\Models\AppUser', 'id', 'user_id');
    }

    public function order()
    {
        return $this->hasOne('\App\Models\Order','id', 'order_id');
    }
}
