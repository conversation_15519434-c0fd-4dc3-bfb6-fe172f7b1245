<?php

namespace App\Http\Controllers;

use App\Mail\CustomOrgApproveMail;
use App\Mail\CustomOrgMail;
use App\Mail\JoinAsOrganizerSuccessMail;
use App\Models\DirectoryType;
use App\Models\Directory;
use App\Models\DirectoryTypeLink;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpFoundation\Response;
use Spatie\Permission\Models\Role;
use \App\Models\User;

class DirectoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $directories = Directory::all();
        $types = DirectoryType::where('status', 1)->get();
        $i = 1;
        return view('admin.directory.index', compact('directories', 'i', 'types'));
    }

    public function importDirectories(Request $request)
    {
        set_time_limit(0);
        $this->validate($request, [
            'directory_type' => 'required',
            'directory_file' => 'required|file|mimes:csv,txt|max:2048', // Validate as CSV file
        ]);
        $file = $request->file('directory_file');
        if (($handle = fopen($file->getPathname(), 'r')) !== false) {
            $headers = fgetcsv($handle);
            $csvRecords = [];
            while (($row = fgetcsv($handle)) !== false) {
                $csvRecords[] = array_combine($headers, $row); // Combine headers with row values
            }
            if (!empty($csvRecords)) {
                foreach ($csvRecords as $csvRecord) {
                    $directory = Directory::where('name', $csvRecord['Account Name'])->first();
                    if (!isset($directory->id) || empty($directory->id)) {
                        $image = $this->getDirectoryPlaceHolder($csvRecord['Account Name']);
                        do {
                            $uniqueId = str_pad(rand(0, ********), 8, '0', STR_PAD_LEFT);
                            $validateUniqueId = Directory::where('ref_id', $uniqueId)->first();
                        } while ($validateUniqueId && $validateUniqueId->id); // Loop until the ID is unique
                        $location = $this->generateAddress($csvRecord);
                        $geoCodeData = $this->getGeocodingData($location);
                        $contactInfo = [
                            'phone' => $csvRecord['Phone'] ?? "",
                            'email' => $uniqueId . "@muslimsbright.com",
                            'website' => $csvRecord['Website'] ?? "",
                        ];
                        $directory = [
                            'name' => $csvRecord['Account Name'],
                            'business_name' => $csvRecord['Account Name'] ?? "N/A",
                            'image' => $image,
                            'location' => $location,
                            'directory_type' => $request->directory_type,
                            'tags' => 'Masjid',
                            'status' => 1,
                            'address_type' => 'offline',
                            'address' => $geoCodeData['formatted_address'] ?? "",
                            'latitude' => $geoCodeData['latitude'] ?? 0,
                            'longtitude' => $geoCodeData['longitude'] ?? 0,
                            'social_media' => '{"facebook":null,"instagram":null,"whatsapp":null,"x":null}',
                            'contact_informations' => json_encode($contactInfo),
                            'description' => "",
                            'business_hours' => '{"monday":{"open_time":null,"close_time":null},"tuesday":{"open_time":null,"close_time":null},"wednesday":{"open_time":null,"close_time":null},"thursday":{"open_time":null,"close_time":null},"friday":{"open_time":null,"close_time":null},"saturday":{"open_time":null,"close_time":null},"sunday":{"open_time":null,"close_time":null}}',
                            "show_business_hours" => 0,
                            "gallery" => "",
                            "personal_email" => $uniqueId . "@muslimsbright.com",
                            "created_by" => 0,
                            "ref_id" => $uniqueId,
                        ];
                        Directory::create($directory);
                    }
                }
            }
            fclose($handle);
            return redirect()->route('directory.index')->withStatus(__('Successfully Imported'));
        } else {
            return back()->withErrors(['file' => 'Invalid CSV file.']);
        }
    }

    private function getGeocodingData($address)
    {
        // Replace spaces with plus sign for URL encoding
        $address = urlencode($address);

        // Google Maps Geocoding API URL
        $url = "https://maps.googleapis.com/maps/api/geocode/json?address=$address&key=AIzaSyAMhFpujOTpUfQYBIBMuzn_1viQgojw6Vg";

        // Get the response from the API
        $response = file_get_contents($url);

        // Decode the JSON response into an array
        $data = json_decode($response, true);

        // Check if the response contains the expected data
        if ($data['status'] == 'OK') {
            // Get the formatted address
            $formattedAddress = $data['results'][0]['formatted_address'];

            // Get the latitude and longitude
            $latitude = $data['results'][0]['geometry']['location']['lat'];
            $longitude = $data['results'][0]['geometry']['location']['lng'];

            return [
                'formatted_address' => $formattedAddress,
                'latitude' => $latitude,
                'longitude' => $longitude
            ];
        } else {
            // Handle error if the API doesn't return valid data
            return null;
        }
    }


    private function generateAddress($addressData)
    {
        // Extract individual components
        $street = isset($addressData['Billing Street']) ? $addressData['Billing Street'] : '';
        $city = isset($addressData['Billing City']) ? $addressData['Billing City'] : '';
        $state = isset($addressData['Billing State/Province']) ? $addressData['Billing State/Province'] : '';
        $zip = isset($addressData['Billing Zip/Postal Code']) ? $addressData['Billing Zip/Postal Code'] : '';

        // Initialize an array to hold parts of the address
        $addressParts = [];

        // Add non-empty address components to the array
        if ($street) {
            $addressParts[] = $street;
        }
        if ($city) {
            $addressParts[] = $city;
        }
        if ($state) {
            $addressParts[] = $state;
        }
        if ($zip) {
            $addressParts[] = $zip;
        }

        // Join the address parts with commas, ensuring no extra commas are included
        $formattedAddress = implode(', ', $addressParts);

        return $formattedAddress;
    }

    private function getDirectoryPlaceHolder($accountName)
    {
        // Split the string into words
        $words = explode(' ', $accountName);

        // Extract the first and last words
        $firstWord = $words[0] ?? '';
        $lastWord = end($words) ?? '';

        // Get the initials
        $initials = substr($firstWord, 0, 1) . substr($lastWord, 0, 1);
        // Open file handle
        $name = uniqid() . '.png';
        $destinationPath = public_path('/images/upload');
        $savePath = $destinationPath . "/" . $name;
        // $file = fopen($destinationPath . "/" . $name, 'wb');
        $url = 'https://placehold.co/600x400/png?text=' . strtoupper($initials) . '&font=Source%20Sans%20Pro';

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_BINARYTRANSFER, 1);
        $raw = curl_exec($ch);
        curl_close($ch);
        if (file_exists($savePath)) {
            unlink($savePath);
        }
        $fp = fopen($savePath, 'x');
        fwrite($fp, $raw);
        fclose($fp);
        return $name;
    }

    public function addCustomDirectory()
    {
        $user = \Auth::user();
        if ($user->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        if (!empty($user->org_id)) {
            return redirect('user/login');
        }
        $types = DirectoryType::where('status', 1)->get();
        $directories = Directory::where('status', 1)->get();
        $roles = Role::all();
        $showOrganizations = false;
        $customOrgName = "";
        if (empty($user->settings)) {
            $showOrganizations = true;
            $settingsArray = json_decode($user->settings, true);
            if (isset($settingsArray['custom_org_name'])) {
                $customOrgName = $settingsArray['custom_org_name'];
            }
        }
        return view('admin.organizer.add-custom-org', compact('types', 'customOrgName', 'roles', 'showOrganizations', 'directories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_create_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $types = DirectoryType::where('status', 1)->get();
        $roles = Role::all();
        return view('admin.directory.create', compact('types', 'roles'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'bail|required',
            'logo' => 'bail|required|mimes:jpeg,png,jpg,gif|max:1048',
            'images' => 'bail|required|array',
            'images.*' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            'business_name' => 'bail|required',
            'location' => 'bail|required',
            'directory_type' => 'bail|required',
            'tags' => 'bail|required',
            'status' => 'bail|required',
            'address_type' => 'bail|required',
            // 'address' => 'bail|required_if:type,offline',
            // 'latitude' => 'bail|required_if:type,offline',
            // 'longtitude' => 'bail|required_if:type,offline',
            'address' => 'bail|required_if:address_type,offline|array|min:1',
            'address.*.street' => 'bail|required_if:address_type,offline|string|max:255',
            'address.*.lat' => 'bail|required_if:address_type,offline|numeric',
            'address.*.long' => 'bail|required_if:address_type,offline|numeric',
            'business_url' => 'bail|required_if:type,online',
            'contact_informations' => 'bail|required',
            'description' => 'bail|required',
            'personal_email' => 'bail|required|email', // Corrected rule format
            // 'organizer_id' => 'bail|required',
            'join_as_organizer' => 'bail|required',
            'contact_informations.email' => 'bail|required',
            'contact_informations.website' => 'bail|required',
        ], [
            'personal_email.required' => 'The business email field is mandatory.', // Custom messages
            'personal_email.email' => 'Please provide a valid email address for business email.',
        ]);
        $user = \Auth::user();
        if ($user->hasRole('Organizer')) {
            $websiteArray = parse_url($request->contact_informations['website']);
            $emailArray = explode('@', $request->personal_email);
            $domain = "";
            if (isset($websiteArray['host']) && !empty($websiteArray['host'])) {
                $domain = str_replace('www.', '', $websiteArray['host']);
            }
            if (
                (isset($emailArray[1]) && !empty($emailArray[1]) &&
                    $emailArray[1] != $domain) || (!isset($emailArray[1]) || empty($emailArray[1]))
            ) {
                return redirect()->back()->withInput()->with('error', 'Your business email doesnt matched with your website');
            }
        }
        $data = $request->all();
        $directoryTypes = $data['directory_type'];
        unset($data['directory_type']);
        $data['gallery'] = [];
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        if ($request->hasFile('logo')) {
            $data['image'] = (new AppHelper)->saveImage($request->file('logo'), true);
        }
        // if (!empty($data['gallery'])) {
        //     $data['image'] = $data['gallery'][0];
        //     unset($data['gallery'][0]);
        // }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['security'] = 1;
        $data['business_hours'] = json_encode($request->business_hours);
        $data['address'] = json_encode($request->address);
        $data['social_media'] = json_encode($request->social_media);
        $data['contact_informations'] = json_encode($request->contact_informations);
        $data['show_business_hours'] = $request->has('show_business_hours') ? 1 : 0;
        $data['created_by'] = $user->id;
        $data['tags'] = implode(',', $request->tags);
        $directory = Directory::create($data);
        if (!empty($directoryTypes)) {
            foreach ($directoryTypes as $directoryType) {
                $newLink = DirectoryTypeLink();
                $newLink->directory_id = $directory->id;
                $newLink->type_id = $directoryType;
                $newLink->save();
            }
        }
        $message = 'Business has added successfully.';
        if ($user->hasRole('Organizer')) {
            $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                $q->whereIn("name", ["admin"]);
            })->pluck('email')->toArray();
            if (!empty($adminUsers)) {
                \Mail::to($adminUsers)->send(new CustomOrgMail('admin', $directory, $user));
            }
            \Mail::to($user->email)->send(new CustomOrgMail('organizer', $directory, $user));
            \Mail::to($request->personal_email)->send(new CustomOrgMail('organization', $directory, $user));
            $user->status = 0;
            $user->save();
            \Auth::logout();
            return redirect('user/login?type=org')->with('error_msg', 'Business has added successfully. Please wait for until review by the team.');
        }
        // User::find($request->organizer_id)->update(['org_id' => $directory->id]);
        return redirect()->route('directory.index')->withStatus(__($message));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {}

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Directory $directory)
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $selectedTypes = $directory->types->pluck('name')->toArray() ?? [];
        $types = DirectoryType::where('status', 1)->get();
        $tags = explode(',', $directory->tags) ?? [];
        $business_hours = json_decode($directory->business_hours, true);
        if (!empty($business_hours)) {
            foreach ($business_hours as $day => $business_hour) {
                $business_hours[$day]['is_closed'] = $business_hour['is_closed'] ?? "";
                $business_hours[$day]['open_time'] = $business_hour['open_time'] ?? "";
                $business_hours[$day]['close_time'] = $business_hour['close_time'] ?? "";
            }
        }
        $social_media = json_decode($directory->social_media, true);
        $contact_info = json_decode($directory->contact_informations, true);
        $roles = Role::all();
        $users = User::with("roles")->whereHas("roles", function ($q) use ($directory) {
            $q->where("id", [$directory->organizer_role]);
        })->get();
        $gallery = [];
        $galleryImages = [];
        if (!empty($directory->gallery)) {
            $galleryImages = explode(',', $directory->gallery);
        }
        if (!empty($galleryImages)) {
            foreach ($galleryImages as $key => $galleryImage) {
                array_push($gallery, ['id' => $key, 'src' => url('images/upload/' . $galleryImage)]);
            }
        }
        return view('admin.directory.edit', compact('gallery', 'types', 'users', 'directory', 'tags', 'business_hours', 'social_media', 'contact_info', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Directory $directory)
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        $this->validate($request, [
            'name' => 'bail|required',
            'business_name' => 'bail|required',
            'location' => 'bail|required',
            'directory_type' => 'bail|required|array',
            'tags' => 'bail|required',
            'status' => 'bail|required',
            // 'address_type' => 'bail|required',
            // 'address' => 'bail|required_if:address_type,offline',
            // 'latitude' => 'bail|required_if:address_type,offline',
            // 'longtitude' => 'bail|required_if:address_type,offline',
            // 'business_url' => 'bail|required_if:address_type,online',
            'address_type' => 'bail|required',
            'address' => 'bail|required_if:address_type,offline|array|min:1',
            'address.*.street' => 'bail|required_if:address_type,offline|string|max:255',
            'address.*.lat' => 'bail|required_if:address_type,offline|numeric',
            'address.*.long' => 'bail|required_if:address_type,offline|numeric',
            'contact_informations' => 'bail|required',
            'description' => 'bail|required',
            // 'organizer_role' => 'bail|required',
            // 'organizer_id' => 'bail|required',
            'personal_email' => 'bail|required|email', // Corrected rule format
            'join_as_organizer' => 'bail|required',
            'contact_informations.email' => 'bail|required',
            'contact_informations.website' => 'bail|required',
        ], [
            'personal_email.required' => 'The business email field is mandatory.', // Custom messages
            'personal_email.email' => 'Please provide a valid email address for business email.',
        ]);
        $data = $request->all();
        $directoryTypes = $data['directory_type'];
        unset($data['directory_type']);
        if ($request->hasFile('logo')) {
            (new AppHelper)->deleteFile($directory->image);
            $data['image'] = (new AppHelper)->saveImage($request->file('logo'), true);
        }
        $gallerys = [];
        $gallerys = array_merge($gallerys, (!empty($directory->gallery)) ? explode(',', $directory->gallery) : []);
        $constructedGallery = [];
        if ($request->has('preloaded')) {
            foreach ($gallerys as $key => $gallery) {
                if (in_array($key, $request->preloaded) !== false) {
                    array_push($constructedGallery, $gallery);
                } else {
                    (new AppHelper)->deleteFile($gallery);
                }
            }
        }
        $data['gallery'] = $constructedGallery;
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        // if (!empty($data['gallery'])) {
        //     $data['image'] = $data['gallery'][0];
        //     unset($data['gallery'][0]);
        // }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        // dd($data['gallery']);
        $data['address'] = json_encode($request->address);
        $data['business_hours'] = json_encode($request->business_hours);
        $data['social_media'] = json_encode($request->social_media);
        $data['contact_informations'] = json_encode($request->contact_informations);
        $data['show_business_hours'] = $request->has('show_business_hours') ? 1 : 0;
        $directory->update($data);
        if (!empty($directoryTypes)) {
            \App\Models\DirectoryTypeLink::where('directory_id', $directory->id)->delete();
            foreach ($directoryTypes as $directoryType) {
                $newLink = new \App\Models\DirectoryTypeLink();
                $newLink->directory_id = $directory->id;
                $newLink->type_id = $directoryType;
                $newLink->save();
            }
        }
        return redirect()->route('directory.index')->withStatus(__('Business has updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        Directory::find($id)->delete();
        // return redirect()->route('directory.index')->withStatus(__('Directory deleted successfully.'));
    }

    public function types()
    {
        abort_if(Gate::denies('directory_types_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $types = DirectoryType::all();
        return view('admin.directory.types.index', compact('types'));
    }

    public function createType()
    {
        abort_if(Gate::denies('directory_type_create_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        return view('admin.directory.types.create');
    }

    public function storeType(Request $request)
    {
        abort_if(Gate::denies('directory_type_create_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $this->validate($request, [
            'name' => 'required',
            'status' => 'required',
            'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
        ]);

        $data = $request->all();
        if ($request->hasFile('image')) {
            $data['image'] = (new AppHelper)->saveImage($request);
        }
        DirectoryType::create($data);
        return redirect()->route('directory-types')->withStatus(__('Directory Type has added successfully.'));
    }

    public function destroyType(DirectoryType $type)
    {
        abort_if(Gate::denies('directory_types_delete_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $type->delete();
        return true;
    }

    public function editType(DirectoryType $type)
    {
        abort_if(Gate::denies('directory_types_edit_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        return view('admin.directory.types.edit', compact('type'));
    }

    public function updateType(DirectoryType $type, Request $request)
    {
        abort_if(Gate::denies('directory_types_edit_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $this->validate($request, [
            'name' => 'required',
            'status' => 'required',
        ]);
        $data = $request->all();
        if ($request->hasFile('image')) {
            $request->validate([
                'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            ]);
            if (isset($type->image) && !empty($type->image)) {
                (new AppHelper)->deleteFile($type->image);
            }
            $data['image'] = (new AppHelper)->saveImage($request);
        }
        $type->update($data);
        return redirect()->route('directory-types')->withStatus(__('Directory Type has updated successfully.'));
    }

    public function customDirectoryApprove(Request $request)
    {
        $this->validate($request, [
            'id' => 'required'
        ]);
        $directory = Directory::find($request->id);
        if (isset($directory->created_by) && !empty($directory->created_by)) {
            $userId = $directory->created_by;
            $directory->status = 1;
            $directory->save();
            $user = User::find($userId);
            $settings['org_id'] = $directory->id;
            $settings['org_name'] = $directory->name;
            $user->settings = json_encode($settings);
            // $user->org_id = $directory->id;
            $user->organization_status = 1;
            $user->save();

            $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                $q->whereIn("name", ["admin"]);
            })->pluck('email')->toArray();
            if (!empty($adminUsers)) {
                \Mail::to($adminUsers)->send(new CustomOrgApproveMail('admin', $directory));
            }
            \Mail::to($user->email)->send(new CustomOrgApproveMail('organizer', $directory));
            if (isset($directory->personal_email) && !empty($directory->personal_email)) {
                \Mail::to($directory->personal_email)->send(new CustomOrgApproveMail('organization', $directory));
            }
        }
        echo '<h1>Approval successful. Thank you for taking action.</h1>';
    }

    public function approveOrganizers(Directory $directory, Request $request)
    {
        $this->validate($request, [
            'organizer_id' => 'required'
        ]);

        $user = User::find($request->organizer_id);
        if (isset($user->id) && $user->organization_status == '0') {
            $user->organization_status = 1;
            $user->org_id = $directory->id;
            $user->status = 1;
            $user->save();
            \Mail::to($user->email)->send(new JoinAsOrganizerSuccessMail($user, $directory));
        }
        echo '<h1>Approval successful. Thank you for taking action.</h1>';
    }
}
