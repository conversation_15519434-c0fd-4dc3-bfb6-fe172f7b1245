<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Country extends Model
{
    use HasFactory;
    protected $fillable = [
        'iso',
        'name',
        'nicename',
        'iso3',
        'numcode',
        'phonecode',
        'status',
    ];
    protected $table = 'country';

    public static function getCountryWithCountOfDirectory()
    {
        return DB::table('country')
            ->select('country.*', DB::raw('COUNT(directories.id) as directory_count'))
            ->leftJoin('directories', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(directories.where_we_work, JSON_QUOTE(CAST(country.id AS CHAR)))"), DB::raw('1'))
                    ->where('directories.status', '=', 1);
            })
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('directories')
                    ->whereRaw("JSON_CONTAINS(directories.where_we_work, JSON_QUOTE(CAST(country.id AS CHAR)))")
                    ->where('directories.status', '=', 1);
            })
            ->groupBy('country.id', 'country.name', 'country.nicename', 'country.iso', 'country.iso3', 'country.numcode', 'country.phonecode', 'country.status')
            ->orderBy('country.id', 'asc')
            ->get();
    }

    public static function getCountryWithCountOfMasajids()
    {
        return DB::table('country')
            ->select('country.*', DB::raw('COUNT(masajids.id) as directory_count'))
            ->leftJoin('masajids', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(masajids.where_we_work, JSON_QUOTE(CAST(country.id AS CHAR)))"), DB::raw('1'))
                    ->where('masajids.status', '=', 1);
            })
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('masajids')
                    ->whereRaw("JSON_CONTAINS(masajids.where_we_work, JSON_QUOTE(CAST(country.id AS CHAR)))")
                    ->where('masajids.status', '=', 1);
            })
            ->groupBy('country.id', 'country.name', 'country.nicename', 'country.iso', 'country.iso3', 'country.numcode', 'country.phonecode', 'country.status')
            ->orderBy('country.id', 'asc')
            ->get();
    }

    public static function getCountryWithCountOfSchools()
    {
        return DB::table('country')
            ->select('country.*', DB::raw('COUNT(schools.id) as schools_count'))
            ->leftJoin('schools', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(schools.where_we_work, JSON_QUOTE(CAST(country.id AS CHAR)))"), DB::raw('1'))
                    ->where('schools.status', '=', 1);
            })
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('schools')
                    ->whereRaw("JSON_CONTAINS(schools.where_we_work, JSON_QUOTE(CAST(country.id AS CHAR)))")
                    ->where('schools.status', '=', 1);
            })
            ->groupBy('country.id', 'country.name', 'country.nicename', 'country.iso', 'country.iso3', 'country.numcode', 'country.phonecode', 'country.status')
            ->orderBy('country.id', 'asc')
            ->get();
    }
}
