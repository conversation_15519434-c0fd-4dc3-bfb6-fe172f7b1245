<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Directory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'business_name',
        'image',
        'location',
        'directory_type',
        'tags',
        'status',
        'prime_status',
        'address_type',
        'address',
        'latitude',
        'longtitude',
        'business_url',
        'social_media',
        'contact_informations',
        'description',
        'business_hours',
        'show_business_hours',
        'gallery',
        'personal_email',
        'join_as_organizer',
        'created_by',
        'ref_id',
        'order',
        'related_directories',
        'chapters',
        'work_region',
        'where_we_work',
        'what_we_do',
        'donate_and_support',
        'external_link',
    ];

    protected $casts = [
        'directory_type' => 'array',
    ];

    public function typeLinks()
    {
        return $this->hasMany(\App\Models\DirectoryTypeLink::class, 'directory_id');
    }

    // If you have a types table, you can define this
    public function types()
    {
        return $this->belongsToMany(\App\Models\DirectoryType::class, 'directory_type_links', 'directory_id', 'type_id');
    }

    public static function constructAddress($directory)
    {
        if (isset($directory->address) && is_array(json_decode($directory->address, true))) {
            $address = json_decode($directory->address, true);
            foreach ($address as $key => $addr) {
                $address[$key]['url'] = url('/organizations/' . $directory->id . '/' . str_replace(' ', '-', $directory->name));
            }
        } else {
            $address[0]['street'] = $directory->address ?? "";
            $address[0]['lat'] = $directory->latitude ?? "";
            $address[0]['long'] = $directory->longtitude ?? "";
            $address[0]['title'] = $directory->title ?? "";
            $address[0]['url'] = url('/organizations/' . $directory->id . '/' . str_replace(' ', '-', $directory->name));
        }
        return $address;
    }

    public function category()
    {
        return $this->belongsTo(DirectoryType::class, 'directory_type');
    }

    public function events()
    {
        return $this->hasMany('App\Models\Event', 'user_id', 'id');
    }

    // public function organization()
    // {
    //     return $this->belongsTo(User::class, 'id', 'org_id');
    // }
    public function organizers()
    {
        return $this->hasMany(User::class, 'org_id');
    }

    public static function getBusinessUrl($contactInfo)
    {
        $contactInfoArray = json_decode($contactInfo, true);
        if (isset($contactInfoArray['website']) && !empty($contactInfoArray['website'])) {
            return $contactInfoArray['website'];
        }
        return 'N/A';
    }

    public function directoryType()
    {
        return $this->belongsTo(DirectoryType::class, 'directory_type');
    }
    /*
     * return directory lists (just name and id param)
     */
    public static function getDirectoryLists()
    {
        return Directory::where('status', 1)->pluck('name', 'id');
    }
    /*
     * return
     */
    public static function returnRelatedDirectoriesAndFormatting($directory_id)
    {
        $selectedDirectories = Directory::find($directory_id)->related_directories;
        if($selectedDirectories){
            $selectedDirectories = json_decode($selectedDirectories, true) ?? [];
        }
        return $selectedDirectories ?? [];
    }
    /*
     * return chapters
     */
    public static function returnChaptersAndFormatting($directory_id)
    {
        $selectedChapters = Directory::find($directory_id)->chapters;
        if($selectedChapters){
            $selectedChapters = json_decode($selectedChapters, true) ?? [];
        }
        return $selectedChapters ?? [];
    }
    /**
     * return similar / related directory
     */
    public static function returnSimilarOrganizationOrChapters($type, $directory_id)
    {
        if($type == 'related_directories'){
            $similarOrganizationsCollection = Directory::find($directory_id)->related_directories;
        } else {
            $similarOrganizationsCollection = Directory::find($directory_id)->chapters;
        }
        $similarOrganizations = [];
        if($similarOrganizationsCollection){
            $similarOrganizationsCollection = json_decode($similarOrganizationsCollection, true) ?? [];
            foreach($similarOrganizationsCollection as $item){
                $similarOrganizations[] = DB::table('directories')
                    ->where('id', $item)
                    ->select('id', 'name', 'image')
                    ->first();
            }
        }
        return $similarOrganizations;
    }
}
