<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Support\Facades\Log;
use App\Models\Ticket;
use App\Models\Event;
use App\Models\Form;
use App\Models\Module;
use Auth;
use Throwable;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Gate;
use Modules\Seatmap\Entities\SeatMaps;

class TicketController extends Controller
{
    public function index($id, $name)
    {
        abort_if(Gate::denies('ticket_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $event = Event::find($id);
        $ticket = Ticket::where([['event_id', $id], ['is_deleted', 0]])->orderBy('id', 'DESC')->get();
        return view('admin.ticket.index', compact('ticket', 'event'));
    }

    public function create($id)
    {
        abort_if(Gate::denies('ticket_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $event = Event::find($id);
        $settings = Setting::find(1);
        $ticketFees = $settings->ticket_fees ? json_decode($settings->ticket_fees, true) : [];
        $forms = Form::where('user_id', Auth::user()->id)
            ->OrWhere('user_id', 0)->get();
        $seatModule = Module::where('module', 'Seatmap')->first();
        if ($seatModule->is_enable == 1 && $seatModule->is_install == 1) {
            $seatMaps = SeatMaps::where('organizer_id', auth()->user()->id)->get();
            $tickets = Ticket::where('seatmap_id', '!=', Null)->get();
            foreach ($tickets as $value) {
                foreach ($seatMaps as $key => $map) {
                    if ($map->id == $value->seatmap_id) {
                        unset($seatMaps[$key]);
                    }
                }
            }
            return view('admin.ticket.create', compact('event', 'seatModule', 'seatMaps', 'forms', 'ticketFees'));
        }
        $start_time = date('m/d/Y');
        $end_time = date('m/d/Y h:i A', strtotime($event->end_time));
        // dd($end_time);
        return view('admin.ticket.create', compact('event', 'seatModule', 'forms', 'ticketFees', 'start_time', 'end_time'));
    }


    public function store(Request $request)
    {
        $request->validate([
            'type' => 'bail|required',
            'name' => 'bail|required',
            'form_id' => 'bail|required',
            'start_time' => 'bail|required',
            'end_time' => 'bail|required',
        ]);
        if ($request->type == 'donation') {
            $request->validate([
                'donation_amount_type' => 'bail|required',
                'donation_amount' => 'bail|required_if:amount_type,Fixed',
            ]);
        } else {
            $request->validate([
                'price' => 'bail|required_if:type,paid',
                'quantity' => 'bail|required',
                'ticket_per_order' => 'bail|required',
            ]);
        }
        $data = $request->all();

        $event = Event::find($request->event_id);
        $data['ticket_number'] = chr(rand(65, 90)) . chr(rand(65, 90)) . '-' . rand(999, 10000);
        if ($request->type != 'donation') {
            $currentTotal = ($event->total_qty + $request->quantity);
            if ($currentTotal > $event->people) {
                return redirect()->back()->withErrors([
                    'error' => 'Please note that you only have ' . $event->people . ' people attending, so you cannot sell ' . $currentTotal . ' tickets.'
                ]);
            }
        } else {
            dd($request->all());
            $data['donation_amount'] = array_map(function ($item) {
                return preg_replace('/[^\d.]/', '', $item);
            }, $request->donation_amount);
            $data['quantity'] = 0;
            $data['ticket_per_order'] = 0;
            $data['price'] = 0;
        }

        if ($request->type == "free") {
            $data['price'] = 0;
        }
        $user = \Auth::user();
        if ($user->hasRole('Organizer')) {
            $settings = Setting::find(1);
            $ticketFees = $settings->ticket_fees ? json_decode($settings->ticket_fees, true) : [];
            $data['fee_type'] = $ticketFees['fee_1_type'] ?? "$";
            $data['fee'] = $ticketFees['fee_1'] ?? "0";
            $data['fee_2_type'] = $ticketFees['fee_2_type'] ?? "$";
            $data['fee_2'] = $ticketFees['fee_2'] ?? "0";
            $data['insurance'] = $ticketFees['need_insurance'] ?? "0";
            $data['insurance_amount_type'] = $ticketFees['insurance_type'] ?? "$";
            $data['insurance_amount'] = $ticketFees['insurance'] ?? "0";
        } else {
            if ($data['insurance'] == '1') {
                $request->validate([
                    'insurance_amount_type' => 'bail|required',
                    'insurance_amount' => 'bail|required',
                ]);
            } else {
                $data['insurance_amount_type'] = '$';
                $data['insurance_amount'] = '0';
            }
        }
        $data['donation_amount'] = (isset($data['donation_amount']))?json_encode($data['donation_amount']):json_encode([]);
        $data['user_id'] = $event->user_id;
        Ticket::create($data);
        // $totalTickets = Ticket::where('event_id', $event->id)->count();
        // if ($totalTickets == 1 && $event->status == '0') {
        //     $event->status = 1;
        //     $event->save();
        // } 
        $event->status = $request->publish_event;
        $event->save();

        return redirect($request->event_id . '/' . preg_replace('/\s+/', '-', $event->name) . '/tickets')->withStatus(__('Ticket has added successfully.'));
    }

    public function show(Ticket $ticket)
    {
    }

    public function edit($id)
    {
        abort_if(Gate::denies('ticket_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $ticket = Ticket::find($id); 
        $event = Event::find($ticket->event_id);
        $forms = Form::where('user_id', Auth::user()->id)->OrWhere('user_id', 0)->get();
        $donationAmount = ($ticket->type == 'donation') ? json_decode($ticket->donation_amount, true) : [];
        return view('admin.ticket.edit', compact('ticket', 'event', 'forms', 'donationAmount'));
    }

    public function update(Request $request, $id)
    {

        $request->validate([
            'type' => 'bail|required',
            'name' => 'bail|required',
            'form_id' => 'bail|required',
            'start_time' => 'bail|required',
            'end_time' => 'bail|required',
        ]);
        if ($request->type == 'donation') {
            $request->validate([
                'donation_amount_type' => 'bail|required',
                'donation_amount' => 'bail|required_if:amount_type,Fixed',
            ]);
        } else {
            $request->validate([
                'price' => 'bail|required_if:type,paid',
                'quantity' => 'bail|required',
                'ticket_per_order' => 'bail|required',
            ]);
        }
        $data = $request->all();

        $user = \Auth::user();
        $ticket = Ticket::find($id);
        $event = Event::find($request->event_id);
        if ($request->type != 'donation') {
            $currentTotal = ((($event->total_qty - $ticket->quantity) + $request->quantity));
            if ($currentTotal > $event->people) {
                return redirect()->back()->withErrors([
                    'error' => 'Please note that you only have ' . $event->people . ' people attending, so you cannot sell ' . $currentTotal . ' tickets.'
                ]);
            }
        } else {
            $data['donation_amount'] = array_map(function ($item) {
                return preg_replace('/[^\d.]/', '', $item);
            }, $request->donation_amount);
        }
        if ($user->hasRole('Organizer')) {
            $data['fee_type'] = $ticket->fee_type;
            $data['fee'] = $ticket->fee;
            $data['fee_2_type'] = $ticket->fee_2_type;
            $data['fee_2'] = $ticket->fee_2;
            $data['insurance'] = $ticket->insurance;
            $data['insurance_amount_type'] = $ticket->insurance_amount_type;
            $data['insurance_amount'] = $ticket->insurance_amount;
        } else {
            if ($data['insurance'] == '1') {
                $request->validate([
                    'insurance_amount_type' => 'bail|required',
                    'insurance_amount' => 'bail|required',
                ]);
            } else {
                $data['insurance_amount_type'] = '$';
                $data['insurance_amount'] = '0';
            }
        }
        if ($request->type == "free") {
            $data['price'] = 0;
        }
        $ticket->update($data);
        $event->status = $request->publish_event;
        $event->save();
        // else {
        // $data['donation_amount'] = array_map(function ($item) {
        //     return preg_replace('/[^\d.]/', '', $item);
        // }, $request->donation_amount);
        //     $ticket->update($data);
        // }
        return redirect($request->event_id . '/' . preg_replace('/\s+/', '-', $event->name) . '/tickets')->withStatus(__('Ticket has updated successfully.'));
    }

    public function destroy(Ticket $ticket)
    {
    }

    public function deleteTickets($id)
    {
        try {
            $ticket = Ticket::find($id)->update(['is_deleted' => 1]);
            return true;
        } catch (Throwable $th) {
            return response('Data is Connected with other Data', 400);
        }
    }
}
