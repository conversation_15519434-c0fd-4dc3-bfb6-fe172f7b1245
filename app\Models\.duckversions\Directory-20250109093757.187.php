<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Directory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'business_name',
        'image',
        'location',
        'directory_type',
        'tags',
        'status',
        'address_type',
        'address',
        'latitude',
        'longtitude',
        'business_url',
        'social_media',
        'contact_informations',
        'description',
        'business_hours',
        'show_business_hours',
        'gallery',
        'personal_email',
        'join_as_organizer',
        'created_by',
        'ref_id',
    ];

    public static function constructAddress($directory)
    {
        if (isset($directory->address) && is_array(json_decode($directory->address, true))) {
        } else {
            $address['street'] = $directory->address ?? "";
        }
    }

    public function category()
    {
        return $this->belongsTo(DirectoryType::class, 'directory_type');
    }

    // public function organization()
    // {
    //     return $this->belongsTo(Directory::class, 'org_id');
    // }
    public function organizers()
    {
        return $this->hasMany(User::class, 'org_id');
    }

    public static function getBusinessUrl($contactInfo)
    {
        $contactInfoArray = json_decode($contactInfo, true);
        if (isset($contactInfoArray['website']) && !empty($contactInfoArray['website'])) {
            return $contactInfoArray['website'];
        }
        return 'N/A';
    }
}
