<?php

namespace App\Http\Controllers;

use App\Mail\CustomOrgApproveMail;
use App\Mail\CustomOrgMail;
use App\Mail\JoinAsOrganizerSuccessMail;
use App\Models\Country;
use App\Models\DirectoryType;
use App\Models\Directory;
use App\Models\DirectoryTypeLink;
use App\Models\MasajidsPrograms;
use App\Models\Programs;
use App\Models\TagsModel;
use App\Models\USAStates;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpFoundation\Response;
use Spatie\Permission\Models\Role;
use \App\Models\User;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class DirectoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $types = DirectoryType::where('status', 1)->pluck('name', 'id')->toArray();
        $directories = DB::table('directories')
            ->leftJoin('directory_types', function ($join) {
                $join->on('directory_types.id', '=', DB::raw("CAST(JSON_UNQUOTE(JSON_EXTRACT(directories.directory_type, '$[0]')) AS UNSIGNED)"));
            })
            ->leftJoin('users', 'users.org_id', '=', 'directories.id') // Keep left join to include directories without users
            ->select(
                'directories.*',
                'directory_types.order as directory_type_order',
                DB::raw('COUNT(users.id) as user_count') // Count users, even if 0
            )
            ->groupBy('directories.id', 'directory_types.order', 'directories.name') // Ensure groupBy includes unique columns
            ->orderBy('directory_types.order', 'asc')
            ->orderBy('directories.order', 'asc')
            ->get()
            ->map(function ($directory) use ($types) {
                $directoryTypes = json_decode($directory->directory_type, true);
                if (!is_array($directoryTypes)) {
                    $directoryTypes = [];
                }
                $directory->directory_type_names = array_map(fn($id) => $types[$id] ?? $id, $directoryTypes);
                return $directory;
            });

        $i = 1;
        return view('admin.directory.index', compact('directories', 'i', 'types'));
    }

    public function orderDirectoryTypeProcess(Request $request)
    {
        $this->validate($request, [
            'positions' => 'required|array',
        ]);
        $positions = $request->positions;
        foreach ($positions as $position) {
            if (isset($position['id']) && isset($position['position'])) {
                DirectoryType::where('id', $position['id'])->update(['order' => $position['position']]);
            }
        }
    }

    public function orderDirectoryProcess(Request $request)
    {
        $this->validate($request, [
            'positions' => 'required|array',
        ]);
        $positions = $request->positions;
        foreach ($positions as $position) {
            if (isset($position['id']) && isset($position['position'])) {
                Directory::where('id', $position['id'])->update(['order' => $position['position']]);
            }
        }
    }


    public function importDirectories(Request $request)
    {
        set_time_limit(0);
        $this->validate($request, [
            'directory_type' => 'required',
            'directory_file' => 'required|file|mimes:csv,txt|max:2048', // Validate as CSV file
        ]);
        $file = $request->file('directory_file');
        if (($handle = fopen($file->getPathname(), 'r')) !== false) {
            $headers = fgetcsv($handle);
            $csvRecords = [];
            while (($row = fgetcsv($handle)) !== false) {
                $csvRecords[] = array_combine($headers, $row); // Combine headers with row values
            }
            // echo '<pre>';
            // print_r($csvRecords);
            // die;
            if (!empty($csvRecords)) {
                foreach ($csvRecords as $i => $csvRecord) {
                    if (isset($csvRecord['Account Name'])) {
                        $directory = Directory::where('name', $csvRecord['Account Name'])->get();
                        if (!isset($directory[0]->id) || empty($directory[0]->id)) {
                            echo 'i am coming here';
                            die;
                            continue;
                            // echo $i . ". " . $csvRecord['Account Name'] . "<br>";
                            $image = $this->getDirectoryPlaceHolder($csvRecord['Account Name']);
                            do {
                                $uniqueId = str_pad(rand(0, ********), 8, '0', STR_PAD_LEFT);
                                $validateUniqueId = Directory::where('ref_id', $uniqueId)->first();
                            } while ($validateUniqueId && $validateUniqueId->id); // Loop until the ID is unique
                            $location = $this->generateAddress($csvRecord);
                            $geoCodeData = $this->getGeocodingData($location);
                            $contactInfo = [
                                'phone' => $csvRecord['Phone'] ?? "",
                                'email' => $uniqueId . "@muslimsbright.com",
                                'website' => $csvRecord['Website'] ?? "",
                            ];
                            $directory = [
                                'name' => $csvRecord['Account Name'],
                                'business_name' => $csvRecord['Account Name'] ?? "N/A",
                                'image' => $image,
                                'location' => $location,
                                'directory_type' => $request->directory_type,
                                'tags' => 'Masjid',
                                'status' => 1,
                                'address_type' => 'offline',
                                // 'address' => $geoCodeData['formatted_address'] ?? "",
                                // 'latitude' => $geoCodeData['latitude'] ?? 0,
                                // 'longtitude' => $geoCodeData['longitude'] ?? 0,
                                'address' => json_encode([
                                    [
                                        'title' => 'Address',
                                        'street' => $geoCodeData['formatted_address'] ?? "",
                                        'lat' => $geoCodeData['latitude'] ?? 0,
                                        'long' => $geoCodeData['longitude'] ?? 0,
                                        'state' => isset($csvRecord['Billing State/Province']) ? $csvRecord['Billing State/Province'] : '',
                                    ]
                                ]),
                                'social_media' => '{"facebook":null,"instagram":null,"whatsapp":null,"x":null}',
                                'contact_informations' => json_encode($contactInfo),
                                'description' => "",
                                'business_hours' => '{"monday":{"open_time":null,"close_time":null},"tuesday":{"open_time":null,"close_time":null},"wednesday":{"open_time":null,"close_time":null},"thursday":{"open_time":null,"close_time":null},"friday":{"open_time":null,"close_time":null},"saturday":{"open_time":null,"close_time":null},"sunday":{"open_time":null,"close_time":null}}',
                                "show_business_hours" => 0,
                                "gallery" => "",
                                "personal_email" => $uniqueId . "@muslimsbright.com",
                                "created_by" => 0,
                                "ref_id" => $uniqueId,
                            ];
                            $dir = Directory::create($directory);
                            $newLink = new \App\Models\DirectoryTypeLink();
                            $newLink->directory_id = $dir->id;
                            $newLink->type_id = $request->directory_type;
                            $newLink->save();
                        } else {
                            // foreach ($directory as $dir) {
                            //     // $dir->delete();
                            //     if (!is_array(json_decode($dir->address, true))) {
                            //         echo $i . ". " . $csvRecord['Account Name'] . "<br>";
                            //         // die;
                            //         $location = $this->generateAddress($csvRecord);
                            //         $geoCodeData = $this->getGeocodingData($location);
                            //         // echo '<pre>';
                            //         // print_r($location);
                            //         // print_r($geoCodeData);
                            //         // die;
                            //         $dir->address = json_encode([
                            //             [
                            //                 'title' => 'Address',
                            //                 'street' => $geoCodeData['formatted_address'] ?? "",
                            //                 'lat' => $geoCodeData['latitude'] ?? 0,
                            //                 'long' => $geoCodeData['longitude'] ?? 0,
                            //                 'state' => $geoCodeData['state'] ?? "N/A",
                            //             ]
                            //         ]);
                            //         $dir->save();
                            //         \App\Models\DirectoryTypeLink::where('directory_id', $dir->id)->delete();
                            //         $newLink = new \App\Models\DirectoryTypeLink();
                            //         $newLink->directory_id = $dir->id;
                            //         $newLink->type_id = $request->directory_type;
                            //         $newLink->save();
                            //         // die;
                            //     }
                            // }
                        }
                    }
                }
            }
            fclose($handle);
            return redirect()->route('directory.index')->withStatus(__('Successfully Imported'));
        } else {
            return back()->withErrors(['file' => 'Invalid CSV file.']);
        }
    }

    private function getGeocodingData($address)
    {
        if (!empty($address)) {
            // Replace spaces with plus sign for URL encoding
            $address = urlencode($address);

            // Google Maps Geocoding API URL
            $url = "https://maps.googleapis.com/maps/api/geocode/json?address=$address&key=AIzaSyAMhFpujOTpUfQYBIBMuzn_1viQgojw6Vg";

            // Get the response from the API
            $response = file_get_contents($url);

            // Decode the JSON response into an array
            $data = json_decode($response, true);

            // Check if the response contains the expected data
            if ($data['status'] == 'OK') {
                // Get the formatted address
                $formattedAddress = $data['results'][0]['formatted_address'];

                // Get the latitude and longitude
                $latitude = $data['results'][0]['geometry']['location']['lat'];
                $longitude = $data['results'][0]['geometry']['location']['lng'];

                $state = null;

                // Loop through address components to find the state
                foreach ($data['results'][0]['address_components'] as $component) {
                    if (in_array("administrative_area_level_1", $component['types'])) {
                        $state = $component['long_name']; // Get the full state name
                        break;
                    }
                }

                return [
                    'formatted_address' => $formattedAddress,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'state' => $state
                ];
            } else {
                // Handle error if the API doesn't return valid data
                return null;
            }
        }
        return "";
    }


    private function generateAddress($addressData)
    {
        // Extract individual components
        $street = isset($addressData['Billing Street']) ? $addressData['Billing Street'] : '';
        $city = isset($addressData['Billing City']) ? $addressData['Billing City'] : '';
        $state = isset($addressData['Billing State/Province']) ? $addressData['Billing State/Province'] : '';
        $zip = isset($addressData['Billing Zip/Postal Code']) ? $addressData['Billing Zip/Postal Code'] : '';

        // Initialize an array to hold parts of the address
        $addressParts = [];

        // Add non-empty address components to the array
        if ($street) {
            $addressParts[] = $street;
        }
        if ($city) {
            $addressParts[] = $city;
        }
        if ($state) {
            $addressParts[] = $state;
        }
        if ($zip) {
            $addressParts[] = $zip;
        }

        // Join the address parts with commas, ensuring no extra commas are included
        $formattedAddress = implode(', ', $addressParts);

        return $formattedAddress;
    }

    private function getDirectoryPlaceHolder($accountName)
    {
        // Split the string into words
        $words = explode(' ', $accountName);

        // Extract the first and last words
        $firstWord = $words[0] ?? '';
        $lastWord = end($words) ?? '';

        // Get the initials
        $initials = substr($firstWord, 0, 1) . substr($lastWord, 0, 1);
        // Open file handle
        $name = uniqid() . '.png';
        $destinationPath = public_path('/images/upload');
        $savePath = $destinationPath . "/" . $name;
        // $file = fopen($destinationPath . "/" . $name, 'wb');
        $url = 'https://placehold.co/600x400/png?text=' . strtoupper($initials) . '&font=Source%20Sans%20Pro';

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_BINARYTRANSFER, 1);
        $raw = curl_exec($ch);
        curl_close($ch);
        if (file_exists($savePath)) {
            unlink($savePath);
        }
        $fp = fopen($savePath, 'x');
        fwrite($fp, $raw);
        fclose($fp);
        return $name;
    }

    public function addCustomDirectory()
    {
        $user = \Auth::user();
        if ($user->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        if (!empty($user->org_id)) {
            return redirect('user/login');
        }
        $types = DirectoryType::where('status', 1)->get();
        $directories = Directory::where('status', 1)->get();
        $roles = Role::all();
        $showOrganizations = false;
        $customOrgName = "";
        if (empty($user->settings)) {
            $showOrganizations = true;
            $settingsArray = json_decode($user->settings, true);
            if (isset($settingsArray['custom_org_name'])) {
                $customOrgName = $settingsArray['custom_org_name'];
            }
        }
        return view('admin.organizer.add-custom-org', compact('types', 'customOrgName', 'roles', 'showOrganizations', 'directories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_create_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $types = DirectoryType::where('status', 1)->get();
        $roles = Role::all();
        $tags = TagsModel::all();
        $whereWeWork = Country::all();
        $whatWeDo = Programs::all();
        $usaStates = USAStates::all();
        $directoryLists = Directory::getDirectoryLists();

        return view('admin.directory.create', compact('types', 'roles', 'directoryLists', 'tags', 'whereWeWork', 'whatWeDo', 'usaStates'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'bail|required',
            // 'logo' => 'bail|required|mimes:jpeg,png,jpg,gif|max:1048',
            'images' => 'bail|required|array',
            'images.*' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            'business_name' => 'bail|required',
            'location' => 'bail|required',
            'directory_type' => 'bail|required|array',
            'tags' => 'bail|required',
            'status' => 'bail|required',
            'prime_status' => 'bail|required',
            'address_type' => 'bail|required',
            // 'address' => 'bail|required_if:type,offline',
            // 'latitude' => 'bail|required_if:type,offline',
            // 'longtitude' => 'bail|required_if:type,offline',

            //'address' => 'bail|required_if:address_type,offline|array|min:1',
            'address' => 'required|array|min:1',
            'address.*.street' => 'bail|required_if:address_type,offline|string|max:255',
            'address.*.lat' => 'bail|required_if:address_type,offline|numeric',
            'address.*.long' => 'bail|required_if:address_type,offline|numeric',
            'business_url' => 'bail|required_if:type,onli   ne',
            'contact_informations' => 'bail|required',
            'description' => 'bail|required',
            'personal_email' => 'bail|required|email', // Corrected rule format
            // 'organizer_id' => 'bail|required',
            'join_as_organizer' => 'bail|required',
            'contact_informations.email' => 'bail|required',
            'contact_informations.website' => 'bail|required',
        ], [
            //'address.required' => 'Address required',
            'personal_email.required' => 'The business email field is mandatory.', // Custom messages
            'personal_email.email' => 'Please provide a valid email address for business email.',
        ]);
        $user = \Auth::user();
        if ($user->hasRole('Organizer')) {
            $websiteArray = parse_url($request->contact_informations['website']);
            $emailArray = explode('@', $request->personal_email);
            $domain = "";
            if (isset($websiteArray['host']) && !empty($websiteArray['host'])) {
                $domain = str_replace('www.', '', $websiteArray['host']);
            }
            if (
                (isset($emailArray[1]) && !empty($emailArray[1]) &&
                    $emailArray[1] != $domain) || (!isset($emailArray[1]) || empty($emailArray[1]))
            ) {
                return redirect()->back()->withInput()->with('error', 'Your business email doesnt matched with your website');
            }
        }
        $data = $request->all();
        $directoryTypes = $data['directory_type'];
        $data['directory_type'] = $request->has('directory_type') ? array_map('intval', $request->directory_type) : [0];
        if ($request->relatedDirectoryLists) {
            $data['related_directories'] = json_encode($request->relatedDirectoryLists, true);
        }
        if ($request->chaptersLists) {
            $data['chapters'] = json_encode($request->chaptersLists, true);
        }
        if ($request->where_we_work) {
            $data['where_we_work'] = json_encode($request->where_we_work, true);
        }
        if ($request->what_we_do) {
            $data['what_we_do'] = json_encode($request->what_we_do, true);
        }
        if ($request->tags) {
            $data['tags'] = json_encode($request->tags, true);
        }
        if ($request->has('prime_status')) {
            $data['prime_status'] = $request->input('prime_status');
        }
        if ($request->has('donate_and_support')) {
            $data['donate_and_support'] = $request->input('donate_and_support');
        }
        if ($request->has('external_link')) {
            $data['external_link'] = $request->input('external_link');
        }
        $data['gallery'] = [];
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        if ($request->hasFile('logo')) {
            $data['image'] = (new AppHelper)->saveImage($request->file('logo'), true);
        } else {
            $data['image'] = $this->getDirectoryPlaceHolder($request->name);
        }
        // if (!empty($data['gallery'])) {
        //     $data['image'] = $data['gallery'][0];
        //     unset($data['gallery'][0]);
        // }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['security'] = 1;
        $data['business_hours'] = json_encode($request->business_hours);
        $data['address'] = json_encode($request->address);
        $data['social_media'] = json_encode($request->social_media);
        $data['contact_informations'] = json_encode($request->contact_informations);
        $data['show_business_hours'] = $request->has('show_business_hours') ? 1 : 0;
        $data['created_by'] = $user->id;
        $data['work_region'] = $request->work_region;
        $directory = Directory::create($data);
        if (!empty($directoryTypes)) {
            foreach ($directoryTypes as $directoryType) {
                $newLink = new \App\Models\DirectoryTypeLink();
                $newLink->directory_id = $directory->id;
                $newLink->type_id = $directoryType;
                $newLink->save();
            }
        }
        $message = 'Business has added successfully.';
        if ($user->hasRole('Organizer')) {
            $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                $q->whereIn("name", ["admin"]);
            })->pluck('email')->toArray();
            if (!empty($adminUsers)) {
                \Mail::to($adminUsers)->send(new CustomOrgMail('admin', $directory, $user));
            }
            \Mail::to($user->email)->send(new CustomOrgMail('organizer', $directory, $user));
            \Mail::to($request->personal_email)->send(new CustomOrgMail('organization', $directory, $user));
            $user->status = 0;
            $user->save();
            \Auth::logout();
            return redirect('user/login?type=org')->with('error_msg', 'Business has added successfully. Please wait for until review by the team.');
        }
        // User::find($request->organizer_id)->update(['org_id' => $directory->id]);
        return redirect()->route('directory.index')->withStatus(__($message));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {}

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Directory $directory)
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        /*
         * directory types
         */
        if ($directory->directory_type && is_array($directory->directory_type) == true) {
            $selectedTypes = $directory->directory_type;
        } elseif (is_string($directory->directory_type)) {
            $selectedTypes = [intval($directory->directory_type)];
        } else {
            $selectedTypes = $directory->directory_type ? json_decode($directory->directory_type, true) : [];
        }
        $types = DirectoryType::where('status', 1)->get();

        $tags = TagsModel::all();
        $selectedTags = json_decode($directory->tags, true);
        $selectedTags = is_array($selectedTags)  ? $selectedTags : [];

        $usaStates = USAStates::all();
        $whereWeWork = Country::all();
        $selectedCountries = json_decode($directory->where_we_work, true);
        $selectedCountries = is_array($selectedCountries) ? $selectedCountries : [];

        $workRegion = $directory->work_region ?? 'worldwide';

        $whatWeDo = Programs::all();
        $selectedPrograms = json_decode($directory->what_we_do, true);
        $selectedPrograms = is_array($selectedPrograms) ? $selectedPrograms : [];

        $business_hours = json_decode($directory->business_hours, true);
        if (!empty($business_hours)) {
            foreach ($business_hours as $day => $business_hour) {
                $business_hours[$day]['is_closed'] = $business_hour['is_closed'] ?? "";
                $business_hours[$day]['open_time'] = $business_hour['open_time'] ?? "";
                $business_hours[$day]['close_time'] = $business_hour['close_time'] ?? "";
            }
        }
        $social_media = json_decode($directory->social_media, true);
        // Ensure the keys exist
        $required_keys = ['youtube', 'linkedin', 'tiktok'];
        foreach ($required_keys as $key) {
            if (!array_key_exists($key, $social_media)) {
                $social_media[$key] = null;
            }
        }

        $contact_info = json_decode($directory->contact_informations, true);

        $roles = Role::all();
        $users = User::with("roles")->whereHas("roles", function ($q) use ($directory) {
            $q->where("id", [$directory->organizer_role]);
        })->get();

        $gallery = [];
        $galleryImages = [];
        if (!empty($directory->gallery)) {
            $galleryImages = explode(',', $directory->gallery);
        }
        if (!empty($galleryImages)) {
            foreach ($galleryImages as $key => $galleryImage) {
                array_push($gallery, ['id' => $key, 'src' => url('images/upload/' . $galleryImage)]);
            }
        }
        $directoryLists = Directory::getDirectoryLists();
        $selectedDirectories = Directory::returnRelatedDirectoriesAndFormatting($directory->id);
        $selectedChapters = Directory::returnChaptersAndFormatting($directory->id);
        /*
        $selectedDirectories = Directory::find($directory->id)->related_directories;
        if($selectedDirectories){
            $selectedDirectories = json_decode($selectedDirectories, true) ?? [];
        } else {
            $selectedDirectories = [];
        }
        */
        return view('admin.directory.edit', compact('gallery', 'selectedTypes', 'types', 'users', 'directory', 'business_hours', 'social_media', 'contact_info', 'roles', 'tags', 'selectedTags', 'directoryLists', 'selectedDirectories', 'selectedChapters', 'usaStates', 'whereWeWork', 'selectedCountries', 'workRegion', 'whatWeDo', 'selectedPrograms'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Directory $directory)
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        $this->validate($request, [
            'name' => 'bail|required',
            'business_name' => 'bail|required',
            'location' => 'bail|required',
            'directory_type' => 'bail|required|array',
            'status' => 'bail|required',
            'prime_status' => 'bail|required',
            'address_type' => 'bail|required',
            'address' => 'bail|required_if:address_type,offline|array|min:1',
            'address.*.street' => 'bail|required_if:address_type,offline|string|max:255',
            'address.*.lat' => 'bail|required_if:address_type,offline|numeric',
            'address.*.long' => 'bail|required_if:address_type,offline|numeric',
            'contact_informations' => 'bail|required',
            'description' => 'bail|required',
            'personal_email' => 'bail|required|email', // Corrected rule format
            'join_as_organizer' => 'bail|required',
            'contact_informations.email' => 'bail|required',
            'contact_informations.website' => 'bail|required',
        ], [
            'personal_email.required' => 'The business email field is mandatory.', // Custom messages
            'personal_email.email' => 'Please provide a valid email address for business email.',
        ]);
        $data = $request->all();

        if ($request->relatedDirectoryLists) {
            $data['related_directories'] = json_encode($request->relatedDirectoryLists);
        }
        if ($request->chaptersLists) {
            $data['chapters'] = json_encode($request->chaptersLists);
        }

        $data['work_region'] = $request->work_region;
        if ($request->where_we_work) {
            $data['where_we_work'] = json_encode(array_values($request->where_we_work));
        } else {
            $data['where_we_work'] = json_encode([]);
        }

        if ($request->what_we_do) {
            $data['what_we_do'] = json_encode(array_values($request->what_we_do));
        } else {
            $data['what_we_do'] = json_encode([]);
        }
        if ($request->tags) {
            $data['tags'] = json_encode(array_values($request->tags));
        } else {
            $data['tags'] = json_encode([]);
        }
        if ($request->has('prime_status')) {
            $data['prime_status'] = $request->input('prime_status');
        }
        if ($request->has('donate_and_support')) {
            $data['donate_and_support'] = $request->input('donate_and_support');
        }
        if ($request->has('external_link')) {
            $data['external_link'] = $request->input('external_link');
        }
        $directoryTypes = $data['directory_type'];
        if ($request->hasFile('logo')) {
            (new AppHelper)->deleteFile($directory->image);
            $data['image'] = (new AppHelper)->saveImage($request->file('logo'), true);
        }
        $gallerys = [];
        $gallerys = array_merge($gallerys, (!empty($directory->gallery)) ? explode(',', $directory->gallery) : []);
        $constructedGallery = [];
        if ($request->has('preloaded')) {
            foreach ($gallerys as $key => $gallery) {
                if (in_array($key, $request->preloaded) !== false) {
                    array_push($constructedGallery, $gallery);
                } else {
                    (new AppHelper)->deleteFile($gallery);
                }
            }
        }
        $data['gallery'] = $constructedGallery;
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['address'] = json_encode($request->address);
        $data['business_hours'] = json_encode($request->business_hours);
        $data['social_media'] = json_encode($request->social_media);
        $data['contact_informations'] = json_encode($request->contact_informations);
        $data['show_business_hours'] = $request->has('show_business_hours') ? 1 : 0;
        $data['directory_type'] = $request->has('directory_type') ? array_map('intval', $request->directory_type) : [0];

        $directory->update($data);
        if (!empty($directoryTypes)) {
            \App\Models\DirectoryTypeLink::where('directory_id', $directory->id)->delete();
            foreach ($directoryTypes as $directoryType) {
                $newLink = new \App\Models\DirectoryTypeLink();
                $newLink->directory_id = $directory->id;
                $newLink->type_id = $directoryType;
                $newLink->save();
            }
        }
        return redirect()->route('directory.index')->withStatus(__('Business has updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        Directory::find($id)->delete();
        \App\Models\DirectoryTypeLink::where('directory_id', $id)->delete();
        return true;
        // return redirect()->route('directory.index')->withStatus(__('Directory deleted successfully.'));
    }

    public function types()
    {
        abort_if(Gate::denies('directory_types_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        //$types = DirectoryType::orderBy('order', 'asc')->get();
        $types = DirectoryType::getDirectoryTypeWithCountOfDirectory();
        //dd($types);
        return view('admin.directory.types.index', compact('types'));
    }

    public function createType()
    {
        abort_if(Gate::denies('directory_type_create_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        return view('admin.directory.types.create');
    }

    public function storeType(Request $request)
    {
        abort_if(Gate::denies('directory_type_create_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $this->validate($request, [
            'name' => 'required',
            'status' => 'required',
            'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            'pins_color' => 'required',
        ]);

        $data = $request->all();
        if ($request->hasFile('image')) {
            $data['image'] = (new AppHelper)->saveImage($request);
        }
        DirectoryType::create($data);
        return redirect()->route('directory-types')->withStatus(__('Directory Type has added successfully.'));
    }

    public function destroyType(DirectoryType $type)
    {
        abort_if(Gate::denies('directory_types_delete_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $type->delete();
        return true;
    }

    public function editType(DirectoryType $type)
    {
        abort_if(Gate::denies('directory_types_edit_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        return view('admin.directory.types.edit', compact('type'));
    }

    public function updateType(DirectoryType $type, Request $request)
    {
        abort_if(Gate::denies('directory_types_edit_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $this->validate($request, [
            'name' => 'required',
            'status' => 'required',
            'pins_color' => 'required',
        ]);
        $data = $request->all();
        if ($request->hasFile('image')) {
            $request->validate([
                'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            ]);
            if (isset($type->image) && !empty($type->image)) {
                (new AppHelper)->deleteFile($type->image);
            }
            $data['image'] = (new AppHelper)->saveImage($request);
        }
        $type->update($data);
        return redirect()->route('directory-types')->withStatus(__('Directory Type has updated successfully.'));
    }

    public function customDirectoryApprove(Request $request)
    {
        $this->validate($request, [
            'id' => 'required'
        ]);
        $directory = Directory::find($request->id);
        if (isset($directory->created_by) && !empty($directory->created_by)) {
            $userId = $directory->created_by;
            $directory->status = 1;
            $directory->save();
            $user = User::find($userId);
            $settings['org_id'] = $directory->id;
            $settings['org_name'] = $directory->name;
            $user->settings = json_encode($settings);
            // $user->org_id = $directory->id;
            $user->organization_status = 1;
            $user->save();

            $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                $q->whereIn("name", ["admin"]);
            })->pluck('email')->toArray();
            if (!empty($adminUsers)) {
                \Mail::to($adminUsers)->send(new CustomOrgApproveMail('admin', $directory));
            }
            \Mail::to($user->email)->send(new CustomOrgApproveMail('organizer', $directory));
            if (isset($directory->personal_email) && !empty($directory->personal_email)) {
                \Mail::to($directory->personal_email)->send(new CustomOrgApproveMail('organization', $directory));
            }
        }
        echo '<h1>Approval successful. Thank you for taking action.</h1>';
    }

    public function approveOrganizers(Directory $directory, Request $request)
    {
        $this->validate($request, [
            'organizer_id' => 'required'
        ]);

        $user = User::find($request->organizer_id);
        if (isset($user->id) && $user->organization_status == '0') {
            $user->organization_status = 1;
            $user->org_id = $directory->id;
            $user->status = 1;
            $user->save();
            \Mail::to($user->email)->send(new JoinAsOrganizerSuccessMail($user, $directory));
        }
        echo '<h1>Approval successful. Thank you for taking action.</h1>';
    }

    // ******Show-Directory-Tags******
    public function showTags()
    {
        try {
            $allTags = TagsModel::all();
            return view('admin.directory.tags.index', compact('allTags'));
        } catch (Exception $e) {
            Log::error('Error fetching tags: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load tags. Please try again later.');
        }
    }
    // ******Show-Directory-Tags******

    // ******Create-Directory-Tags******
    public function createTags()
    {
        try {
            return view('admin.directory.tags.add');
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load tags. Please try again later.');
        }
    }
    // ******Create-Directory-Tags******

    // ******Store-Directory-Tags******
    public function storeTags(Request $request)
    {
        try {
            $request->validate([
                'tag_name' => 'required|string',
                'status' => 'required|integer|in:1,0'
            ]);
            TagsModel::create([
                'tag_name' => $request->tag_name,
                'status' => $request->status,
            ]);
            return redirect()->route('directory-tags')->with('success', 'Tag added successfully.');
        } catch (Exception $e) {
            Log::error('Error to store tags: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to store tags. Please try again later.');
        }
    }
    // ******Store-Directory-Tags******

    // ******Edit-Directory-Tags******
    public function editTags($id)
    {
        try {
            $tags = TagsModel::findOrFail($id);
            return view('admin.directory.tags.edit', compact('tags'));
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load tags. Please try again later.');
        }
    }
    // ******Edit-Directory-Tags******

    // ******Update-Directory-Tags******
    public function updateTags(Request $request, $id)
    {
        try {
            $request->validate([
                'tag_name' => 'required|string|unique:tags,tag_name,' . $id,
                'status' => 'required|integer|in:1,0'
            ]);
            $tag = TagsModel::findOrFail($id);
            $tag->update([
                'tag_name' => $request->tag_name,
                'status' => $request->status,
            ]);
            return redirect()->route('directory-tags')->with('success', 'Tag updated successfully.');
        } catch (Exception $e) {
            Log::error('Error updating tag: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update tag. Please try again later.');
        }
    }
    // ******Update-Directory-Tags******

    // ******Delete-Directory-Tags******
    public function deleteTags($id)
    {
        try {
            $tag = TagsModel::findOrFail($id);
            $tag->delete();
            return response()->json(['success' => true, 'message' => 'Tag deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting tag: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to delete tag. Please try again later.'], 500);
        }
    }
    // ******Delete-Directory-Tags******


    // ******Show-Directory-Countries******
    public function showCountries()
    {
        try {
            $countries = Country::all();
            return view('admin.directory.countries.index', compact('countries'));
        } catch (Exception $e) {
            Log::error('Error fetching countries: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load countries. Please try again later.');
        }
    }
    // ******Show-Directory-Countries******

    // ******Create-Directory-Countries******
    public function createCountries()
    {
        try {
            return view('admin.directory.countries.add');
        } catch (Exception $e) {
            Log::error('Error loading countries: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load countries. Please try again later.');
        }
    }
    // ******Create-Directory-Countries******

    // ******Store-Directory-Countries******
    public function storeCountries(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string',
                'nicename' => 'required|string',
                'iso' => 'required|string',
                'iso3' => 'required|string',
                'numcode' => 'nullable|integer',
                'phonecode' => 'required|integer',
                'status' => 'required|integer|in:1,0'
            ]);
            Country::create([
                'name' => $request->name,
                'nicename' => $request->nicename,
                'iso' => $request->iso,
                'iso3' => $request->iso3,
                'numcode' => $request->numcode,
                'phonecode' => $request->phonecode,
                'status' => $request->status,
            ]);
            return redirect()->route('directory-countries')->with('success', 'Countries added successfully.');
        } catch (Exception $e) {
            Log::error('Error to store countries: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to store countries. Please try again later.');
        }
    }
    // ******Store-Directory-Countries******

    // ******Edit-Directory-Countries******
    public function editCountries($id)
    {
        try {
            $countries = Country::findOrFail($id);
            return view('admin.directory.countries.edit', compact('countries'));
        } catch (Exception $e) {
            Log::error('Error loading countries: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load countries. Please try again later.');
        }
    }
    // ******Edit-Directory-Countries******

    // ******Update-Directory-Countries******
    public function updateCountries(Request $request, $id)
    {
        try {
            $request->validate([
                'name' => 'required|string|unique:country,name,' . $id,
                'nicename' => 'required|string',
                'iso' => 'required|string',
                'iso3' => 'required|string',
                'numcode' => 'nullable|integer',
                'phonecode' => 'required|integer',
                'status' => 'required|integer|in:1,0'
            ]);
            $countries = Country::findOrFail($id);
            $countries->update([
                'name' => $request->name,
                'nicename' => $request->nicename,
                'iso' => $request->iso,
                'iso3' => $request->iso3,
                'numcode' => $request->numcode,
                'phonecode' => $request->phonecode,
                'status' => $request->status,
            ]);
            return redirect()->route('directory-countries')->with('success', 'Countries updated successfully.');
        } catch (Exception $e) {
            Log::error('Error updating countries: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update countries. Please try again later.');
        }
    }
    // ******Update-Directory-Countries******

    // ******Delete-Directory-Countries******
    public function deleteCountries($id)
    {
        try {
            $countries = Country::findOrFail($id);
            $countries->delete();
            return response()->json(['success' => true, 'message' => 'Countries deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting countries: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to delete countries. Please try again later.'], 500);
        }
    }
    // ******Delete-Directory-Countries******

    // ******Show-Directory-Programs******
    public function showPrograms()
    {
        try {
            $allPrograms = MasajidsPrograms::all();
            return view('admin.directory.programs.index', compact('allPrograms'));
        } catch (Exception $e) {
            Log::error('Error fetching programs: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load programs. Please try again later.');
        }
    }
    // ******Show-Directory-Programs******

    // ******Create-Directory-Programs******
    public function createPrograms()
    {
        try {
            return view('admin.directory.programs.add');
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load programs. Please try again later.');
        }
    }
    // ******Create-Directory-Programs******

    // ******Store-Directory-Programs******
    public function storePrograms(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string',
                'status' => 'required|integer|in:1,0'
            ]);
            Programs::create([
                'name' => $request->name,
                'status' => $request->status,
            ]);
            return redirect()->route('directory-programs')->with('success', 'Programs added successfully.');
        } catch (Exception $e) {
            Log::error('Error to store programs: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to store programs. Please try again later.');
        }
    }
    // ******Store-Directory-Programs******

    // ******Edit-Directory-Programs******
    public function editPrograms($id)
    {
        try {
            $programs = Programs::findOrFail($id);
            return view('admin.directory.programs.edit', compact('programs'));
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load programs. Please try again later.');
        }
    }
    // ******Edit-Directory-Programs******

    // ******Update-Directory-Programs******
    public function updatePrograms(Request $request, $id)
    {
        try {
            $request->validate([
                'name' => 'required|string|unique:programs,name,' . $id,
                'status' => 'required|integer|in:1,0'
            ]);
            $programs = Programs::findOrFail($id);
            $programs->update([
                'name' => $request->name,
                'status' => $request->status,
            ]);
            return redirect()->route('directory-programs')->with('success', 'Programs updated successfully.');
        } catch (Exception $e) {
            Log::error('Error updating programs: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update programs. Please try again later.');
        }
    }
    // ******Update-Directory-Programs******

    // ******Delete-Directory-Programs******
    public function deletePrograms($id)
    {
        try {
            $programs = Programs::findOrFail($id);
            $programs->delete();
            return response()->json(['success' => true, 'message' => 'Programs deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting programs: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to delete programs. Please try again later.'], 500);
        }
    }
    // ******Delete-Directory-Programs******
}
