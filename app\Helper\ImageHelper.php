<?php


namespace App\Helper;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class ImageHelper
{

    public function imageUpload($images, $recId, $folder = 'news', $size = 800, $centerCrop = false)
    {
        $imageUrls = [];
        if (is_array($images)) {
            foreach ($images as $image) {
                $path      = "images/$folder/" . $recId;
                if ($folder == 'event-images') {
                    $imageName = $recId . '.' . $image->extension();
                } else {
                    $imageName = self::removeExtensionFromName($image->getClientOriginalName()) . rand(1000, 9999) . '.' . $image->extension();
                }
                File::isDirectory(public_path($path)) or File::makeDirectory(public_path($path), 0777, true, true);
                $imgFile = Image::make($image->getRealPath());
                // $imgFile->resize($size, $size, function ($constraint) {
                //     $constraint->aspectRatio();
                //     $constraint->upsize();
                // });

                // if ($centerCrop) {
                //     $minSize = min($imgFile->height(), $imgFile->width(), $size);
                //     $imgFile->crop($minSize, $minSize);
                // }

                $imgFile->save(public_path($path) . "/" . $imageName);
                $imageUrls[] = $path . "/" . $imageName;
            }
        }

        return $imageUrls;
    }

    public function fileUpload($files, $recId, $folder)
    {
        $fileUrls = [];
        if (is_array($files)) {
            foreach ($files as $file) {
                $path      = "documents/$folder/" . $recId;
                $fileName = self::removeExtensionFromName($file->getClientOriginalName()) . rand(1000, 9999) . '.' . $file->getClientOriginalExtension();
                File::isDirectory(public_path($path)) or File::makeDirectory(public_path($path), 0777, true, true);
                $file->move(public_path($path), $fileName);
                $fileUrls[] = $path . "/" . $fileName;
            }
        }
        return $fileUrls;
    }

    public function imageUploadMemberGallery($images, $recId, $folder = 'news', $size = 800, $centerCrop = false)
    {
        $imageUrls = [];
        if (is_array($images)) {
            foreach ($images as $image) {
                $path      = "images/$folder/" . $recId;
                if ($folder == 'event-images') {
                    $imageName = $recId . '.' . $image->extension();
                } else {
                    $rand =  rand(1000, 9999);
                    $imageName = self::removeExtensionFromName($image->getClientOriginalName()) . $rand . '.' . $image->extension();
                    $thumpImageName = self::removeExtensionFromName($image->getClientOriginalName()) . $rand . '-tmp.' . $image->extension();
                }

                File::isDirectory(public_path($path)) or File::makeDirectory(public_path($path), 0777, true, true);
                $imgFile = Image::make($image->getRealPath());
                // $imgFile->resize($size, $size, function ($constraint) {
                //     $constraint->aspectRatio();
                //     $constraint->upsize();
                // });

                // if ($centerCrop) {
                //     $minSize = min($imgFile->height(), $imgFile->width(), $size);
                //     $imgFile->crop($minSize, $minSize);
                // }

                $imgFile->save(public_path($path) . "/" . $imageName);
                $imageUrls[] = $path . "/" . $imageName;


                //thumbnail save start
                $imgThumbFile = Image::make($image->getRealPath());
                $imgThumbFile->resize(200, 200, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
                $imgThumbFile->save(public_path($path) . "/" . $thumpImageName);
                //thumbnail save end
            }
        }

        return $imageUrls;
    }

    public function imageUploadCustomNameReturn($images, $recId, $folder = 'news', $size = 800, $centerCrop = false)
    {
        $imageUrls = [];
        if (is_array($images)) {
            foreach ($images as $image) {
                $path      = "images/$folder/" . $recId;
                if ($folder == 'event-images') {
                    $imageName = $recId . '.' . $image->extension();
                } else {
                    $imageName = self::removeExtensionFromName($image->getClientOriginalName()) . rand(1000, 9999) . '.' . $image->extension();
                }
                File::isDirectory(public_path($path)) or File::makeDirectory(public_path($path), 0777, true, true);
                $imgFile = Image::make($image->getRealPath());
                // $imgFile->resize($size, $size, function ($constraint) {
                //     $constraint->aspectRatio();
                //     $constraint->upsize();
                // });

                // if ($centerCrop) {
                //     $minSize = min($imgFile->height(), $imgFile->width(), $size);
                //     $imgFile->crop($minSize, $minSize);
                // }

                $imgFile->save(public_path($path) . "/" . $imageName);
                //$imageUrls[] = $path . "/" . $imageName;
                $imageUrls[] = $imageName;
            }
        }

        return $imageUrls[0];
    }

    public function imageUploadBase64($base64, $recId, $folder = 'news', $size = 800, $centerCrop = false)
    {
        $imageUrl = null;

        if (is_string($base64)) {
            $image     = self::base64ToFile($base64);
            $path      = "images/$folder/" . $recId;
            $imageName = self::removeExtensionFromName($image->getClientOriginalName()) . rand(1000, 9999) . '.' . $image->extension();
            File::isDirectory(public_path($path)) or File::makeDirectory(public_path($path), 0777, true, true);
            $imgFile = Image::make($image->getRealPath());
            // $imgFile->resize($size, $size, function ($constraint) {
            //     $constraint->aspectRatio();
            //     $constraint->upsize();
            // });

            // if ($centerCrop) {
            //     $minSize = min($imgFile->height(), $imgFile->width(), $size);
            //     $imgFile->crop($minSize, $minSize);
            // }

            $imgFile->save(public_path($path) . "/" . $imageName);
            $imageUrl = $path . "/" . $imageName;
        }

        return $imageUrl;
    }

    public static function base64ToFile($base64)
    {
        $fileData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64));

        $tmpFilePath = sys_get_temp_dir() . '/' . Str::uuid()->toString();
        file_put_contents($tmpFilePath, $fileData);

        $tmpFile = new \Illuminate\Http\File($tmpFilePath);

        return new UploadedFile(
            $tmpFile->getPathname(),
            $tmpFile->getFilename(),
            $tmpFile->getMimeType(),
            0,
            true // Mark it as test, since the file isn't from real HTTP POST.
        );
    }

    public static function removeExtensionFromName($fileName)
    {
        $arr = explode('.', $fileName);

        return $arr[0];
    }
}
