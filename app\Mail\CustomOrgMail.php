<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CustomOrgMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $role;
    public $orgInfo;
    public $user;
    public function __construct($role, $orgInfo, $user)
    {
        $this->role = $role;
        $this->orgInfo = $orgInfo;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if ($this->role === 'admin') {
            $subject = 'Admin Action Required: New Organization Added';
        } elseif ($this->role === 'organization') {
            $subject = 'Action Required: Your organizations added to our portal';
        } elseif ($this->role === 'organizer') {
            $subject = 'Your organizations in pending status';
        } 
        return $this->subject($subject)
            ->view('admin.directory.custom-directory-mail')->with([
                    'role' => $this->role,
                    'user' => $this->user, 
                    'org' => $this->orgInfo
                ]);
    }
}
