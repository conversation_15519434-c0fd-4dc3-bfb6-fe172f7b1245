<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TaxVerifyMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $user_type;
    public $action;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $user_type = "admin", $action = "organizer-action")
    {
        $this->user = $user;
        $this->user_type = $user_type;
        $this->action = $action;
        if ($action == 'admin-action') {
            if ($user_type == 'organizer') {
                $this->subject = 'You have received an update regarding the Tax Form';
            } else {
                $this->subject = 'Action Taken on Tax Form';
            }
        } else {
            if ($user_type == 'organizer') {
                $this->subject = 'Successfully submitted the Tax Form';
            } else {
                $this->subject = 'New Tax Form Receieved.';
            }
        }

    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('tax-verify')->with(['user' => $this->user, 'user_type' => $this->user_type, 'action' => $this->action]);
    }
}
