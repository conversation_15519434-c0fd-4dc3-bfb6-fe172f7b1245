<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Amenities extends Model
{
    use HasFactory;

    protected $table = 'masajids_amenities';
    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    public static function getAmenitiesWithCountOfMasajids()
    {
        return DB::table('masajids_amenities')
            ->select('masajids_amenities.*', DB::raw('COUNT(masajids.id) as masajids_count'))
            ->leftJoin('masajids', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(masajids.facilities, JSON_QUOTE(CAST(masajids_amenities.id AS CHAR)))"), DB::raw('1'))
                    ->where('masajids.status', '=', 1);
            })
            ->groupBy('masajids_amenities.id', 'masajids_amenities.name', 'masajids_amenities.description', 'masajids_amenities.status')
            ->orderBy('masajids_amenities.id', 'asc')
            ->get();
    }
}
