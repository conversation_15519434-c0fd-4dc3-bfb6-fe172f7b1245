<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Termwind\Components\Raw;
use Carbon\Carbon;

class DonationPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'image',
        'description',
        'target',
        'started_date',
        'expired_date',
        'user_id',
        'custom_amount',
        'donation_designation',
        'supported_document',
        'donation_category_id',
        'directory_id',
        'shared_count',
        'added_by',
        'last_edited_by',
        'active',
        'is_external_link',
        'external_link'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function directory()
    {
        return $this->belongsTo(Directory::class, 'directory_id');
    }

    public function added_by_user()
    {
        return $this->belongsTo(User::class, 'added_by', 'id');
    }

    public function last_edited_by_user()
    {
        return $this->belongsTo(User::class, 'last_edited_by', 'id');
    }

    public function category()
    {
        return $this->belongsTo(DonationCategory::class, 'donation_category_id');
    }

    public function payments()
    {
        return $this->hasMany(DonationPayment::class);
    }


    public static function GetPaymentTotalById($id)
    {
        $data = self::where('donation_posts.id', $id)
            ->leftJoin('donation_payments', 'donation_payments.donation_post_id', '=', 'donation_posts.id')
            ->select(DB::raw('SUM(donation_payments.amount) as amount'))
            ->where('donation_payments.active', true)
            ->where('donation_payments.status', DonationPayment::PS_SUCCESS)
            ->first();

        if ($data) {
            return $data->amount != null ? $data->amount : '0.00';
        }

        return '0.00';
    }
    public static function GetPaymentCountById($id)
    {
        $data = self::where('donation_posts.id', $id)->leftJoin('donation_payments', 'donation_payments.donation_post_id', '=', 'donation_posts.id')
            ->select('donation_payments.id')
            ->where('donation_payments.active', true)
            ->where('donation_payments.status', DonationPayment::PS_SUCCESS)
            ->count();

        return $data;
    }

    public static function getPublic($donationPosts)
    {
        foreach ($donationPosts as $post) {
            if ($post->image && is_array(json_decode($post->image, true))) {
                $images = json_decode($post->image, true);
                $imageArray = [];
                foreach ($images as $image) {
                    array_push($imageArray, \App\Models\AppSetting::getAssetUrl($image));
                }
                $post->image = $imageArray;
            }
            $post->total_collection = self::GetPaymentTotalById($post->id);
            $post->donator_count    = self::GetPaymentCountById($post->id);
        }
        return $donationPosts;
    }

    public static function getPublicSingle($post)
    {
        if ($post != null) {
            if ($post->image && is_array(json_decode($post->image, true))) {
                $images = json_decode($post->image, true);
                $imageArray = [];
                foreach ($images as $image) {
                    array_push($imageArray, \App\Models\AppSetting::getAssetUrl($image));
                }
                $post->image = $imageArray;
            }
            $post->total_collection = self::GetPaymentTotalById($post->id);
            $post->donator_count    = self::GetPaymentCountById($post->id);
            $post->time_ago    = Carbon::parse($post->created_at)->diffForHumans();
        }

        return $post;
    }
}
