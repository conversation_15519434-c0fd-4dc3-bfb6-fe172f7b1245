<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Category;
use App\Models\Ticket;
use App\Models\Order;
use App\Models\Setting;
use App\Http\Controllers\AppHelper;
use App\Models\User;
use App\Models\AppUser;
use Carbon\Carbon;
use App\Models\Directory;
use App\Models\EventCategoryLink;
use Throwable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class EventController extends Controller
{

    public function publishEvent(Request $request)
    {
        $request->validate([
            'id' => 'bail|required',
        ]);
        $event = Event::find($request->id);
        if (isset($event->id) && !empty($event->id)) {
            if (isset($event->is_external_link) && $event->is_external_link == '0') {
                $tickets = Ticket::where([['event_id', $request->id], ['is_deleted', 0]])->get();
                if ($tickets->isEmpty()) {
                    return response()->json(['error' => 1, 'message' => __('You dont have tickets to publish the events.')]);
                    // return redirect()->route('events.index')->with('error', __('You dont have tickets to publish the events.'));
                } else {
                    $event->status = 1;
                    $event->save();
                    return response()->json(['error' => 0, 'message' => __('Event has been published successfully.')]);
                }
            } else {
                $event->status = 1;
                $event->save();
                return response()->json(['error' => 0, 'message' => __('Event has been published successfully.')]);
            }
        } else {
            return response()->json(['error' => 1, 'message' => __('Event details not found.')]);
        }
    }

    public function archiveEvents()
    {
        $twoWeeksAgo = Carbon::now()->subWeeks(2);
        $user = Auth::user();
        if ($user->hasRole('admin')) {
            $events = Event::with(['category:id,name'])
                ->where([
                    ['is_deleted', 0],
                    ['event_status', 'Pending'],
                    ['end_time', '<', $twoWeeksAgo]
                ])->get();
        } elseif ($user->hasRole('Organizer')) {
            $events = Event::with(['category:id,name'])
                ->where([
                    ['is_deleted', 0],
                    ['event_status', 'Pending'],
                    ['end_time', '<', $twoWeeksAgo]
                ])->where('user_id', $user->id)->get();
        }
        return view('admin.event.archive-events', compact('events', 'user'));
    }

    public function index(Request $request)
    {
        abort_if(Gate::denies('event_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $twoWeeksAgo = Carbon::now()->subWeeks(2);
        if (Auth::user()->hasRole('admin')) {
            $timezone = Setting::find(1)->timezone;
            $date = Carbon::now($timezone);
            $events = Event::with(['category:id,name'])
                ->where([
                    // ['status', 1],
                    ['is_deleted', 0],
                    ['event_status', 'Pending'],
                    ['end_time', '>', $twoWeeksAgo]
                ]);
            $chip = array();
            if ($request->has('type') && $request->type != null) {
                $chip['type'] = $request->type;
                $events = $events->where('type', $request->type);
            }
            if ($request->has('category') && $request->category != null) {
                $chip['category'] = Category::find($request->category)->name;
                $events = $events->where('category_id', $request->category);
            }
            if ($request->has('duration') && $request->duration != null) {
                $chip['date'] = $request->duration;
                if ($request->duration == 'Today') {
                    $temp = Carbon::now($timezone)->format('Y-m-d');
                    $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
                } else if ($request->duration == 'Tomorrow') {
                    $temp = Carbon::tomorrow($timezone)->format('Y-m-d');
                    $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
                } else if ($request->duration == 'ThisWeek') {
                    $now = Carbon::now($timezone);
                    $weekStartDate = $now->startOfWeek()->format('Y-m-d H:i:s');
                    $weekEndDate = $now->endOfWeek()->format('Y-m-d H:i:s');
                    $events = $events->whereBetween('start_time', [$weekStartDate, $weekEndDate]);
                } else if ($request->duration == 'date') {
                    if (isset($request->date)) {
                        $temp = Carbon::parse($request->date)->format('Y-m-d H:i:s');
                        $events = $events->whereBetween('start_time', [$request->date . ' 00:00:00', $request->date . ' 23:59:59']);
                    }
                }
            }
            $events = $events->orderBy('start_time', 'DESC')->get();
        } elseif (Auth::user()->hasRole('Organizer')) {
            $timezone = Setting::find(1)->timezone;
            $date = Carbon::now($timezone);
            $events = Event::with(['category:id,name'])
                ->where([
                    // ['status', 1],
                    ['user_id', Auth::user()->id],
                    ['is_deleted', 0],
                    ['event_status', 'Pending'],
                    ['end_time', '>', $twoWeeksAgo]
                    // [
                    //     'end_time',
                    //     '>',
                    //     $date->format('Y-m-d H:i:s')
                    // ]
                ]);
            $chip = array();
            if ($request->has('type') && $request->type != null) {
                $chip['type'] = $request->type;
                $events = $events->where('type', $request->type);
            }
            if ($request->has('category') && $request->category != null) {
                $chip['category'] = Category::find($request->category)->name;
                $events = $events->where('category_id', $request->category);
            }
            if ($request->has('duration') && $request->duration != null) {
                $chip['date'] = $request->duration;
                if ($request->duration == 'Today') {
                    $temp = Carbon::now($timezone)->format('Y-m-d');
                    $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
                } else if ($request->duration == 'Tomorrow') {
                    $temp = Carbon::tomorrow($timezone)->format('Y-m-d');
                    $events = $events->whereBetween('start_time', [$temp . ' 00:00:00', $temp . ' 23:59:59']);
                } else if ($request->duration == 'ThisWeek') {
                    $now = Carbon::now($timezone);
                    $weekStartDate = $now->startOfWeek()->format('Y-m-d H:i:s');
                    $weekEndDate = $now->endOfWeek()->format('Y-m-d H:i:s');
                    $events = $events->whereBetween('start_time', [$weekStartDate, $weekEndDate]);
                } else if ($request->duration == 'date') {
                    if (isset($request->date)) {
                        $temp = Carbon::parse($request->date)->format('Y-m-d H:i:s');
                        $events = $events->whereBetween('start_time', [$request->date . ' 00:00:00', $request->date . ' 23:59:59']);
                    }
                }
            }
            $events = $events->orderBy('start_time', 'ASC')->get();
        }
        return view('admin.event.index', compact('events'));
    }

    public function create()
    {
        abort_if(Gate::denies('event_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $category = Category::where('status', 1)->orderBy('id', 'DESC')->get();
        // $users = User::role('Organizer')->orderBy('id', 'DESC')->get();
        $organizations = Directory::where('status', 1)->orderBy('business_name', 'asc')->get();
        if (Auth::user()->hasRole('admin')) {
            $scanner = User::role('scanner')->orderBy('id', 'DESC')->get();
        } else if (Auth::user()->hasRole('Organizer')) {
            $scanner = User::role('scanner')->where('org_id', Auth::user()->id)->OrWhere('org_id', 0)->orderBy('id', 'DESC')->get();
        }
        return view('admin.event.create', compact('category', 'scanner', 'organizations'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'bail|required',
            // 'image' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            'start_time' => 'bail|required',
            'end_time' => 'bail|required',
            'category_id' => 'bail|required|array',
            'type' => 'bail|required',
            'address' => 'bail|required_if:type,offline',
            'lat' => 'bail|required_if:type,offline',
            'lang' => 'bail|required_if:type,offline',
            // 'status' => 'bail|required',
            'url' => 'bail|required_if:type,online',
            'description' => 'bail|required',
            // 'scanner_id' => 'bail|required_if:type,offline',
            // 'people' => 'bail|required',
        ], [
            'lat.required_if' => 'Invalid Location',
            'lang.required_if' => 'Invalid Location',
        ]);
        $data = $request->all();
        if ($request->has('disclaimer')) {
            $request->validate([
                'disclaimer_text' => 'required'
            ]);
            $data['disclaimer'] = 1;
        } else {
            $data['disclaimer'] = 0;
        }
        // if ($request->type == 'offline') {
        //     $data['scanner_id'] = $request->scanner_id;
        // }
        $data['gallery'] = [];
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        if (!empty($data['gallery'])) {
            $data['image'] = $data['gallery'][0];
            unset($data['gallery'][0]);
        }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['security'] = 1;
        if ($request->hasFile('image')) {
            $data['image'] = (new AppHelper)->saveImage($request);
        }

        $data['is_external_link'] = $request->is_external_link ?? 0;
        $data['external_link'] = "";
        if ($data['is_external_link']) {
            $request->validate([
                'external_link' => 'required'
            ]);
            $data['external_link'] = $request->external_link;
        }
        $prefixUniqueCheck = false;
        while (!$prefixUniqueCheck) {
            $prefix = Str::random(9);
            $prefixUniqueCheckQuery = Event::where('prefix', $prefix)->first();
            if (!$prefixUniqueCheckQuery) {
                $data['prefix'] = $prefix;
                break;
            } else {
                $prefixUniqueCheck = false;
            }
        }
        if (!$data['is_external_link']) {
            $data['status'] = 0;
        }
        if (!empty($request->yt_url) && !filter_var($request->yt_url, FILTER_VALIDATE_URL)) {
            $data['yt_url'] = 'https://www.youtube.com/embed/' . $request->yt_url;
        }
        $data['people'] = $data['people'] ?? 0;
        $categoryIds = $data['category_id'];
        unset($data['category_id']);

        $event = Event::create($data);
        if (isset($event->id)) {
            foreach ($categoryIds as $categoryId) {
                $categoryLink = new EventCategoryLink();
                $categoryLink->event_id = $event->id;
                $categoryLink->category_id = $categoryId;
                $categoryLink->save();
            }
        }
        return redirect()->route('events.index')->withStatus(__('Event has added successfully. Please create the tickets to publish the event.'));
    }

    public function show($event)
    {
        $event = Event::with(['category', 'organization'])->find($event);
        $event->ticket = Ticket::where([['event_id', $event->id], ['is_deleted', 0]])->orderBy('id', 'DESC')
            ->get();
        (new AppHelper)->eventStatusChange();
        $event->sales = Order::with(['customer:id,name', 'ticket:id,name'])->where('event_id', $event->id)
            ->orderBy('id', 'DESC')->get();
        foreach ($event->ticket as $value) {
            $value->used_ticket = Order::whereHas('orderchild', function ($query) use ($value) {
                $query->where('ticket_id', $value->id);
            })->with([
                'orderchild' => function ($query) use ($value) {
                    $query->where('ticket_id', $value->id);
                }
            ])->get()->sum(function ($order) {
                return $order->orderchild->sum('quantity');
            });
        }

        return view('admin.event.view', compact('event'));
    }

    public function edit(Event $event)
    {
        abort_if(Gate::denies('event_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $category = Category::where('status', 1)->orderBy('id', 'DESC')->get();
        // $users = User::role('Organizer')->orderBy('id', 'DESC')->get();
        $organizations = Directory::where('status', 1)->orderBy('business_name', 'asc')->get();
        if (Auth::user()->hasRole('admin')) {
            $scanner = User::role('scanner')->orderBy('id', 'DESC')->get();
        } else if (Auth::user()->hasRole('Organizer')) {
            $scanner = User::role('scanner')->where('org_id', Auth::user()->id)->OrWhere('org_id', 0)->orderBy('id', 'DESC')->get();
        }
        $gallery = [];
        $galleryImages = [$event->image];
        if (!empty($event->gallery)) {
            $galleryImages = array_merge(explode(',', $event->gallery), $galleryImages);
        }
        if (!empty($galleryImages)) {
            foreach ($galleryImages as $key => $galleryImage) {
                array_push($gallery, ['id' => $key, 'src' => url('images/upload/' . $galleryImage)]);
            }
        }
        $selectedCategoryIds = $categoryIds = EventCategoryLink::where('event_id', $event->id)->pluck('category_id')->toArray();
        return view('admin.event.edit', compact('event', 'category', 'organizations', 'selectedCategoryIds', 'scanner', 'gallery'));
    }

    public function update(Request $request, Event $event)
    {
        $request->validate([
            'name' => 'bail|required',
            'start_time' => 'bail|required',
            'end_time' => 'bail|required',
            'category_id' => 'bail|required|array',
            'type' => 'bail|required',
            'address' => 'bail|required_if:type,offline',
            'lat' => 'bail|required_if:type,offline',
            'lang' => 'bail|required_if:type,offline',
            // 'status' => 'bail|required',
            'url' => 'bail|required_if:type,online',
            'description' => 'bail|required',
            // 'scanner_id' => 'bail|required_if:type,offline',
            // 'people' => 'bail|required',
        ]);
        $data = $request->all();
        if ($request->has('disclaimer')) {
            $request->validate([
                'disclaimer_text' => 'required'
            ]);
            $data['disclaimer'] = 1;
        } else {
            $data['disclaimer'] = 0;
        }
        // if ($request->type == 'offline') {
        //     // $data['scanner_id'] = implode(',', $request->scanner_id);
        //     $data['scanner_id'] = $request->scanner_id;
        // }
        // if ($request->hasFile('image')) {
        //     (new AppHelper)->deleteFile($event->image);
        //     $data['image'] = (new AppHelper)->saveImage($request);
        // }
        $gallerys = [$event->image];
        $gallerys = array_merge($gallerys, (!empty($event->gallery)) ? explode(',', $event->gallery) : []);
        $constructedGallery = [];
        if ($request->has('preloaded')) {
            foreach ($gallerys as $key => $gallery) {
                if (in_array($key, $request->preloaded) !== false) {
                    array_push($constructedGallery, $gallery);
                } else {
                    (new AppHelper)->deleteFile($gallery);
                }
            }
        }
        $data['gallery'] = $constructedGallery;
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        if (!empty($data['gallery'])) {
            $data['image'] = $data['gallery'][0];
            unset($data['gallery'][0]);
        }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        if (!isset($event->prefix)) {
            $data['prefix'] = Str::random(9);
        }
        $data['is_external_link'] = $request->is_external_link ? 1 : 0;
        $data['external_link'] = "";
        if ($data['is_external_link']) {
            $request->validate([
                'external_link' => 'required'
            ]);
            $data['external_link'] = $request->external_link;
        }
        if (!empty($request->yt_url) && !filter_var($request->yt_url, FILTER_VALIDATE_URL)) {
            $data['yt_url'] = 'https://www.youtube.com/embed/' . $request->yt_url;
        }
        if (!$data['is_external_link']) {
            $ticketCounts = Ticket::where('event_id', $event->id)->count();
            if ($ticketCounts == 0) {
                $data['status'] = 0;
            }
        }
        $data['people'] = $data['people'] ?? 0;
        $eventId = $event->id;
        $event = Event::find($event->id)->update($data);
        $categoryIds = $data['category_id'];
        unset($data['category_id']);
        EventCategoryLink::where('event_id', $eventId)->delete();
        foreach ($categoryIds as $categoryId) {
            $categoryLink = new EventCategoryLink();
            $categoryLink->event_id = $eventId;
            $categoryLink->category_id = $categoryId;
            $categoryLink->save();
        }
        return redirect()->route('events.index')->withStatus(__('Event has updated successfully.'));
    }

    public function destroy(Event $event)
    {
        try {
            Event::find($event->id)->update(['is_deleted' => 1, 'event_status' => 'Deleted']);
            $ticket = Ticket::where('event_id', $event->id)->update(['is_deleted' => 1]);
            return true;
        } catch (Throwable $th) {
            return response('Data is Connected with other Data', 400);
        }
    }

    public function getMonthEvent(Request $request)
    {
        (new AppHelper)->eventStatusChange();
        $day = Carbon::parse($request->year . '-' . $request->month . '-01')->daysInMonth;
        if (Auth::user()->hasRole('Organizer')) {
            $data = Event::whereBetween('start_time', [$request->year . "-" . $request->month . "-01 12:00", $request->year . "-" . $request->month . "-" . $day . "  23:59"])
                ->where([['status', 1], ['is_deleted', 0], ['user_id', Auth::user()->id]])
                ->orderBy('id', 'DESC')
                ->get();
        }
        if (Auth::user()->hasRole('admin')) {
            $data = Event::whereBetween('start_time', [$request->year . "-" . $request->month . "-01 12:00", $request->year . "-" . $request->month . "-" . $day . " 23:59"])
                ->where([['status', 1], ['is_deleted', 0]])->orderBy('id', 'DESC')->get();
        }
        foreach ($data as $value) {
            $value->tickets = Ticket::where([['event_id', $value->id], ['is_deleted', 0]])->sum('quantity');
            $value->sold_ticket = Order::where('event_id', $value->id)->sum('quantity');
            $value->day = $value->start_time->format('D');
            $value->date = $value->start_time->format('d');
            $value->average = $value->tickets == 0 ? 0 : $value->sold_ticket * 100 / $value->tickets;
        }
        return response()->json(['data' => $data, 'success' => true], 200);
    }

    public function eventGallery($id)
    {
        $data = Event::find($id);
        return view('admin.event.gallery', compact('data'));
    }

    public function addEventGallery(Request $request)
    {
        $event = array_filter(explode(',', Event::find($request->id)->gallery));
        if ($request->hasFile('file')) {
            $images = $request->file('file');
            foreach ($images as $image) {
                $name = uniqid() . '.' . $image->getClientOriginalExtension();
                $destinationPath = public_path('/images/upload');
                $image->move($destinationPath, $name);
                array_push($event, $name);
            }
            Event::find($request->id)->update(['gallery' => implode(',', $event)]);
        }
        return true;
    }

    public function removeEventImage($image, $id)
    {

        $gallery = array_filter(explode(',', Event::find($id)->gallery));
        if (count(array_keys($gallery, $image)) > 0) {
            if (($key = array_search($image, $gallery)) !== false) {
                unset($gallery[$key]);
            }
        }
        $aa = implode(',', $gallery);
        $data = Event::find($id);
        $data->gallery = $aa;
        $data->update();
        return redirect()->back();
    }
}
