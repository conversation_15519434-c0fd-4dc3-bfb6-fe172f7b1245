<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CustomOrgApproveMail extends Mailable
{
    use Queueable, SerializesModels;
 
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $role;
    public $directory;
    public function __construct($role, $directory)
    {
        $this->role = $role;
        $this->directory = $directory;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if ($this->role === 'admin') {
            $subject = $this->directory->name . ' has been successfully approved by Organization';
        } elseif ($this->role === 'organization') {
            $subject = 'Thank you for your prompt action';
        } elseif ($this->role === 'organizer') {
            $subject = 'Your organization has been approved by the organization admin';
        }
        return $this->subject($subject)
            ->view('admin.directory.custom-directory-approve-mail')->with([
                    'role' => $this->role,
                    'org' => $this->directory
                ]);
    }
}
