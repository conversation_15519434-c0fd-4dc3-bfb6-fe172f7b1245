<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use \App\Models\User;
use \App\Models\DonationPost;
use \App\Models\DonationCategory;

class DonationController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        $donation_posts = DonationPost::orderBy('id', 'desc')->with('user', 'category', 'added_by_user', 'last_edited_by_user')->get();
        // } else {
        // $donation_posts = DonationPost::where('user_id', $userId)->orderBy('id', 'desc')->with('user', 'category', 'added_by_user', 'last_edited_by_user')->get();
        // }
        // dd(json_decode($donation_posts[0]->image, true));
        return view('admin.donations.donation_posts', compact('donation_posts'))
            ->with('i', 1);
    }

    public function getAllTypeOfOrganizations()
    {
        $organizations = [];
        $organizationsArray = User::getOrganizationUsers();
        if (!empty($organizationsArray)) {
            foreach ($organizationsArray as $organization) {
                if ($organization->image)
                    $organization->image = AppSetting::getAssetUrl($organization->image);
                $orgTypeQuery = $organization->get_org_type;
                $org_type = $orgTypeQuery->title ?? "";
                if (!isset($organizations[$org_type])) {
                    $organizations[$org_type] = [];
                }
                if (!empty($org_type)) {
                    array_push($organizations[$org_type], $organization->toArray());
                }
            }
        }
        return $organizations;
    }

    public function create(DonationPost $donation_posts)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        // $is_admin = false;
        $organizations = User::role('Organizer')->orderBy('id', 'DESC')->get();
        // $organizations = [];
        // if (User::isAccessByAdmin()) {
        //     $is_admin = true;
        //     $organizations = $this->getAllTypeOfOrganizations();
        //     $donation_categories = DonationCategory::where('active', true)->get();
        // } else {
        $donation_categories = DonationCategory::where('user_id', $userId)->where('active', true)->get();
        // }

        return view('admin.donations.donation_posts_add', ['organizations' => $organizations, 'donation_posts' => $donation_posts, 'donation_categories' => $donation_categories]);
    }

    public function store(Request $request)
    {
        $user = auth()->user();
        // if (\App\Models\User::isAccessByAdmin()) {
        //     $userId = $request->organizations;
        // } else {
        //     $userId = User::getUserId($user);
        // }
        $userId = $request->organizations;

        $request->validate([
            'title' => 'required',
            // 'target'               => 'required|numeric',
            'donation_category_id' => 'required',
            'expired_date' => 'required|date',
            'description' => 'required|min:50',
        ]);

        $donation_posts = DonationPost::create([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'donation_category_id' => $request->input('donation_category_id'),
            'expired_date' => DateTime::createFromFormat('m/d/Y', $request->input('expired_date'))->format('Y-m-d'),
            'target' => (!empty($request->input('target'))) ? $request->input('target') : 0,
            'user_id' => $userId,
            'added_by' => $user->id,
            'active' => $request->input('active') ?? 0,
        ]);
        $gallery = [];
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($gallery, $imageName);
            }
        }
        if (!empty($gallery)) {
            $donation_posts->update([
                'image' => (!empty($gallery)) ? json_encode($gallery) : ""
            ]);
        }
        // if (!User::isAccessByAdmin()) {
        //     $admins = \App\Models\User::getUsersByRole([\App\Models\User::SUPERADMINROLENAME, \App\Models\User::ADMINROLENAME])->pluck('email')->toArray();
        //     \Mail::to($admins)->send(new \App\Mail\DonationMail($donation_posts, 'admin-notification'));
        // }
        return redirect()->route('donation_posts.index')
            ->with('success', 'DonationPost created successfully.');
    }

    public function show($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        $donation_posts = DonationPost::where('id', $id)->with('category', 'user', 'payments')->first();
        // } else {
        //     $donation_posts = DonationPost::where('user_id', $userId)->where('id', $id)->with('category', 'user', 'payments')->first();
        // }

        $images = json_decode($donation_posts->image) ?? [];
        $total_payment = DonationPost::GetPaymentTotalById($donation_posts->id);
        return view('admin.donations.donation_posts_show', compact('donation_posts', 'images', 'total_payment'));
    }

    public function edit($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        // $is_admin = false;
        // if (User::isAccessByAdmin()) {
        // $is_admin = true;
        $donation_posts = DonationPost::find($id);
        $donation_categories = DonationCategory::where('active', true)->get();
        $organizations = User::role('Organizer')->orderBy('id', 'DESC')->get();
        // } else {
        //     $donation_posts = DonationPost::where('user_id', $userId)->first($id);
        //     $donation_categories = DonationCategory::where('user_id', $userId)->where('active', true)->get();
        // }

        $image = [];
        if ($donation_posts->image) {
            $image = json_decode($donation_posts->image);
            $image = array_map(function ($key, $value) {
                return ['id' => $key + 1, 'src' => url('/images/upload/' . $value)];
            }, array_keys($image), $image);
        }
        return view('admin.donations.donation_posts_add', compact(
            'donation_posts',
            'image',
            'donation_categories',
            'organizations'
        ));
    }

    public function update(Request $request, $id)
    {
        $user = auth()->user();
        // if (\App\Models\User::isAccessByAdmin()) {
        $userId = $request->organizations;
        // } else {
        // $userId = User::getUserId($user);
        // }

        // if (User::isAccessByAdmin()) {
        $donation_posts = DonationPost::find($id);
        // } else {
        // $donation_posts = DonationPost::where('user_id', $userId)->where('id', $id)->first();
        // }

        $request->validate([
            'title' => 'required',
            'donation_category_id' => 'required',
            'expired_date' => 'required|date',
            'description' => 'required|min:50',
        ]);
        $donation_posts->update([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'donation_category_id' => $request->input('donation_category_id'),
            'expired_date' => \DateTime::createFromFormat('m/d/Y', $request->input('expired_date'))->format('Y-m-d'),
            'target' => (!empty($request->input('target'))) ? $request->input('target') : 0,
            'last_edited_by' => $user->id,
            'user_id' => $userId,
            'active' => $request->input('active') ?? 0,
        ]);
        $images = [];
        if ($donation_posts->image && $request->old) {
            $updatedImgs = $request->old;
            $images = json_decode($donation_posts->image);
            $images = array_map(function ($key, $value) use ($updatedImgs) {
                if (in_array(($key + 1), $updatedImgs)) {
                    return $value;
                } else {
                    (new AppHelper)->deleteFile($value);
                }
            }, array_keys($images), $images);
        }
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($images, $imageName);
            }
        }
        $donation_posts->update([
            'image' => json_encode(array_values(array_filter($images)))
        ]);

        return redirect()->route('donation_posts.index')
            ->with('success', 'DonationPost updated successfully');
    }

    public function destroy($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        // if (User::isAccessByAdmin()) {
        //     $donation_posts = DonationPost::find($id);
        // } else {
        $donation_posts = DonationPost::where('user_id', $userId)->where('id', $id)->first();
        // }

        $payments = $donation_posts->payments;
        if (count($payments) != 0) {
            return redirect()->route('donation_posts.index')
                ->with('warning', 'Could not delete!, This post has payment/s already');
        }
        $donation_posts->delete();
        return redirect()->route('donation_posts.index')
            ->with('success', 'DonationPost deleted successfully');
    }

    public function categoryIndex()
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        $categories = DonationCategory::where('user_id', $userId)->with('donation_posts', 'user', 'added_by_user', 'last_edited_by_user')->get();

        return view('admin.donations.donation_categories', compact('categories'))->with('i', 1);
        ;
    }

    public function categoryCreate(DonationCategory $category)
    {
        return view('admin.donations.donation_categories_add', compact('category'));
    }

    public function categoryStore(Request $request)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        $request->validate([
            'name' => 'required',
            'description' => 'required'
        ]);

        $category = DonationCategory::create([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'user_id' => $userId,
            'added_by' => $user->id,
        ]);

        return redirect()->route('donation_categories.index')
            ->with('success', 'Category created successfully.');
    }


    public function categoryEdit(DonationCategory $category)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        //!User::isAccessByAdmin() && 
        if ($category->user_id != $userId) {
            return abort(403, 'you dont have access to this page');
        }
        return view('admin.donations.donation_categories_add', compact('category'));
    }

    public function categoryUpdate(Request $request, DonationCategory $category)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        //!User::isAccessByAdmin() && 
        if ($category->user_id != $userId) {
            return abort(403, 'you dont have access to this page');
        }
        $request->validate([
            'name' => 'required',
            'description' => 'required'
        ]);

        $category->update([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'last_edited_by' => $user->id
        ]);

        return redirect()->route('donation_categories.index')
            ->with('success', 'Category updated successfully');
    }

    public function categoryDestroy(DonationCategory $category)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        // if (!User::isAccessByAdmin() && $category->user_id != $userId) {
        //     return abort(403, 'you dont have access to this page');
        // }
        $donations = $category->donation_posts;
        if (count($donations) != 0) {
            return redirect()->route('donation_categories.index')
                ->with('warning', 'This category not empty');
        }

        $category->delete();

        return redirect()->route('donation_categories.index')
            ->with('success', 'Category deleted successfully');
    }
}
