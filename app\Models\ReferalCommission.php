<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReferalCommission extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'commission_from',
        'commission_to',
        'amount',
        'status',
        'reason',
    ];

    public function referrer()
    {
        return $this->belongsTo(User::class, 'commission_from', 'id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }
}
