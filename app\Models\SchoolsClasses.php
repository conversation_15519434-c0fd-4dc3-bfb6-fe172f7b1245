<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SchoolsClasses extends Model
{
    use HasFactory;

    protected $table = 'schools_classes';

    protected $fillable = [
        'class_name',
        'school_id',
        'category_id',
        'class_start_date',
        'class_end_date',
        'class_schedule',
        'class_start_time',
        'class_end_time',
        'age',
        'prerequisites',
        'tution_and_fee',
        'class_format',
        'class_status',
        'extra_fields',
    ];

    public function school()
    {
        return $this->belongsTo(Schools::class);
    }

    public function category()
    {
        return $this->belongsTo(ClassesCategory::class, 'category_id');
    }
}
