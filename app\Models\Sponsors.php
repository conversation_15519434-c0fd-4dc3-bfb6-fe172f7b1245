<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sponsors extends Model
{
    use HasFactory;
    protected $table = 'sponsors';

    protected $fillable = [
        'name',
        'image',
        'url',
        'event_id',
        'status',
    ];

    public function events()
    {
        return $this->belongsToMany(Event::class, 'event_sponsor', 'sponsor_id', 'event_id');
    }
}
