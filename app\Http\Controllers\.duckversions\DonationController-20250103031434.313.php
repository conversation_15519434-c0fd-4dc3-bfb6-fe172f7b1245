<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use \App\Models\User;

class DonationController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        if (User::isAccessByAdmin()) {
            $donation_posts = DonationPost::orderBy('id', 'desc')->with('user', 'category', 'added_by_user', 'last_edited_by_user')->get();
        } else {
            $donation_posts = DonationPost::where('user_id', $userId)->orderBy('id', 'desc')->with('user', 'category', 'added_by_user', 'last_edited_by_user')->get();
        }

        return view('donations.donation_posts', compact('donation_posts'))
            ->with('i', 1);
    }

    public function getAllTypeOfOrganizations()
    {
        $organizations = [];
        $organizationsArray = User::getOrganizationUsers();
        if (!empty($organizationsArray)) {
            foreach ($organizationsArray as $organization) {
                if ($organization->image)
                    $organization->image = AppSetting::getAssetUrl($organization->image);
                $orgTypeQuery = $organization->get_org_type;
                $org_type = $orgTypeQuery->title ?? "";
                if (!isset($organizations[$org_type])) {
                    $organizations[$org_type] = [];
                }
                if (!empty($org_type)) {
                    array_push($organizations[$org_type], $organization->toArray());
                }
            }
        }
        return $organizations;
    }

    public function create(DonationPost $donation_posts)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        $is_admin = false;
        $organizations = [];
        if (User::isAccessByAdmin()) {
            $is_admin = true;
            $organizations = $this->getAllTypeOfOrganizations();
            $donation_categories = DonationCategory::where('active', true)->get();
        } else {
            $donation_categories = DonationCategory::where('user_id', $userId)->where('active', true)->get();
        }

        return view('donations.donation_posts_add', ['organizations' => $organizations, 'is_admin' => $is_admin, 'donation_posts' => $donation_posts, 'donation_categories' => $donation_categories]);
    }

    public function store(Request $request)
    {
        $user = auth()->user();
        if (\App\Models\User::isAccessByAdmin()) {
            $userId = $request->organizations;
        } else {
            $userId = User::getUserId($user);
        }

        $request->validate([
            'title' => 'required',
            // 'target'               => 'required|numeric',
            'donation_category_id' => 'required',
            'expired_date' => 'required|date',
            'description' => 'required|min:50',
        ]);

        $donation_posts = DonationPost::create([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'donation_category_id' => $request->input('donation_category_id'),
            'expired_date' => $request->input('expired_date'),
            'target' => (!empty($request->input('target'))) ? $request->input('target') : 0,
            'user_id' => $userId,
            'added_by' => $user->id,
            'active' => $request->input('active') ?? 0,
        ]);

        $image = new ImageHelper();
        $imagesArray = $image->imageUpload($request->images, $donation_posts->id, 'donation_post');
        if (!empty($imagesArray)) {
            $donation_posts->update([
                'image' => json_encode($imagesArray)
            ]);
        }
        if (!User::isAccessByAdmin()) {
            $admins = \App\Models\User::getUsersByRole([\App\Models\User::SUPERADMINROLENAME, \App\Models\User::ADMINROLENAME])->pluck('email')->toArray();
            \Mail::to($admins)->send(new \App\Mail\DonationMail($donation_posts, 'admin-notification'));
        }
        return redirect()->route('donation_posts.index')
            ->with('success', 'DonationPost created successfully.');
    }

    public function show($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        if (User::isAccessByAdmin()) {
            $donation_posts = DonationPost::where('id', $id)->with('category', 'user', 'payments')->first();
        } else {
            $donation_posts = DonationPost::where('user_id', $userId)->where('id', $id)->with('category', 'user', 'payments')->first();
        }

        $images = json_decode($donation_posts->image) ?? [];
        $total_payment = DonationPost::GetPaymentTotalById($donation_posts->id);
        return view('donations.donation_posts_show', compact('donation_posts', 'images', 'total_payment'));
    }

    public function edit($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);
        $is_admin = false;
        if (User::isAccessByAdmin()) {
            $is_admin = true;
            $donation_posts = DonationPost::find($id);
            $donation_categories = DonationCategory::where('active', true)->get();
        } else {
            $donation_posts = DonationPost::where('user_id', $userId)->first($id);
            $donation_categories = DonationCategory::where('user_id', $userId)->where('active', true)->get();
        }

        $image = [];
        if ($donation_posts->image) {
            $image = json_decode($donation_posts->image);
            $image = array_map(function ($key, $value) {
                return ['id' => $key + 1, 'src' => url($value)];
            }, array_keys($image), $image);
        }

        return view('donations.donation_posts_add', compact('donation_posts', 'image', 'donation_categories', 'is_admin'));
    }

    public function update(Request $request, $id)
    {
        $user = auth()->user();
        if (\App\Models\User::isAccessByAdmin()) {
            $userId = $request->organizations;
        } else {
            $userId = User::getUserId($user);
        }

        if (User::isAccessByAdmin()) {
            $donation_posts = DonationPost::find($id);
        } else {
            $donation_posts = DonationPost::where('user_id', $userId)->where('id', $id)->first();
        }

        $request->validate([
            'title' => 'required',
            'donation_category_id' => 'required',
            'expired_date' => 'required|date',
            'description' => 'required|min:50',
        ]);

        $donation_posts->update([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'donation_category_id' => $request->input('donation_category_id'),
            'expired_date' => $request->input('expired_date'),
            'target' => (!empty($request->input('target'))) ? $request->input('target') : 0,
            'last_edited_by' => $user->id,
            'user_id' => $userId,
            'active' => $request->input('active') ?? 0,
        ]);

        $image = new ImageHelper();
        $images = [];
        if ($donation_posts->image && $request->old) {
            $updatedImgs = $request->old;
            $images = json_decode($donation_posts->image);
            $images = array_map(function ($key, $value) use ($updatedImgs) {
                if (in_array(($key + 1), $updatedImgs)) {
                    return $value;
                } else {
                    if (File::exists(public_path($value))) {
                        File::delete($value);
                    }
                }
            }, array_keys($images), $images);
        }

        $imagesArray = $image->imageUpload($request->images, $donation_posts->id, 'donation_post');
        if (!empty($imagesArray)) {
            $images = array_merge($images, $imagesArray);
        }

        $donation_posts->update([
            'image' => json_encode(array_values(array_filter($images)))
        ]);

        return redirect()->route('donation_posts.index')
            ->with('success', 'DonationPost updated successfully');
    }

    public function destroy($id)
    {
        $user = auth()->user();
        $userId = User::getUserId($user);

        if (User::isAccessByAdmin()) {
            $donation_posts = DonationPost::find($id);
        } else {
            $donation_posts = DonationPost::where('user_id', $userId)->where('id', $id)->first();
        }

        $payments = $donation_posts->payments;
        if (count($payments) != 0) {
            return redirect()->route('donation_posts.index')
                ->with('warning', 'Could not delete!, This post has payment/s already');
        }
        $donation_posts->delete();
        return redirect()->route('donation_posts.index')
            ->with('success', 'DonationPost deleted successfully');
    }
}
