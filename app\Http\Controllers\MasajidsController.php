<?php

namespace App\Http\Controllers;

use App\Mail\CustomOrgMail;
use App\Models\Country;
use App\Models\DirectoryType;
use App\Models\Facilities;
use App\Models\Amenities;
use App\Models\Masajids;
use App\Models\MasajidsPrograms;
use App\Models\Programs;
use App\Models\Setting;
use App\Models\TagsModel;
use App\Models\USAStates;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpFoundation\Response;
use Spatie\Permission\Models\Role;
use \App\Models\User;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class MasajidsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $types = DirectoryType::where('status', 1)->pluck('name', 'id')->toArray();
        $directories = DB::table('masajids')
            ->leftJoin('directory_types', function ($join) {
                $join->on('directory_types.id', '=', DB::raw("CAST(JSON_UNQUOTE(JSON_EXTRACT(masajids.directory_type, '$[0]')) AS UNSIGNED)"));
            })
            ->leftJoin('users', 'users.org_id', '=', 'masajids.id') // Keep left join to include directories without users
            ->select(
                'masajids.*',
                'directory_types.order as directory_type_order',
                DB::raw('COUNT(users.id) as user_count') // Count users, even if 0
            )
            ->whereRaw("JSON_CONTAINS(masajids.directory_type, '15', '$')")
            ->groupBy('masajids.id', 'directory_types.order', 'masajids.name') // Ensure groupBy includes unique columns
            ->orderBy('directory_types.order', 'asc')
            ->orderBy('masajids.order', 'asc')
            ->get()
            ->map(function ($directory) use ($types) {
                $directoryTypes = json_decode($directory->directory_type, true);
                if (!is_array($directoryTypes)) {
                    $directoryTypes = [];
                }
                $directory->directory_type_names = array_map(fn($id) => $types[$id] ?? $id, $directoryTypes);
                return $directory;
            });

        $i = 1;
        return view('admin.directory.Masajids.masajids.index', compact('directories', 'i', 'types'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_create_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $types = DirectoryType::where('status', 1)->get();
        $roles = Role::all();
        $tags = TagsModel::all();
        $whereWeWork = Country::all();
        $whatWeDo = MasajidsPrograms::all();
        $facilities = Facilities::all();
        $amenities = Amenities::all();
        $usaStates = USAStates::all();
        $directoryLists = Masajids::getDirectoryLists();

        return view('admin.directory.Masajids.masajids.create', compact('types', 'roles', 'directoryLists', 'tags', 'whereWeWork', 'whatWeDo', 'usaStates','amenities', 'facilities'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'bail|required',
            'images' => 'bail|required|array',
            'images.*' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            'business_name' => 'bail|required',
            'location' => 'bail|required',
            'directory_type' => 'bail|required|array',
            'tags' => 'bail|required',
            'status' => 'bail|required',
            'prime_status' => 'bail|required',
            'address_type' => 'bail|required',
            'address' => 'required|array|min:1',
            'address.*.street' => 'bail|required_if:address_type,offline|string|max:255',
            'address.*.lat' => 'bail|required_if:address_type,offline|numeric',
            'address.*.long' => 'bail|required_if:address_type,offline|numeric',
            'business_url' => 'bail|required_if:type,onli   ne',
            'contact_informations' => 'bail|required',
            'description' => 'bail|required',
            'personal_email' => 'bail|required|email',
            'join_as_organizer' => 'bail|required',
            'contact_informations.email' => 'bail|required',
            'contact_informations.website' => 'bail|required',
        ], [
            'personal_email.required' => 'The business email field is mandatory.',
            'personal_email.email' => 'Please provide a valid email address for business email.',
        ]);
        $user = Auth::user();
        if ($user->hasRole('Organizer')) {
            $websiteArray = parse_url($request->contact_informations['website']);
            $emailArray = explode('@', $request->personal_email);
            $domain = "";
            if (isset($websiteArray['host']) && !empty($websiteArray['host'])) {
                $domain = str_replace('www.', '', $websiteArray['host']);
            }
            if (
                (isset($emailArray[1]) && !empty($emailArray[1]) &&
                    $emailArray[1] != $domain) || (!isset($emailArray[1]) || empty($emailArray[1]))
            ) {
                return redirect()->back()->withInput()->with('error', 'Your business email doesnt matched with your website');
            }
        }
        $data = $request->all();
        $directoryTypes = $data['directory_type'];
        $data['directory_type'] = $request->has('directory_type') ? array_map('intval', $request->directory_type) : [0];
        if ($request->relatedDirectoryLists) {
            $data['related_directories'] = json_encode($request->relatedDirectoryLists, true);
        }
        if ($request->chaptersLists) {
            $data['chapters'] = json_encode($request->chaptersLists, true);
        }
        if ($request->where_we_work) {
            $data['where_we_work'] = json_encode($request->where_we_work, true);
        }
        if ($request->what_we_do) {
            $data['what_we_do'] = json_encode($request->what_we_do, true);
        }
        if ($request->facilities) {
            $data['facilities'] = json_encode($request->facilities, true);
        }
        if ($request->amenities) {
            $data['amenities'] = json_encode($request->amenities, true);
        }
        if ($request->tags) {
            $data['tags'] = json_encode($request->tags, true);
        }
        if ($request->has('prime_status')) {
            $data['prime_status'] = $request->input('prime_status');
        }
        if ($request->has('donate_and_support')) {
            $data['donate_and_support'] = $request->input('donate_and_support');
        }
        if ($request->has('external_link')) {
            $data['external_link'] = $request->input('external_link');
        }
        $data['gallery'] = [];
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        if ($request->hasFile('logo')) {
            $data['image'] = (new AppHelper)->saveImage($request->file('logo'), true);
        } else {
            $data['image'] = $this->getDirectoryPlaceHolder($request->name);
        }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['security'] = 1;
        $data['business_hours'] = json_encode($request->business_hours);
        $data['address'] = json_encode($request->address);
        $data['social_media'] = json_encode($request->social_media);
        $data['contact_informations'] = json_encode($request->contact_informations);
        $data['show_business_hours'] = $request->has('show_business_hours') ? 1 : 0;
        $data['created_by'] = $user->id;
        $data['work_region'] = $request->work_region;
        $directory = Masajids::create($data);
        if (!empty($directoryTypes)) {
            foreach ($directoryTypes as $directoryType) {
                $newLink = new \App\Models\DirectoryTypeLink();
                $newLink->directory_id = $directory->id;
                $newLink->type_id = $directoryType;
                $newLink->save();
            }
        }
        $message = 'Business has added successfully.';
        if ($user->hasRole('Organizer')) {
            $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                $q->whereIn("name", ["admin"]);
            })->pluck('email')->toArray();
            if (!empty($adminUsers)) {
                Mail::to($adminUsers)->send(new CustomOrgMail('admin', $directory, $user));
            }
            Mail::to($user->email)->send(new CustomOrgMail('organizer', $directory, $user));
            Mail::to($request->personal_email)->send(new CustomOrgMail('organization', $directory, $user));
            $user->status = 0;
            $user->save();
            Auth::logout();
            return redirect('user/login?type=org')->with('error_msg', 'Business has added successfully. Please wait for until review by the team.');
        }
        return redirect()->route('masajids.index')->withStatus(__($message));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Masajids $masajid)
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($masajid->directory_type && is_array($masajid->directory_type) == true) {
            $selectedTypes = $masajid->directory_type;
        } elseif (is_string($masajid->directory_type)) {
            $selectedTypes = [intval($masajid->directory_type)];
        } else {
            $selectedTypes = $masajid->directory_type ? json_decode($masajid->directory_type, true) : [];
        }
        $types = DirectoryType::where('status', 1)->get();

        $tags = TagsModel::all();
        $selectedTags = json_decode($masajid->tags, true);
        $selectedTags = is_array($selectedTags)  ? $selectedTags : [];

        $usaStates = USAStates::all();
        $whereWeWork = Country::all();
        $selectedCountries = json_decode($masajid->where_we_work, true);
        $selectedCountries = is_array($selectedCountries) ? $selectedCountries : [];

        $workRegion = $masajid->work_region ?? 'worldwide';

        $whatWeDo = MasajidsPrograms::all();
        $selectedPrograms = json_decode($masajid->what_we_do, true);
        $selectedPrograms = is_array($selectedPrograms) ? $selectedPrograms : [];

        $facilities = Facilities::all();
        $selectedFacilities = json_decode($masajid->facilities, true);
        $selectedFacilities = is_array($selectedFacilities) ? $selectedFacilities : [];

        $amenities = Amenities::all();
        $selectedAmenities = json_decode($masajid->amenities, true);
        $selectedAmenities = is_array($selectedAmenities) ? $selectedAmenities : [];

        $business_hours = json_decode($masajid->business_hours, true);
        if (!empty($business_hours)) {
            foreach ($business_hours as $day => $business_hour) {
                $business_hours[$day]['is_closed'] = $business_hour['is_closed'] ?? "";
                $business_hours[$day]['open_time'] = $business_hour['open_time'] ?? "";
                $business_hours[$day]['close_time'] = $business_hour['close_time'] ?? "";
            }
        }
        $social_media = json_decode($masajid->social_media, true) ?? [];
        $required_keys = ['youtube', 'linkedin', 'tiktok'];
        foreach ($required_keys as $key) {
            if (!array_key_exists($key, $social_media)) {
                $social_media[$key] = null;
            }
        }

        $contact_info = json_decode($masajid->contact_informations, true);

        $roles = Role::all();
        $users = User::with("roles")->whereHas("roles", function ($q) use ($masajid) {
            $q->where("id", [$masajid->organizer_role]);
        })->get();

        $gallery = [];
        $galleryImages = [];
        if (!empty($masajid->gallery)) {
            $galleryImages = explode(',', $masajid->gallery);
        }
        if (!empty($galleryImages)) {
            foreach ($galleryImages as $key => $galleryImage) {
                array_push($gallery, ['id' => $key, 'src' => url('images/upload/' . $galleryImage)]);
            }
        }
        $directoryLists = Masajids::getDirectoryLists();
        $selectedDirectories = Masajids::returnRelatedDirectoriesAndFormatting($masajid->id);
        $selectedChapters = Masajids::returnChaptersAndFormatting($masajid->id);
        return view('admin.directory.Masajids.masajids.edit', compact('gallery', 'selectedTypes', 'types', 'users', 'masajid', 'business_hours', 'social_media', 'contact_info', 'roles', 'tags', 'selectedTags', 'directoryLists', 'selectedDirectories', 'selectedChapters', 'usaStates', 'whereWeWork', 'selectedCountries', 'workRegion', 'whatWeDo', 'selectedPrograms', 'facilities', 'selectedFacilities', 'amenities', 'selectedAmenities'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Masajids $masajid)
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        $this->validate($request, [
            'name' => 'bail|required',
            'business_name' => 'bail|required',
            'location' => 'bail|required',
            'directory_type' => 'bail|required|array',
            'status' => 'bail|required',
            'prime_status' => 'bail|required',
            'address_type' => 'bail|required',
            'address' => 'bail|required_if:address_type,offline|array|min:1',
            'address.*.street' => 'bail|required_if:address_type,offline|string|max:255',
            'address.*.lat' => 'bail|required_if:address_type,offline|numeric',
            'address.*.long' => 'bail|required_if:address_type,offline|numeric',
            'contact_informations' => 'bail|required',
            'description' => 'bail|required',
            'personal_email' => 'bail|required|email', // Corrected rule format
            'join_as_organizer' => 'bail|required',
            'contact_informations.email' => 'bail|required',
            'contact_informations.website' => 'bail|required',
        ], [
            'personal_email.required' => 'The business email field is mandatory.', // Custom messages
            'personal_email.email' => 'Please provide a valid email address for business email.',
        ]);
        $data = $request->all();

        if ($request->relatedDirectoryLists) {
            $data['related_directories'] = json_encode($request->relatedDirectoryLists);
        }
        if ($request->chaptersLists) {
            $data['chapters'] = json_encode($request->chaptersLists);
        }

        $data['work_region'] = $request->work_region;
        if ($request->where_we_work) {
            $data['where_we_work'] = json_encode(array_values($request->where_we_work));
        } else {
            $data['where_we_work'] = json_encode([]);
        }
        if ($request->what_we_do) {
            $data['what_we_do'] = json_encode(array_values($request->what_we_do));
        } else {
            $data['what_we_do'] = json_encode([]);
        }
        if ($request->facilities) {
            $data['facilities'] = json_encode($request->facilities, true);
        } else {
            $data['facilities'] = json_encode([]);
        }
        if ($request->amenities) {
            $data['amenities'] = json_encode($request->amenities, true);
        } else {
            $data['amenities'] = json_encode([]);
        }
        if ($request->tags) {
            $data['tags'] = json_encode(array_values($request->tags));
        } else {
            $data['tags'] = json_encode([]);
        }
        if ($request->has('prime_status')) {
            $data['prime_status'] = $request->input('prime_status');
        }
        if ($request->has('donate_and_support')) {
            $data['donate_and_support'] = $request->input('donate_and_support');
        }
        if ($request->has('external_link')) {
            $data['external_link'] = $request->input('external_link');
        }
        $directoryTypes = $data['directory_type'];
        if ($request->hasFile('logo')) {
            (new AppHelper)->deleteFile($masajid->image);
            $data['image'] = (new AppHelper)->saveImage($request->file('logo'), true);
        }
        $gallerys = [];
        $gallerys = array_merge($gallerys, (!empty($masajid->gallery)) ? explode(',', $masajid->gallery) : []);
        $constructedGallery = [];
        if ($request->has('preloaded')) {
            foreach ($gallerys as $key => $gallery) {
                if (in_array($key, $request->preloaded) !== false) {
                    array_push($constructedGallery, $gallery);
                } else {
                    (new AppHelper)->deleteFile($gallery);
                }
            }
        }
        $data['gallery'] = $constructedGallery;
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['address'] = json_encode($request->address);
        $data['business_hours'] = json_encode($request->business_hours);
        $data['social_media'] = json_encode($request->social_media);
        $data['contact_informations'] = json_encode($request->contact_informations);
        $data['show_business_hours'] = $request->has('show_business_hours') ? 1 : 0;
        $data['directory_type'] = $request->has('directory_type') ? array_map('intval', $request->directory_type) : [0];

        $masajid->update($data);
        if (!empty($directoryTypes)) {
            \App\Models\DirectoryTypeLink::where('directory_id', $masajid->id)->delete();
            foreach ($directoryTypes as $directoryType) {
                $newLink = new \App\Models\DirectoryTypeLink();
                $newLink->directory_id = $masajid->id;
                $newLink->type_id = $directoryType;
                $newLink->save();
            }
        }
        return redirect()->route('masajids.index')->withStatus(__('Business has updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        Masajids::find($id)->delete();
        \App\Models\DirectoryTypeLink::where('directory_id', $id)->delete();
        return true;
    }

    // **********Index-Order**********
    public function orderMasajidsProcess(Request $request)
    {
        $this->validate($request, [
            'positions' => 'required|array',
        ]);
        $positions = $request->positions;
        foreach ($positions as $position) {
            if (isset($position['id']) && isset($position['position'])) {
                Masajids::where('id', $position['id'])->update(['order' => $position['position']]);
            }
        }
    }
    // **********Index-Order**********

    // ******Show-Masajids-Facilities******
    public function showFacilities()
    {
        try {
            $allFacilities = Facilities::all();
            return view('admin.directory.Masajids.facilities.index', compact('allFacilities'));
        } catch (Exception $e) {
            Log::error('Error fetching facilities: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load facilities. Please try again later.');
        }
    }
    // ******Show-Masajids-Facilities******

    // ******Create-Masajids-Facilities******
    public function createFacilities()
    {
        try {
            return view('admin.directory.Masajids.facilities.add');
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load facilities. Please try again later.');
        }
    }
    // ******Create-Masajids-Facilities******

    // ******Store-Masajids-Facilities******
    public function storeFacilities(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string',
                'description' => 'nullable|string',
                'status' => 'required|integer|in:1,0'
            ]);
            Facilities::create([
                'name' => $request->name,
                'description' => $request->description,
                'status' => $request->status,
            ]);
            return redirect()->route('masajids-facilities')->with('success', 'Facilities added successfully.');
        } catch (Exception $e) {
            Log::error('Error to store facilities: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to store facilities. Please try again later.');
        }
    }
    // ******Store-Masajids-Facilities******

    // ******Edit-Masajids-Facilities******
    public function editFacilities($id)
    {
        try {
            $facilities = Facilities::findOrFail($id);
            return view('admin.directory.Masajids.facilities.edit', compact('facilities'));
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load facilities. Please try again later.');
        }
    }
    // ******Edit-Masajids-Facilities******

    // ******Update-Masajids-Facilities******
    public function updateFacilities(Request $request, $id)
    {
        try {
            $request->validate([
                'name' => 'required|string|unique:masajids_facilities,name,' . $id,
                'description' => 'nullable|string',
                'status' => 'required|integer|in:1,0'
            ]);
            $facilities = Facilities::findOrFail($id);
            $facilities->update([
                'name' => $request->name,
                'description' => $request->description,
                'status' => $request->status,
            ]);
            return redirect()->route('masajids-facilities')->with('success', 'Facilities updated successfully.');
        } catch (Exception $e) {
            Log::error('Error updating facilities: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update facilities. Please try again later.');
        }
    }
    // ******Update-Masajids-Facilities******

    // ******Delete-Masajids-Facilities******
    public function deleteFacilities($id)
    {
        try {
            $facilities = Facilities::findOrFail($id);
            $facilities->delete();
            return response()->json(['success' => true, 'message' => 'Facilities deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting facilities: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to delete facilities. Please try again later.'], 500);
        }
    }
    // ******Delete-Masajids-Facilities******

    // ******Show-Masajids-Amenities******
    public function showAmenities()
    {
        try {
            $allAmenities = Amenities::all();
            return view('admin.directory.Masajids.amenities.index', compact('allAmenities'));
        } catch (Exception $e) {
            Log::error('Error fetching amenities: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load amenities. Please try again later.');
        }
    }
    // ******Show-Masajids-Amenities******

    // ******Create-Masajids-Amenities******
    public function createAmenities()
    {
        try {
            return view('admin.directory.Masajids.amenities.add');
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load amenities. Please try again later.');
        }
    }
    // ******Create-Masajids-Amenities******

    // ******Store-Masajids-Amenities******
    public function storeAmenities(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string',
                'description' => 'nullable|string',
                'status' => 'required|integer|in:1,0'
            ]);
            Amenities::create([
                'name' => $request->name,
                'description' => $request->description,
                'status' => $request->status,
            ]);
            return redirect()->route('masajids-amenities')->with('success', 'Amenities added successfully.');
        } catch (Exception $e) {
            Log::error('Error to store amenities: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to store amenities. Please try again later.');
        }
    }
    // ******Store-Masajids-Amenities******

    // ******Edit-Masajids-Amenities******
    public function editAmenities($id)
    {
        try {
            $amenities = Amenities::findOrFail($id);
            return view('admin.directory.Masajids.amenities.edit', compact('amenities'));
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load amenities. Please try again later.');
        }
    }
    // ******Edit-Masajids-Amenities******

    // ******Update-Masajids-Amenities******
    public function updateAmenities(Request $request, $id)
    {
        try {
            $request->validate([
                'name' => 'required|string|unique:masajids_facilities,name,' . $id,
                'description' => 'nullable|string',
                'status' => 'required|integer|in:1,0'
            ]);
            $amenities = Amenities::findOrFail($id);
            $amenities->update([
                'name' => $request->name,
                'description' => $request->description,
                'status' => $request->status,
            ]);
            return redirect()->route('masajids-amenities')->with('success', 'Amenities updated successfully.');
        } catch (Exception $e) {
            Log::error('Error updating amenities: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update amenities. Please try again later.');
        }
    }
    // ******Update-Masajids-Amenities******

    // ******Delete-Masajids-Amenities******
    public function deleteAmenities($id)
    {
        try {
            $amenities = Amenities::findOrFail($id);
            $amenities->delete();
            return response()->json(['success' => true, 'message' => 'Amenities deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting amenities: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to delete amenities. Please try again later.'], 500);
        }
    }
    // ******Delete-Masajids-Amenities******

    // ******Show-Masajids-Programs******
    public function showPrograms()
    {
        try {
            $allPrograms = MasajidsPrograms::all();
            return view('admin.directory.Masajids.programs.index', compact('allPrograms'));
        } catch (Exception $e) {
            Log::error('Error fetching programs: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load programs. Please try again later.');
        }
    }
    // ******Show-Masajids-Programs******

    // ******Create-Masajids-Programs******
    public function createPrograms()
    {
        try {
            return view('admin.directory.Masajids.programs.add');
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load programs. Please try again later.');
        }
    }
    // ******Create-Masajids-Programs******

    // ******Store-Masajids-Programs******
    public function storePrograms(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string',
                'status' => 'required|integer|in:1,0'
            ]);
            MasajidsPrograms::create([
                'name' => $request->name,
                'status' => $request->status,
            ]);
            return redirect()->route('masajids-programs')->with('success', 'Programs added successfully.');
        } catch (Exception $e) {
            Log::error('Error to store programs: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to store programs. Please try again later.');
        }
    }
    // ******Store-Masajids-Programs******

    // ******Edit-Masajids-Programs******
    public function editPrograms($id)
    {
        try {
            $programs = MasajidsPrograms::findOrFail($id);
            return view('admin.directory.Masajids.programs.edit', compact('programs'));
        } catch (Exception $e) {
            Log::error('Error to load: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load programs. Please try again later.');
        }
    }
    // ******Edit-Masajids-Programs******

    // ******Update-Masajids-Programs******
    public function updatePrograms(Request $request, $id)
    {
        try {
            $request->validate([
                'name' => 'required|string|unique:programs,name,' . $id,
                'status' => 'required|integer|in:1,0'
            ]);
            $programs = MasajidsPrograms::findOrFail($id);
            $programs->update([
                'name' => $request->name,
                'status' => $request->status,
            ]);
            return redirect()->route('masajids-programs')->with('success', 'Programs updated successfully.');
        } catch (Exception $e) {
            Log::error('Error updating programs: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update programs. Please try again later.');
        }
    }
    // ******Update-Masajids-Programs******

    // ******Delete-Masajids-Programs******
    public function deletePrograms($id)
    {
        try {
            $programs = MasajidsPrograms::findOrFail($id);
            $programs->delete();
            return response()->json(['success' => true, 'message' => 'Programs deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting programs: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to delete programs. Please try again later.'], 500);
        }
    }
    // ******Delete-Masajids-Programs******

    // ******Export-Masajids-Locations******
    public function showExport()
    {
        try{
            $existingPlaces = Masajids::pluck('name')->map(fn($name) => strtolower($name))->toArray();
            $gmapkey = Setting::find(1)->map_key;
            return view('admin.directory.Masajids.export.export', compact('gmapkey', 'existingPlaces'));
        } catch (Exception $e){
            Log::error('Error to show index: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to show export index.');
        }
    }
    // ******Export-Masajids-Locations******

    // ******Export-Masajids-Locations-In-DB******
    public function exportMasajids(Request $request)
    {
        try {
            $masajidsData = $request->input('masajids');
            if (!is_array($masajidsData)) {
                return response()->json(['success' => false, 'message' => 'Invalid data format.'], 400);
            }

            $savedMasajids = [];
            foreach ($masajidsData as $masajid) {
                $validatedData = Validator::make($masajid, [
                    'name' => 'nullable|string',
                    'address' => 'nullable|string',
                    'lat' => 'nullable|numeric',
                    'lng' => 'nullable|numeric',
                    'photo' => 'nullable',
                    'description' => 'nullable|string',
                    'phone' => 'nullable|string',
                    'website' => 'nullable|string',
                    'review' => 'nullable|string',
                ]);

                if ($validatedData->fails()) {
                    continue;
                }

                $reviews = [];
                if (!empty($masajid['reviews']) && is_array($masajid['reviews'])) {
                    foreach ($masajid['reviews'] as $review) {
                        $filteredReview = [
                            'author_name' => $review['author_name'] ?? null,
                            'rating' => $review['rating'] ?? null,
                            'relative_time_description' => $review['relative_time_description'] ?? null,
                            'text' => $review['text'] ?? null,
                            'profile_photo_url' => $review['profile_photo_url'] ?? null,
                        ];
                        $reviews[] = $filteredReview;
                    }
                }

                $imageName = null;
                $photoUrl = $masajid['photo'] ?? null;
                if (!empty($photoUrl) && filter_var($photoUrl, FILTER_VALIDATE_URL)) {
                    try {
                        $tempImage = tempnam(sys_get_temp_dir(), 'masajid_img');
                        $imageContents = file_get_contents($photoUrl);
                        file_put_contents($tempImage, $imageContents);

                        $finfo = finfo_open(FILEINFO_MIME_TYPE);
                        $mimeType = finfo_file($finfo, $tempImage);
                        finfo_close($finfo);

                        $extensionMap = ['image/jpeg' => 'jpg', 'image/png' => 'png', 'image/jpg' => 'jpg'];
                        $extension = $extensionMap[$mimeType] ?? 'jpg';

                        $uploadedFile = new \Illuminate\Http\UploadedFile($tempImage, uniqid() . '.' . $extension, $mimeType, null, true);
                        $imageName = (new AppHelper)->saveImage($uploadedFile, true);
                    } catch (Exception $e) {
                        Log::error('Failed to download or save photo: ' . $e->getMessage());
                        return response()->json(['success' => false, 'message' => 'Failed to store masajid image. Please try again.'], 500);
                    }
                }

                $data = [
                    'name' => $masajid['name'],
                    'location' => $masajid['address'],
                    'status' => 1,
                    'address_type' => 'offline',
                    'address' => json_encode([["title" => null, "street" => $masajid['address'], "state" => null, "lat" => $masajid['lat'], "long" => $masajid['lng']]]),
                    'directory_type' => [15],
                    'description' => $masajid['description'],
                    'contact_informations' => json_encode(['phone' => $masajid['phone'], 'email' => null, 'website' => $masajid['website']]),
                    'image' => $imageName,
                    'reviews' => json_encode($reviews),
                ];

                $existing = Masajids::where('name', $masajid['name'])->first();
                if ($existing) {
                    $existing->update($data);
                    $savedMasajids[] = $existing;
                } else {
                    $savedMasajids[] = Masajids::create($data);
                }
            }
            return response()->json(['success' => true, 'message' => 'Masajids stored/updated successfully.', 'data' => $savedMasajids], 200);
        } catch (\Exception $e) {
            Log::error('Error storing masajids: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to store masajids. Please try again.'], 500);
        }
    }
    // ******Export-Masajids-Locations-In-DB******
}
