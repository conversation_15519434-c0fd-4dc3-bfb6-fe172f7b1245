<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Leaders;
use App\Models\LeadersCategory;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use <PERSON>wi<PERSON>\TwiML\Voice\Leave;

class LeadersController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // if (!Auth::user()->hasRole('admin')) {
        //     abort(403, 'Invalid Access');
        // }
        try {
            $leaders = Leaders::orderBy('id', 'desc')->get();
            return view('admin.leaders.leaders-list', compact('leaders'));
        } catch (Exception $e) {
            Log::error('Error fetching leaders list: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => 'Failed to load leaders list. Please try again later.'], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try{
            $categories = LeadersCategory::all();
            $users = User::all();
            return view('admin.leaders.leaders-create', compact('categories', 'users'));
        }
        catch(Exception $e){
            Log::error('Error fetching leaders list: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => 'Failed to load leaders list. Please try again later.'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name'          => 'required|string|max:255',
            'user_id'       => 'required|array',
            'user_id.*'     => 'exists:users,id',
            'category'      => 'required|exists:leaders_categories,category',
            'email'         => 'nullable|email|max:255|unique:leaders,email',
            'bio'           => 'nullable|string',
            'contact'       => 'nullable|digits:10',
            'social_media_links' => 'nullable|array',
        ]);

        try {
            $socialLinks = array_filter($request->social_media_links); // Remove empty fields
            Leaders::create([
                'name'                => $request->name,
                'user_id'             => json_encode($request->user_id),
                'category'            => $request->category,
                'bio'                 => $request->bio,
                'email'               => $request->email,
                'contact'             => $request->contact,
                'social_media_links'  => json_encode($socialLinks),
            ]);

            return redirect()->route('leaders.index')->with('success', 'Leader created successfully.');
        } catch (\Exception $e) {
            Log::error('Error creating leader: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to create leader. Please try again.');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try{
            $leader = Leaders::find($id);
            $categories = LeadersCategory::all();
            $users = User::all();
            return view('admin.leaders.leaders-edit', compact('leader', 'categories', 'users'));
        }
        catch(Exception $e){
            Log::error('Error fetching leaders list: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => 'Failed to load leaders list. Please try again later.'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name'          => 'required|string|max:255',
            'user_id'       => 'required|array',
            'user_id.*'     => 'exists:users,id',
            'category'      => 'required|exists:leaders_categories,category',
            'email'         => 'nullable|email',
            'bio'           => 'nullable|string',
            'contact'       => 'nullable|digits:10',
            'social_media_links'           => 'nullable|array',
            'social_media_links.facebook'  => 'nullable|url',
            'social_media_links.instagram' => 'nullable|url',
            'social_media_links.twitter'   => 'nullable|url',
            'social_media_links.x'         => 'nullable|url',
            'social_media_links.youtube'   => 'nullable|url',
            'social_media_links.tiktok'    => 'nullable|url',
            'social_media_links.website'   => 'nullable|url',
        ]);

        try {
            // Remove empty social links (null or empty strings)
            $socialLinks = array_filter($request->social_media_links ?? []);

            $leader = Leaders::findOrFail($id);
            $leader->update([
                'name'          => $request->name,
                'user_id'       => json_encode($request->user_id), // Store as JSON
                'category'      => $request->category,
                'bio'           => $request->bio,
                'email'         => $request->email,
                'contact'       => $request->contact,
                'social_media_links'  => json_encode($socialLinks),
            ]);
            return redirect()->route('leaders.index')->with('success', 'Leader updated successfully.');
        } catch(Exception $e) {
            Log::error('Error updating leader: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update leader. Please try again later.');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // if (!Auth::user()->hasRole('admin')) {
        //     return response()->json(['error' => 'Invalid Access'], 403);
        // }
        $leaders = Leaders::find($id);
        if (!$leaders) {
            return response()->json(['error' => 'Prayer time not found'], 404);
        }
        try {
            $leaders->delete();
            return response()->json(['success' => 'Leaders deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting leaders: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete leaders. Please try again.'], 500);
        }
    }

    // ***********Leaders-Category-List***********
    public function categoryIndex()
    {
        try {
            $leadersCategory = LeadersCategory::withCount('leaders')->get();
            return view('admin.leaders.leaders-category-list', compact('leadersCategory'));
        } catch (Exception $e) {
            Log::error('Error fetching leaders category list: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => 'Failed to load leaders category list. Please try again later.'], 500);
        }
    }
    // ***********Leaders-Category-List***********

    // ***********Leaders-Category-Create***********
    public function categoryCreate()
    {
        try {
            return view('admin.leaders.leaders-category-create');
        } catch (Exception $e) {
            Log::error('Error fetching leaders category list: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => 'Failed to load leaders category list. Please try again later.'], 500);
        }
    }
    // ***********Leaders-Category-Create***********

    // ***********Leaders-Category-Store***********
    public function categoryStore(Request $request)
    {
        $request->validate([
            'category' => 'required|string|max:255',
            'status' => 'required|in:0,1',
        ]);

        try {
            LeadersCategory::create([
                'category' => $request->category,
                'status' => $request->status,
            ]);
            return redirect()->route('leadersCategory.index')->with('success', 'Leaders category created successfully.');
        } catch (Exception $e) {
            Log::error('Error creating leaders category: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to create leaders category. Please try again later.');
        }
    }
    // ***********Leaders-Category-Store***********

    // ***********Leaders-Category-Edit***********
    public function categoryEdit($id)
    {
        try {
            $leadersCategory = LeadersCategory::find($id);
            return view('admin.leaders.leaders-category-edit', compact('leadersCategory'));
        } catch (Exception $e) {
            Log::error('Error fetching leaders category: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => 'Failed to load leaders category. Please try again later.'], 500);
        }
    }
    // ***********Leaders-Category-Edit***********

    // ***********Leaders-Category-Update***********
    public function categoryUpdate(Request $request, $id)
    {
        $request->validate([
            'category' => 'required|string|max:255',
            'status' => 'required|in:0,1',
        ]);

        try {
            $leadersCategory = LeadersCategory::findOrFail($id);
            $leadersCategory->update([
                'category' => $request->category,
                'status' => $request->status,
            ]);
            return redirect()->route('leadersCategory.index')->with('success', 'Leaders category updated successfully.');
        } catch (Exception $e) {
            Log::error('Error updating leaders category: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update leaders category. Please try again later.');
        }
    }
    // ***********Leaders-Category-Update***********

    // ***********Leaders-Category-Destroy***********
    public function categoryDestroy($id)
    {
        // if (!Auth::user()->hasRole('admin')) {
        //     return response()->json(['error' => 'Invalid Access'], 403);
        // }
        $leadersCategory = LeadersCategory::find($id);
        if (!$leadersCategory) {
            return response()->json(['error' => 'Leaders category not found'], 404);
        }
        try {
            $leadersCategory->delete();
            return response()->json(['success' => 'Leaders category deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting leaders category: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete leaders category. Please try again.'], 500);
        }
    }
    // ***********Leaders-Category-Destroy***********
    public function leadersIndex(Request $request, $id, $name)
    {
        try {
            $event = Event::findOrFail($id);
            $categories = LeadersCategory::all();
            $selectedCategory = $request->query('category'); 
            $leaders = [];

            $storedLeaderIds = $event->speaker_id ? json_decode($event->speaker_id, true) : [];

            if ($storedLeaderIds && count($storedLeaderIds) > 0) {
                $leaders = Leaders::whereIn('id', $storedLeaderIds)
                    ->where('status', 1)
                    ->get();
                
                if (!$selectedCategory && $leaders->count() > 0) {
                    $selectedCategory = $leaders->first()->category;
                }
            }
            if ($selectedCategory) {
                $leaders = Leaders::where('category', $selectedCategory)
                    ->where('status', 1)
                    ->get();
            }

            $leadersCategory = LeadersCategory::withCount('leaders')->get();
            return view('admin.leaders.manage-leaders-list', compact(
                'event', 'leadersCategory', 'categories', 'leaders', 'selectedCategory', 'storedLeaderIds'
            ));
        } catch (Exception $e) {
            Log::error('Error fetching leaders or categories: ' . $e->getMessage());
            return response()->view('errors.500', ['message' => 'Failed to load leaders or categories. Please try again later.'], 500);
        }
    }

    public function getLeaders($category)
        {
            try {
                $leaders = Leaders::where('category', $category)
                    ->where('status', 1)
                    ->get();
                return response()->json(['success' => true, 'leaders' => $leaders]);
            } catch (Exception $e) {
                Log::error('Error fetching leaders: ' . $e->getMessage());
                return response()->json(['success' => false, 'message' => 'Failed to fetch leaders.']);
            }
        }

    public function storeEvent(Request $request)
    {
        $request->validate([
            'event_id' => 'required|exists:events,id', 
            'leader_ids' => 'required|array',        
            'leader_ids.*' => 'exists:leaders,id',   
        ]);

        try {
            $event = Event::findOrFail($request->event_id);

            $event->update([
                'speaker_id' => json_encode($request->leader_ids),
            ]);

            return response()->json(['success' => true, 'message' => __('Speaker Added successfully.')]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => __('Failed to update event.')]);
        }
    }





}
