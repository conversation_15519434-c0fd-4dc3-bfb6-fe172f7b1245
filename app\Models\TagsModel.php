<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class TagsModel extends Model
{
    use HasFactory;

    protected $table = 'tags';
    protected $fillable = [
        'tag_name',
        'status',
    ];

    public static function getTagsWithCountOfDirectory()
    {
        return DB::table('tags')
            ->select('tags.*', DB::raw('COUNT(directories.id) as directory_count'))
            ->leftJoin('directories', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(directories.tags, JSON_QUOTE(tags.tag_name))"), DB::raw('1'))
                    ->where('directories.status', '=', 1);
            })
            ->groupBy('tags.id', 'tags.tag_name', 'tags.status')
            ->orderBy('tags.tag_name', 'asc')
            ->get();
    }

    public static function getTagsWithCountOfMasajids()
    {
        return DB::table('tags')
            ->select('tags.*', DB::raw('COUNT(masajids.id) as directory_count'))
            ->leftJoin('masajids', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(masajids.tags, JSON_QUOTE(tags.tag_name))"), DB::raw('1'))
                    ->where('masajids.status', '=', 1);
            })
            ->groupBy('tags.id', 'tags.tag_name', 'tags.status')
            ->orderBy('tags.tag_name', 'asc')
            ->get();
    }

    public static function getTagsWithCountOfSchools()
    {
        return DB::table('tags')
            ->select('tags.*', DB::raw('COUNT(schools.id) as schools_count'))
            ->leftJoin('schools', function ($join) {
                $join->on(DB::raw("JSON_CONTAINS(schools.tags, JSON_QUOTE(tags.tag_name))"), DB::raw('1'))
                    ->where('schools.status', '=', 1);
            })
            ->groupBy('tags.id', 'tags.tag_name', 'tags.status')
            ->orderBy('tags.tag_name', 'asc')
            ->get();
    }
}
