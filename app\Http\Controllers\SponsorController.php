<?php

namespace App\Http\Controllers;

use App\Models\Sponsors;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SponsorController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        try{
            $sponsors = Sponsors::orderBy('order', 'asc')->get();
            return view('admin.sponsors.index', compact('sponsors'));
        } catch (Exception $e){
            Log::error('Error fetching sponsors: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to fetch sponsors. Please try again later.');
        }
    }

    /**
     * Manage the order of index.
     */
    public function orderSponsersProcess(Request $request)
    {
        $this->validate($request, [
            'positions' => 'required|array',
        ]);
        $positions = $request->positions;
        foreach ($positions as $position) {
            if (isset($position['id']) && isset($position['position'])) {
                Sponsors::where('id', $position['id'])->update(['order' => $position['position']]);
            }
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try{
            return view('admin.sponsors.create');
        } catch (Exception $e){
            Log::error('Error to load create sponsors: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load create sponsors. Please try again later.');
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg',
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:255',
            'status' => 'required|boolean',
        ]);

        try {
            if ($request->hasFile('image')) {
                $image = (new AppHelper)->saveImage($request->file('image'), true);
            } else {
                $image = $this->getDirectoryPlaceHolder($request->name);
            }

            Sponsors::create([
                'image' => $image,
                'name' => $request->name,
                'url' => $request->url,
                'status' => $request->status,
            ]);

            return redirect()->route('sponsors.index')->with('success', 'Sponsor created successfully.');
        } catch (\Exception $e) {
            Log::error('Error creating sponsor: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to create sponsor. Please try again later.');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try{
            $sponsors = Sponsors::findOrFail($id);
            return view('admin.sponsors.edit', compact('sponsors'));
        } catch (Exception $e){
            Log::error('Error to load edit sponsors: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load edit sponsors. Please try again later.');
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'image' => 'nullable|image|mimes:jpeg,png,jpg',
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:255',
            'status' => 'required|boolean',
        ]);

        try {
            $sponsor = Sponsors::findOrFail($id);
            $existingImage = $sponsor->image;

            if ($request->hasFile('image')) {
                $image = (new AppHelper)->saveImage($request->file('image'), true);
                $oldImagePath = public_path('/images/upload/' . $existingImage);
                if ($existingImage && file_exists($oldImagePath)) {
                    unlink($oldImagePath);
                }
                $sponsor->image = $image;
            }
            $sponsor->name = $request->name;
            $sponsor->url = $request->url;
            $sponsor->status = $request->status;
            $sponsor->save();

            return redirect()->route('sponsors.index')->with('success', 'Sponsor updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating sponsor: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update sponsor. Please try again later.');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $sponsor = Sponsors::findOrFail($id);
            if ($sponsor->image) {
                $imagePath = public_path('/images/upload/' . $sponsor->image);
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
            $sponsor->delete();
            return response()->json(['status' => 'success', 'message' => 'Sponsor deleted successfully.']);
        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('Foreign key constraint error: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => 'This record is connected with other data and cannot be deleted.'], 400);
        } catch (\Exception $e) {
            Log::error('Error deleting sponsor: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => 'Failed to delete sponsor. Please try again later.'], 500);
        }
    }
}
