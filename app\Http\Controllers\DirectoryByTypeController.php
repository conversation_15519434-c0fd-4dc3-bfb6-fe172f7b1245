<?php

namespace App\Http\Controllers;

use App\Mail\CustomOrgApproveMail;
use App\Mail\CustomOrgMail;
use App\Mail\JoinAsOrganizerSuccessMail;
use App\Models\DirectoryType;
use App\Models\Directory;
use App\Models\DirectoryTypeLink;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpFoundation\Response;
use Spatie\Permission\Models\Role;
use \App\Models\User;
use Illuminate\Support\Facades\DB;

class DirectoryByTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (!\Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $types = DirectoryType::where('status', 1)->pluck('name', 'id')->toArray();
        
        // $directories = DB::table('directories')
        //     ->leftJoin('users', 'users.org_id', '=', 'directories.id')
        //     ->select('directories.id', 'directories.name', 'directories.business_name', 'directories.image', 'directories.tags', 'directories.status', 'directories.address_type', 'directories.order', 'directories.directory_type', DB::raw('COUNT(users.id) as user_count'))
        //     ->whereJsonContains('directories.directory_type', intval($request->type))
        //     ->orderBy('directories.order', 'asc')
        //     ->get()
        //     ->map(function ($directory) use ($types) {
        //         $directoryTypes = json_decode($directory->directory_type, true);
        //         if (!is_array($directoryTypes)) {
        //             $directoryTypes = [];
        //         }
        //         $directory->directory_type_names = array_map(fn($id) => $types[$id] ?? $id, $directoryTypes);
        //         return $directory;
        //     });

        $directories = DB::table('directories')
            ->leftJoin('directory_types', function ($join) {
                $join->on('directory_types.id', '=', DB::raw("CAST(JSON_UNQUOTE(JSON_EXTRACT(directories.directory_type, '$[0]')) AS UNSIGNED)"));
            })
            ->leftJoin('users', 'users.org_id', '=', 'directories.id') // Keep left join to include directories without users
            ->select(
                'directories.*',
                'directory_types.order as directory_type_order',
                DB::raw('COUNT(users.id) as user_count') // Count users, even if 0
            )
            ->whereJsonContains('directories.directory_type', intval($request->type))
            ->groupBy('directories.id', 'directory_types.order', 'directories.name') // Ensure groupBy includes unique columns
            ->orderBy('directory_types.order', 'asc')
            ->orderBy('directories.order', 'asc')
            ->get()
            ->map(function ($directory) use ($types) {
                $directoryTypes = json_decode($directory->directory_type, true);
                if (!is_array($directoryTypes)) {
                    $directoryTypes = [];
                }
                $directory->directory_type_names = array_map(fn($id) => $types[$id] ?? $id, $directoryTypes);
                return $directory;
            });
        $i = 1;
        return view('admin.directory.index', compact('directories', 'i', 'types'));
    }
}
