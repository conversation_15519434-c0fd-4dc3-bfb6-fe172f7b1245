<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReferIncomeTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'amount',
        'user_id',
        'status',
        'note',
        'reason',
        'rejected_by',
        'approved_by',
    ];

    public function organization()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function RejectedBy(){
        return $this->belongsTo(User::class,'rejected_by','id');
    }

    public function approvedBy(){
        return $this->belongsTo(User::class,'approved_by','id');
    }
}
