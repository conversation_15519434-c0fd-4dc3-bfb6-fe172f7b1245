<?php

namespace App\Http\Controllers;

use App\Mail\CustomOrgMail;
use App\Models\ClassesCategory;
use App\Models\Country;
use App\Models\DirectoryType;
use App\Models\Directory;
use App\Models\DirectoryTypeLink;
use App\Models\Programs;
use App\Models\Schools;
use App\Models\SchoolsClass;
use App\Models\SchoolsClasses;
use App\Models\TagsModel;
use App\Models\USAStates;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpFoundation\Response;
use Spatie\Permission\Models\Role;
use \App\Models\User;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SchoolsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $types = DirectoryType::where('status', 1)->pluck('name', 'id')->toArray();
        $directories = DB::table('schools')
            ->leftJoin('directory_types', function ($join) {
                $join->on('directory_types.id', '=', DB::raw("CAST(JSON_UNQUOTE(JSON_EXTRACT(schools.directory_type, '$[0]')) AS UNSIGNED)"));
            })
            ->leftJoin('users', 'users.org_id', '=', 'schools.id') // Keep left join to include directories without users
            ->select(
                'schools.*',
                'directory_types.order as directory_type_order',
                DB::raw('COUNT(users.id) as user_count') // Count users, even if 0
            )
            ->whereRaw("JSON_CONTAINS(schools.directory_type, '16', '$')")
            ->groupBy('schools.id', 'directory_types.order', 'schools.name') // Ensure groupBy includes unique columns
            ->orderBy('directory_types.order', 'asc')
            ->orderBy('schools.order', 'asc')
            ->get()
            ->map(function ($directory) use ($types) {
                $directoryTypes = json_decode($directory->directory_type, true);
                if (!is_array($directoryTypes)) {
                    $directoryTypes = [];
                }
                $directory->directory_type_names = array_map(fn($id) => $types[$id] ?? $id, $directoryTypes);
                return $directory;
            });

        $i = 1;
        return view('admin.directory.schools.index', compact('directories', 'i', 'types'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_create_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $types = DirectoryType::where('status', 1)->get();
        $roles = Role::all();
        $tags = TagsModel::all();
        $whereWeWork = Country::all();
        $whatWeDo = Programs::all();
        $usaStates = USAStates::all();
        $directoryLists = Schools::getDirectoryLists();

        return view('admin.directory.schools.create', compact('types', 'roles', 'directoryLists', 'tags', 'whereWeWork', 'whatWeDo', 'usaStates'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'bail|required',
            'images' => 'bail|required|array',
            'images.*' => 'bail|required|image|mimes:jpeg,png,jpg,gif|max:3048',
            'business_name' => 'bail|required',
            'location' => 'bail|required',
            'directory_type' => 'bail|required|array',
            'tags' => 'bail|required',
            'status' => 'bail|required',
            'prime_status' => 'bail|required',
            'address_type' => 'bail|required',
            'address' => 'required|array|min:1',
            'address.*.street' => 'bail|required_if:address_type,offline|string|max:255',
            'address.*.lat' => 'bail|required_if:address_type,offline|numeric',
            'address.*.long' => 'bail|required_if:address_type,offline|numeric',
            'business_url' => 'bail|required_if:type,onli   ne',
            'contact_informations' => 'bail|required',
            'description' => 'bail|required',
            'personal_email' => 'bail|required|email',
            'join_as_organizer' => 'bail|required',
            'contact_informations.email' => 'bail|required',
            'contact_informations.website' => 'bail|required',
        ], [
            'personal_email.required' => 'The business email field is mandatory.',
            'personal_email.email' => 'Please provide a valid email address for business email.',
        ]);
        $user = Auth::user();
        if ($user->hasRole('Organizer')) {
            $websiteArray = parse_url($request->contact_informations['website']);
            $emailArray = explode('@', $request->personal_email);
            $domain = "";
            if (isset($websiteArray['host']) && !empty($websiteArray['host'])) {
                $domain = str_replace('www.', '', $websiteArray['host']);
            }
            if (
                (isset($emailArray[1]) && !empty($emailArray[1]) &&
                    $emailArray[1] != $domain) || (!isset($emailArray[1]) || empty($emailArray[1]))
            ) {
                return redirect()->back()->withInput()->with('error', 'Your business email doesnt matched with your website');
            }
        }
        $data = $request->all();
        $directoryTypes = $data['directory_type'];
        $data['directory_type'] = $request->has('directory_type') ? array_map('intval', $request->directory_type) : [0];
        if ($request->relatedDirectoryLists) {
            $data['related_directories'] = json_encode($request->relatedDirectoryLists, true);
        }
        if ($request->chaptersLists) {
            $data['chapters'] = json_encode($request->chaptersLists, true);
        }
        if ($request->where_we_work) {
            $data['where_we_work'] = json_encode($request->where_we_work, true);
        }
        if ($request->what_we_do) {
            $data['what_we_do'] = json_encode($request->what_we_do, true);
        }
        if ($request->tags) {
            $data['tags'] = json_encode($request->tags, true);
        }
        if ($request->has('prime_status')) {
            $data['prime_status'] = $request->input('prime_status');
        }
        if ($request->has('donate_and_support')) {
            $data['donate_and_support'] = $request->input('donate_and_support');
        }
        if ($request->has('external_link')) {
            $data['external_link'] = $request->input('external_link');
        }
        $data['gallery'] = [];
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        if ($request->hasFile('logo')) {
            $data['image'] = (new AppHelper)->saveImage($request->file('logo'), true);
        } else {
            $data['image'] = $this->getDirectoryPlaceHolder($request->name);
        }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['security'] = 1;
        $data['business_hours'] = json_encode($request->business_hours);
        $data['address'] = json_encode($request->address);
        $data['social_media'] = json_encode($request->social_media);
        $data['contact_informations'] = json_encode($request->contact_informations);
        $data['show_business_hours'] = $request->has('show_business_hours') ? 1 : 0;
        $data['created_by'] = $user->id;
        $data['work_region'] = $request->work_region;
        $directory = Schools::create($data);
        if (!empty($directoryTypes)) {
            foreach ($directoryTypes as $directoryType) {
                $newLink = new DirectoryTypeLink();
                $newLink->directory_id = $directory->id;
                $newLink->type_id = $directoryType;
                $newLink->save();
            }
        }
        $message = 'Business has added successfully.';
        if ($user->hasRole('Organizer')) {
            $adminUsers = User::with("roles")->whereHas("roles", function ($q) {
                $q->whereIn("name", ["admin"]);
            })->pluck('email')->toArray();
            if (!empty($adminUsers)) {
                Mail::to($adminUsers)->send(new CustomOrgMail('admin', $directory, $user));
            }
            Mail::to($user->email)->send(new CustomOrgMail('organizer', $directory, $user));
            Mail::to($request->personal_email)->send(new CustomOrgMail('organization', $directory, $user));
            $user->status = 0;
            $user->save();
            Auth::logout();
            return redirect('user/login?type=org')->with('error_msg', 'Business has added successfully. Please wait for until review by the team.');
        }
        return redirect()->route('schools.index')->withStatus(__($message));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Schools $school)
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        abort_if(Gate::denies('directory_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($school->directory_type && is_array($school->directory_type) == true) {
            $selectedTypes = $school->directory_type;
        } elseif (is_string($school->directory_type)) {
            $selectedTypes = [intval($school->directory_type)];
        } else {
            $selectedTypes = $school->directory_type ? json_decode($school->directory_type, true) : [];
        }
        $types = DirectoryType::where('status', 1)->get();

        $tags = TagsModel::all();
        $selectedTags = json_decode($school->tags, true);
        $selectedTags = is_array($selectedTags)  ? $selectedTags : [];

        $usaStates = USAStates::all();
        $whereWeWork = Country::all();
        $selectedCountries = json_decode($school->where_we_work, true);
        $selectedCountries = is_array($selectedCountries) ? $selectedCountries : [];

        $workRegion = $school->work_region ?? 'worldwide';

        $whatWeDo = Programs::all();
        $selectedPrograms = json_decode($school->what_we_do, true);
        $selectedPrograms = is_array($selectedPrograms) ? $selectedPrograms : [];

        $business_hours = json_decode($school->business_hours, true);
        if (!empty($business_hours)) {
            foreach ($business_hours as $day => $business_hour) {
                $business_hours[$day]['is_closed'] = $business_hour['is_closed'] ?? "";
                $business_hours[$day]['open_time'] = $business_hour['open_time'] ?? "";
                $business_hours[$day]['close_time'] = $business_hour['close_time'] ?? "";
            }
        }
        $social_media = json_decode($school->social_media, true) ?? [];
        $required_keys = ['youtube', 'linkedin', 'tiktok'];
        foreach ($required_keys as $key) {
            if (!array_key_exists($key, $social_media)) {
                $social_media[$key] = null;
            }
        }

        $contact_info = json_decode($school->contact_informations, true);

        $roles = Role::all();
        $users = User::with("roles")->whereHas("roles", function ($q) use ($school) {
            $q->where("id", [$school->organizer_role]);
        })->get();

        $gallery = [];
        $galleryImages = [];
        if (!empty($school->gallery)) {
            $galleryImages = explode(',', $school->gallery);
        }
        if (!empty($galleryImages)) {
            foreach ($galleryImages as $key => $galleryImage) {
                array_push($gallery, ['id' => $key, 'src' => url('images/upload/' . $galleryImage)]);
            }
        }
        $directoryLists = Schools::getDirectoryLists();
        $selectedDirectories = Schools::returnRelatedDirectoriesAndFormatting($school->id);
        $selectedChapters = Schools::returnChaptersAndFormatting($school->id);
        return view('admin.directory.schools.edit', compact('gallery', 'selectedTypes', 'types', 'users', 'school', 'business_hours', 'social_media', 'contact_info', 'roles', 'tags', 'selectedTags', 'directoryLists', 'selectedDirectories', 'selectedChapters', 'usaStates', 'whereWeWork', 'selectedCountries', 'workRegion', 'whatWeDo', 'selectedPrograms'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Schools $school)
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        $this->validate($request, [
            'name' => 'bail|required',
            'business_name' => 'bail|required',
            'location' => 'bail|required',
            'directory_type' => 'bail|required|array',
            'status' => 'bail|required',
            'prime_status' => 'bail|required',
            'address_type' => 'bail|required',
            'address' => 'bail|required_if:address_type,offline|array|min:1',
            'address.*.street' => 'bail|required_if:address_type,offline|string|max:255',
            'address.*.lat' => 'bail|required_if:address_type,offline|numeric',
            'address.*.long' => 'bail|required_if:address_type,offline|numeric',
            'contact_informations' => 'bail|required',
            'description' => 'bail|required',
            'personal_email' => 'bail|required|email', // Corrected rule format
            'join_as_organizer' => 'bail|required',
            'contact_informations.email' => 'bail|required',
            'contact_informations.website' => 'bail|required',
        ], [
            'personal_email.required' => 'The business email field is mandatory.', // Custom messages
            'personal_email.email' => 'Please provide a valid email address for business email.',
        ]);
        $data = $request->all();

        if ($request->relatedDirectoryLists) {
            $data['related_directories'] = json_encode($request->relatedDirectoryLists);
        }
        if ($request->chaptersLists) {
            $data['chapters'] = json_encode($request->chaptersLists);
        }

        $data['work_region'] = $request->work_region;
        if ($request->where_we_work) {
            $data['where_we_work'] = json_encode(array_values($request->where_we_work));
        } else {
            $data['where_we_work'] = json_encode([]);
        }

        if ($request->what_we_do) {
            $data['what_we_do'] = json_encode(array_values($request->what_we_do));
        } else {
            $data['what_we_do'] = json_encode([]);
        }
        if ($request->tags) {
            $data['tags'] = json_encode(array_values($request->tags));
        } else {
            $data['tags'] = json_encode([]);
        }
        if ($request->has('prime_status')) {
            $data['prime_status'] = $request->input('prime_status');
        }
        if ($request->has('donate_and_support')) {
            $data['donate_and_support'] = $request->input('donate_and_support');
        }
        if ($request->has('external_link')) {
            $data['external_link'] = $request->input('external_link');
        }
        $directoryTypes = $data['directory_type'];
        if ($request->hasFile('logo')) {
            (new AppHelper)->deleteFile($school->image);
            $data['image'] = (new AppHelper)->saveImage($request->file('logo'), true);
        }
        $gallerys = [];
        $gallerys = array_merge($gallerys, (!empty($school->gallery)) ? explode(',', $school->gallery) : []);
        $constructedGallery = [];
        if ($request->has('preloaded')) {
            foreach ($gallerys as $key => $gallery) {
                if (in_array($key, $request->preloaded) !== false) {
                    array_push($constructedGallery, $gallery);
                } else {
                    (new AppHelper)->deleteFile($gallery);
                }
            }
        }
        $data['gallery'] = $constructedGallery;
        if ($request->hasFile('images')) {
            foreach ($request->images as $image) {
                $imageName = (new AppHelper)->saveImage($image, true);
                array_push($data['gallery'], $imageName);
            }
        }
        $data['gallery'] = (!empty($data['gallery'])) ? implode(',', $data['gallery']) : "";
        $data['address'] = json_encode($request->address);
        $data['business_hours'] = json_encode($request->business_hours);
        $data['social_media'] = json_encode($request->social_media);
        $data['contact_informations'] = json_encode($request->contact_informations);
        $data['show_business_hours'] = $request->has('show_business_hours') ? 1 : 0;
        $data['directory_type'] = $request->has('directory_type') ? array_map('intval', $request->directory_type) : [0];

        $school->update($data);
        if (!empty($directoryTypes)) {
            DirectoryTypeLink::where('directory_id', $school->id)->delete();
            foreach ($directoryTypes as $directoryType) {
                $newLink = new DirectoryTypeLink();
                $newLink->directory_id = $school->id;
                $newLink->type_id = $directoryType;
                $newLink->save();
            }
        }
        return redirect()->route('schools.index')->withStatus(__('Business has updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (!Auth::user()->hasRole('admin')) {
            abort(403, 'Invalid Access');
        }
        Schools::find($id)->delete();
        DirectoryTypeLink::where('directory_id', $id)->delete();
        return true;
    }

    // **********Index-Order**********
    public function orderSchoolsProcess(Request $request)
    {
        $this->validate($request, [
            'positions' => 'required|array',
        ]);
        $positions = $request->positions;
        foreach ($positions as $position) {
            if (isset($position['id']) && isset($position['position'])) {
                Schools::where('id', $position['id'])->update(['order' => $position['position']]);
            }
        }
    }
    // **********Index-Order**********

    // **********Classes-Index**********
    public function classesIndex()
    {
        try{
            $classData = SchoolsClasses::with(['school', 'category'])->get();
            return view('admin.directory.schools.classes.index', compact('classData'));
        }catch(Exception $e){
            Log::error('Error loading classes: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error loading classes index');
        }
    }
    // **********Classes-Index**********

    // **********Classes-Index-Order**********
    public function orderClassesProcess(Request $request)
    {
        $this->validate($request, [
            'positions' => 'required|array',
        ]);
        $positions = $request->positions;
        foreach ($positions as $position) {
            if (isset($position['id']) && isset($position['position'])) {
                SchoolsClasses::where('id', $position['id'])->update(['order' => $position['position']]);
            }
        }
    }
    // **********Classes-Index-Order**********

    // **********Classes-Create**********
    public function classesCreate()
    {
        try{
            $schools = Schools::where('status', 1)->get();
            $categories = ClassesCategory::where('status', 1)->get();
            return view('admin.directory.schools.classes.create', compact('schools', 'categories'));
        }catch(Exception $e){
            Log::error('Error loading create: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error loading classes create');
        }
    }
    // **********Classes-Create**********

    // **********Classes-Store**********
    public function classesStore(Request $request)
    {
        $validatedData = $request->validate([
            'class_name'        => 'required|string|max:255',
            'school_id'         => 'required|exists:schools,id',
            'category_id'       => 'required|exists:classes_categories,id',
            'class_start_date'  => 'required|date',
            'class_end_date'    => 'required|date|after_or_equal:class_start_date',
            'class_schedule'    => 'required|string|max:255',
            'class_start_time'  => 'required',
            'class_end_time'    => 'required',
            'age'               => 'required|string',
            'prerequisites'     => 'nullable|string',
            'tution_and_fee'    => 'required|string|max:255',
            'class_format'      => 'required|in:1,2,3',
            'class_status'      => 'required|in:0,1',
        ]);
        $extraFields = $request->input('details', []);
        $validatedData['extra_fields'] = json_encode($extraFields);
        try{
            SchoolsClasses::create($validatedData);
            return redirect()->route('schools-classes')->with('success', 'Class created successfully');
        }catch(Exception $e){
            Log::error('Error storing classes: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error storing classes');
        }
    }
    // **********Classes-Store**********

    // **********Classes-Edit**********
    public function classesEdit($id)
    {
        try {
            $classes = SchoolsClasses::findOrFail($id);
            $schools = Schools::where('status', 1)->get();
            $categories = ClassesCategory::where('status', 1)->get();
            return view('admin.directory.schools.classes.edit', compact('classes', 'schools', 'categories'));
        } catch (Exception $e) {
            Log::error('Error loading edit: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error loading classes edit');
        }
    }
    // **********Classes-Edit**********

    // **********Classes-Update**********
    public function classesUpdate(Request $request, $id)
    {
        try {
            $class = SchoolsClasses::findOrFail($id);
            $validatedData = $request->validate([
                'class_name'        => 'required|string|max:255',
                'school_id'         => 'required|exists:schools,id',
                'category_id'       => 'required|exists:classes_categories,id',
                'class_start_date'  => 'required|date',
                'class_end_date'    => 'required|date|after_or_equal:class_start_date',
                'class_schedule'    => 'required|string|max:255',
                'class_start_time'  => 'required',
                'class_end_time'    => 'required',
                'age'               => 'required|string',
                'prerequisites'     => 'nullable|string',
                'tution_and_fee'    => 'required|string|max:255',
                'class_format'      => 'required|in:1,2,3',
                'class_status'      => 'required|in:0,1',
            ]);
            $extraFields = $request->input('details', []);
            if (!empty($extraFields)) {
                $class->extra_fields = json_encode($extraFields);
            }
            $class->update($validatedData);
            return redirect()->route('schools-classes')->with('success', 'Class updated successfully');
        } catch (Exception $e) {
            Log::error('Error updating classes: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating class');
        }
    }
    // **********Classes-Update**********

    // **********Classes-Delete**********
    public function classesDelete($id)
    {
        try {
            $class = SchoolsClasses::findOrFail($id);
            $class->delete();
            return response()->json(['success' => true, 'message' => 'Programs deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting class: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting class');
        }
    }
    // **********Classes-Delete**********

    // **********Category-Index**********
    public function categoryIndex()
    {
        try{
            $catgeories = ClassesCategory::get();
            return view('admin.directory.schools.category.index', compact('catgeories'));
        }catch(Exception $e){
            Log::error('Error loading category: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error loading category index');
        }
    }
    // **********Category-Index**********

    // **********Category-Create**********
    public function categoryCreate()
    {
        try{
            return view('admin.directory.schools.category.create');
        }catch(Exception $e){
            Log::error('Error loading create: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error loading category create');
        }
    }
    // **********Category-Create**********

    // **********Category-Store**********
    public function categoryStore(Request $request)
    {
        $validatedData = $request->validate([
            'name'        => 'required|string|max:255',
            'status'      => 'required|in:0,1',
        ]);
        try{
            ClassesCategory::create($validatedData);
            return redirect()->route('schools-category')->with('success', 'Category created successfully');
        }catch(Exception $e){
            Log::error('Error storing category: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error storing category');
        }
    }
    // **********Category-Store**********

    // **********Category-Edit**********
    public function categoryEdit($id)
    {
        try {
            $category = ClassesCategory::findOrFail($id);
            return view('admin.directory.schools.category.edit', compact('category'));
        } catch (Exception $e) {
            Log::error('Error loading edit: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error loading category edit');
        }
    }
    // **********Category-Edit**********

    // **********Category-Update**********
    public function categoryUpdate(Request $request, $id)
    {
        try {
            $category = ClassesCategory::findOrFail($id);
            $validatedData = $request->validate([
                'name'        => 'required|string|max:255',
                'status'      => 'required|in:0,1',
            ]);
            $category->update($validatedData);
            return redirect()->route('schools-category')->with('success', 'Category updated successfully');
        } catch (Exception $e) {
            Log::error('Error updating category: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating category');
        }
    }
    // **********Category-Update**********

    // **********Category-Delete**********
    public function categoryDelete($id)
    {
        try {
            $category = ClassesCategory::findOrFail($id);
            $category->delete();
            return response()->json(['success' => true, 'message' => 'Category deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Error deleting category: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting category');
        }
    }
    // **********Category-Delete**********
}
