<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class CheckForMaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    protected $except = [
        'admin/home','admin-setting','maintain',
];
    public function handle(Request $request, Closure $next)
    {

        if (App::isDownForMaintenance())
         {

                if($request->getPathInfo() == '/admin-setting') {

                    return $next($request);
                }else{
                    return redirect('maintain');
                }
            }else{
                return $next($request);
            }

    }
}
